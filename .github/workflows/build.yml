name: Build and Test

on:
    push:
        branches:
            - "**"

jobs:
    build:
        runs-on: ubuntu-latest

        steps:
            - name: Checkout repository
              uses: actions/checkout@v3

            - name: Set up Node.js
              uses: actions/setup-node@v3
              with:
                  node-version: "20.18.3"

            - name: Install dependencies
              run: npm install --legacy-peer-deps

            - name: Run Build
              run: NODE_OPTIONS="--max-old-space-size=4096" npm run build:test
#
# - name: Upload logs if the build fails
#   if: failure()
#   uses: actions/upload-artifact@v3
#   with:
#     name: build-logs
#     path: logs/
