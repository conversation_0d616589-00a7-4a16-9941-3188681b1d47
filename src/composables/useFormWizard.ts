import { ref, onMounted, nextTick, toRaw } from "vue";
import { validateOPDWorkflow } from "@/apps/OPD/services/form_wizard_validation";
import { Service } from "@/services/service";

export const useFormWizard = (title: string) => {
    const currentTabIndex = ref("") as any;

    const onChangeCurrentTab = (index: number) => {
        if (index % 1 === 0) currentTabIndex.value = index;
    };

    const onTabBeforeChange = (index: number) => {
        if (currentTabIndex.value === 0) {
            console.log("First Tab");
        }
        onChangeCurrentTab(index);
        if (Service.getProgramID() == 14) {
            if (validateOPDWorkflow(title)) {
            } else {
                return false;
            }
        }
        console.log("All Tabs");
    };

    const addHeaderAboveList = (customTitle?: string) => {
        const formWizards = document.getElementsByClassName("form-wizard-vue");
        if (!formWizards.length) return;

        const ulElements = formWizards[0].getElementsByClassName("fw-body-list");
        if (!ulElements.length) return;

        const existingTitle = ulElements[0].querySelector(".fw-custom-title");
        if (existingTitle) existingTitle.remove();

        const titleLi = document.createElement("li");
        titleLi.className = "fw-custom-title";
        titleLi.textContent = customTitle || title;
        titleLi.style.cssText = `
            text-align: center;
            font-weight: 700;
            font-size: 18px;
            flex-grow: 0.08;
            padding: 10px 0;
        `;

        ulElements[0].insertBefore(titleLi, ulElements[0].firstChild);
    };

    const changeBtnIconPosition = () => {
        nextTick(() => {
            const button = document.querySelector(".fw-footer-left .fw-btn");

            if (!button) return;

            const span = button.querySelector("span");
            const icon = button.querySelector("i");

            if (!span || !icon) return;

            button.removeChild(span);
            button.removeChild(icon);

            button.appendChild(icon);
            button.appendChild(span);
        });
    };

    // Initialize on component mount
    onMounted(() => {
        addHeaderAboveList();
    });

    // Optional: computed ref if needed
    // const nextButtonOptions = computed(() => {
    //     return currentTabIndex.value === 2
    //         ? {
    //             text: "test",
    //             icon: "check",
    //             hideIcon: true,
    //             hideText: false,
    //             disabled: true,
    //         }
    //         : { disabled: false };
    // });

    return {
        currentTabIndex,
        onChangeCurrentTab,
        onTabBeforeChange,
        changeBtnIconPosition,
        addHeaderAboveList,
    };
};
