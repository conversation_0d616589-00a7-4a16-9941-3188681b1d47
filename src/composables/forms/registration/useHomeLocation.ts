import StandardValidations from "@/validations/StandardValidations";
import { computed, ref } from "vue";
import { useLocation } from "@/composables/useLocation";
import { handleDistrictChange, handleTAChange, isFieldDisabled, createSameAsCurrentHandler } from "@/utils/locationHelpers";
import { icons } from "@/utils/svg";

export const useHomeLocation = (currentLocationFormValues?: any, formRef?: any) => {
    const { getDistricts, districtList, selectedTraditionalAuthorityId, villages, selectedDistrictId, TAs } = useLocation();
    getDistricts();
    const homeLocation = computed(() => [
        {
            componentType: "Heading",
            name: "Home Location",
            position: "center",
        },
        {
            componentType: "checkboxField",
            type: "single",
            name: "same_as_current",
            label: "Same as current",
            onChange: createSameAsCurrentHandler(currentLocationFormValues, formRef),
        },
        {
            componentType: "multiSelectInputField",
            header: "Home district",
            name: "home_district",
            trackBy: "district_id",
            icon: icons.search,
            validation: StandardValidations.required,
            options: districtList.value,
            onChange: (value: any) => handleDistrictChange(value, selectedDistrictId, formRef),
        },
        {
            componentType: "multiSelectInputField",
            header: "Home traditional authority",
            name: "home_traditional_authority",
            trackBy: "traditional_authority_id",
            validation: StandardValidations.required,
            icon: icons.search,
            options: TAs.value,
            disabled: (data: any) => isFieldDisabled(data, TAs.value),
            onChange: (value: any) => handleTAChange(value, selectedTraditionalAuthorityId, formRef),
            openDirection: "auto",
        },
        {
            componentType: "multiSelectInputField",
            header: "Home village",
            name: "home_village",
            trackBy: "village_id",
            icon: icons.search,
            validation: StandardValidations.required,
            options: villages.value,
            disabled: (data: any) => isFieldDisabled(data, villages.value),
            openDirection: "auto",
        },
    ]);

    return {
        homeLocation,
    };
};
