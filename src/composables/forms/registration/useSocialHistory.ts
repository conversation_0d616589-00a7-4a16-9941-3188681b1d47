import { computed, ref } from "vue";
import { FormElement } from "@/interfaces/FormInterface";
import { icons } from "@/utils/svg";
import StandardValidations from "@/validations/StandardValidations";
import { Service } from "@/services/service";
import { GlobalPropertyService } from "@/services/global_property_service";

export const useSocialHistory = () => {
    const isMilitary = ref();
    GlobalPropertyService.isProp("military_site=true").then((res) => {
        isMilitary.value = res;
    });
    const socialHistory = computed(() => {
        return [
            {
                componentType: "Heading",
                name: "Social History",
                position: "center",
            },
            {
                componentType: "multiSelectInputField",
                header: "Religion",
                name: "religion",
                icon: icons.search,
                trackBy: "id",
                options: [
                    { id: 1, name: "Christianity" },
                    { id: 2, name: "Islam" },
                    { id: 3, name: "Judaism" },
                    { id: 4, name: "Hinduism" },
                    { id: 5, name: "Buddhism" },
                    { id: 6, name: "Sikhism" },
                    { id: 7, name: "Jainism" },
                    { id: 8, name: "Bahá'í Faith" },
                    { id: 9, name: "Zoroastrianism" },
                    { id: 10, name: "Confucianism" },
                    { id: 11, name: "Taoism" },
                    { id: 12, name: "Shinto" },
                    { id: 13, name: "Baha'i Faith" },
                    { id: 14, name: "Juche" },
                    { id: 15, name: "Rastafari" },
                ],
            },
            {
                componentType: "radioButtonField",
                header: "Occupation status",
                name: "occupation",
                type: "inline",
                validation: StandardValidations.required,
                condition: () => {
                    return Service.getProgramID() == 1 && isMilitary.value;
                },
                options: [
                    {
                        label: "Military",
                        value: "Military",
                    },
                    {
                        label: "Civilian",
                        value: "Civilian",
                    },
                ],
            },

            {
                componentType: "radioButtonField",
                header: "Occupation status",
                name: "occupation",
                type: "inline",
                condition: (data) => {
                    return !isMilitary;
                },
                options: [
                    {
                        label: "Employed",
                        value: "employed",
                    },
                    {
                        label: "Student",
                        value: "Student",
                    },
                    {
                        label: "Unemployed",
                        value: "unemployed",
                    },
                    {
                        label: "Other",
                        value: "Other",
                    },
                ],
            },
            {
                componentType: "radioButtonField",
                header: "Marital status",
                name: "maritalStatus",
                type: "inline",
                options: [
                    {
                        label: "Single",
                        value: "single",
                    },
                    {
                        label: "Married",
                        value: "married",
                    },
                    {
                        label: "Widow",
                        value: "widow",
                    },
                    {
                        label: "Widower",
                        value: "widower",
                    },
                    {
                        label: "Divorced",
                        value: "divorced",
                    },
                ],
            },
            {
                componentType: "radioButtonField",
                header: "Highest level of education",
                name: "highestLevelOfEducation",
                type: "inline",
                options: [
                    {
                        label: "No education",
                        value: "No education",
                    },
                    {
                        label: "Primary school",
                        value: "primary school",
                    },
                    {
                        label: "Secondary school",
                        value: "secondary school",
                    },
                    {
                        label: "Tertiary education",
                        value: "tertiary education",
                    },
                ],
            },
        ] satisfies FormElement[];
    });
    return {
        socialHistory,
    };
};
