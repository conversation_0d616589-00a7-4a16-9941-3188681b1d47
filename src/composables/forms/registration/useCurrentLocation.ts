import StandardValidations from "@/validations/StandardValidations";
import { computed, ref, watch } from "vue";
import { useLocation } from "@/composables/useLocation";
import { icons } from "@/utils/svg";

export const useCurrentLocation = (formRef?: any) => {
    const isLoadingTAs = ref(false);
    const { getDistricts, districtList, selectedTraditionalAuthorityId, villages, selectedDistrictId, TAs } = useLocation();
    getDistricts();
    const currentLocation = computed(() => {
        return [
            {
                componentType: "Heading",
                name: "Current Location",
                position: "center",
            },
            {
                componentType: "multiSelectInputField",
                header: "Current district",
                name: "current_district",
                trackBy: "district_id",
                validation: (value: string) => {
                    return StandardValidations.required(value);
                },
                icon: icons.search,
                options: districtList.value,
                onChange: (value: any, data: any) => {
                    if (formRef?.value) {
                        formRef.value.setFormValue("current_traditional_authority", []);
                        formRef.value.setFormValue("current_village", []);
                    }
                    selectedDistrictId.value = value?.district_id || null;
                },
            },
            {
                componentType: "multiSelectInputField",
                header: "Current traditional authority",
                name: "current_traditional_authority",
                trackBy: "traditional_authority_id",
                validation: (value: string) => {
                    return StandardValidations.required(value);
                },
                icon: icons.search,
                options: TAs.value,
                disabled: (data: any) => {
                    return !TAs?.value?.length;
                },
                onChange: (value: any, data: any) => {
                    if (formRef?.value) formRef?.value?.setFormValue("current_village", []);
                    selectedTraditionalAuthorityId.value = value?.traditional_authority_id || null;
                },
            },
            {
                componentType: "multiSelectInputField",
                header: "Current village",
                name: "current_village",
                trackBy: "village_id",
                icon: icons.search,
                validation: (value: string) => {
                    return StandardValidations.required(value);
                },
                options: villages.value,
                disabled: (data: any) => {
                    return !villages?.value?.length;
                },
            },
            {
                componentType: "multiSelectInputField",
                header: "Closest landmark/Plot number",
                name: "closestLandmark",
                trackBy: "id",
                icon: icons.search,
                options: [
                    {
                        id: 1,
                        name: "Church",
                    },

                    {
                        id: 2,
                        name: "Mosque",
                    },
                    {
                        id: 3,
                        name: "Primary School",
                    },
                    {
                        id: 4,
                        name: "Borehole",
                    },
                    {
                        id: 5,
                        name: "Secondary School",
                    },
                    {
                        id: 6,
                        name: "College",
                    },
                    {
                        id: 7,
                        name: "Market",
                    },
                    {
                        id: 8,
                        name: "Football Ground",
                    },
                    {
                        id: 9,
                        name: "Other",
                    },
                ],
            },
        ];
    });

    return {
        currentLocation,
        TAs,
        isLoadingTAs,
        selectedDistrictId,
    };
};
