import { computed, ref, watch } from "vue";
import { FormElement } from "@/interfaces/FormInterface";
import { icons } from "@/utils/svg";
import StandardValidations from "@/validations/StandardValidations";
import { RegistrationService } from "@/services/registration_service";
import { useRelationships } from "@/composables/useGuardianRelationship";
export const useGuardianInformation = (personalInformationRef?: any, formRef?: any) => {
    const isChild = computed(() => {
        const data = new RegistrationService().checkIfChild(personalInformationRef.value);
        return data.moreThanThirteenYears;
    });

    const { getRelationships } = useRelationships();
    const relationships = ref();
    watch(personalInformationRef, async (newGender) => {
        if (newGender) {
            relationships.value = await getRelationships(newGender.gender);
        }
    });

    const guardianInformation = computed(() => {
        return [
            {
                componentType: "Heading",
                name: "Guardian Information",
                position: "center",
            },
            {
                componentType: "inputField",
                header: "Guardian National ID",
                name: "guardianNationalID",
                placeholder: "__-__-__-__",
                icon: icons.nationalID,
                validation: (value: string) => {
                    return StandardValidations.isMWNationalID(value);
                },
            },
            {
                componentType: "inputField",
                header: "First name",
                name: "guardianFirstname",
                icon: icons.fullName,
                validation: (value: string) => {
                    return isChild.value ? StandardValidations.isNameEmpty(value) : StandardValidations.isName(value);
                },
            },
            {
                componentType: "inputField",
                header: "Middle name",
                name: "guardianMiddleName",
                icon: icons.fullName,
                validation: (value: string) => {
                    return StandardValidations.isNameEmpty(value);
                },
            },
            {
                componentType: "inputField",
                header: "Last name",
                name: "guardianLastname",
                icon: icons.fullName,
                validation: (value: string) => {
                    return isChild.value ? StandardValidations.isNameEmpty(value) : StandardValidations.isName(value);
                },
            },

            {
                componentType: "phoneInputField",
                name: "guardianPhoneNumber",
                header: "Phone Number",
                validation: (value: any) => {
                    return StandardValidations.isMWPhoneNumber(value.phoneNumber);
                },
            },
            {
                componentType: "multiSelectInputField",
                header: "Relationship to patient",
                name: "relationship",
                trackBy: "trackByID",
                icon: icons.search,
                validation: (value: any) => {
                    return isChild.value ? null : StandardValidations.required(value?.name);
                },
                options: () => {
                    return relationships.value;
                },
                disabled: () => {
                    return !relationships.value;
                },
            },
        ] as FormElement[];
    });
    return {
        guardianInformation,
    };
};
