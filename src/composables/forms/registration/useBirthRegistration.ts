import { FormElement } from "@/components/Forms/interfaces/FormElement";
import { icons } from "@/utils/svg";
import StandardValidations from "@/validations/StandardValidations";
import { computed } from "vue";

export const useBirthRegistration = (formRef: any) => {
    const birthRegistration = computed(() => {
        return [
            {
                componentType: "Heading",
                name: "Birth Registration",
                position: "center",
            },
            {
                componentType: "inputField",
                header: "Birth certificate number",
                name: "Serial Number",
                icon: icons.nationalID,
                placeholder: "__-__-__-__",
            },
            {
                componentType: "inputField",
                header: "Birth Weight/First weight (kg)",
                name: "Weight",
                icon: icons.weight,
                validation: (value: string) => {
                    return StandardValidations.validateWeight(value);
                },
            },
            {
                componentType: "multiSelectInputField",
                header: "How many doses of Tdv did the mother receive?",
                name: "How many doses of Tdv did the mother receive?",
                icon: icons.search,
                options: [
                    {
                        concept_id: 11997,
                        name: "0-2 doses, less than two weeks before delivery",
                    },
                    {
                        concept_id: 11998,
                        name: "2-5 doses more than two weeks of delivery",
                    },
                    {
                        concept_id: 1067,
                        name: "Unknown",
                    },
                ],
                trackBy: "concept_id",
                validation: (value: string) => {
                    return StandardValidations.required(value);
                },
                onChange: (value: any) => {
                    const tdvDose = value?.name;
                    if (tdvDose == "Unknown") {
                        formRef.value.setFormValue("Protected at birth", {
                            concept_id: 1067,
                            name: "Don't know",
                        });
                    } else if (tdvDose == "0-2 doses, less than two weeks before delivery") {
                        formRef.value.setFormValue("Protected at birth", {
                            concept_id: 1066,
                            name: "No",
                        });
                    } else if (tdvDose == "2-5 doses more than two weeks of delivery") {
                        formRef.value.setFormValue("Protected at birth", {
                            concept_id: 1065,
                            name: "Yes",
                        });
                    }
                },
            },
            {
                componentType: "multiSelectInputField",
                header: "Protected at birth (PAB)",
                name: "Protected at birth",
                icon: icons.search,
                trackBy: "concept_id",
                validation: (value: string) => {
                    return StandardValidations.required(value);
                },
                disabled: (data: any) => {
                    return !data["How many doses of Tdv did the mother receive?"];
                },
            },
            {
                componentType: "multiSelectInputField",
                header: "HIV status of the Child's Mother",
                name: "HIV status",
                icon: icons.search,
                options: [
                    {
                        id: 703,
                        name: "Positive",
                    },
                    {
                        id: 664,
                        name: "Negative",
                    },
                    {
                        id: 1067,
                        name: "Unknown",
                    },
                ],
                trackBy: "id",
                validation: (value: string) => {
                    return StandardValidations.required(value);
                },
            },
        ] satisfies Array<FormElement>;
    });
    return {
        birthRegistration,
    };
};
