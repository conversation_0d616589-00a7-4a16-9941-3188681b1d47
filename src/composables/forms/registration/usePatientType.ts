import StandardValidations from "@/validations/StandardValidations";
import { computed, ref } from "vue";
import { useLocation } from "@/composables/useLocation";
import { icons } from "@/utils/svg";

export const usePatientType = () => {
    const { facilityList, getFacilities } = useLocation();
    getFacilities();
    const patientType = computed(() => {
        return [
            {
                componentType: "Heading",
                name: "Patient Type",
                position: "center",
            },

            {
                componentType: "radioButtonField",
                header: "Patient Type",
                name: "patient_type",
                type: "inline",
                validation: StandardValidations.required,
                options: [
                    {
                        label: "New patient",
                        value: "New patient",
                    },
                    {
                        label: "Emergency supply",
                        value: "Emergency supply",
                    },
                    {
                        label: "External consultation",
                        value: "External consultation",
                    },
                ],
            },
            {
                componentType: "multiSelectInputField",
                header: "Facility",
                name: "facility",
                trackBy: "facility_id",
                openDirection: "auto",
                icon: icons.search,
                validation: (value: string) => {
                    return StandardValidations.required(value);
                },
                options: facilityList.value.facilities,
                condition: (data: any) => {
                    return data?.patient_type == "Emergency supply" || data?.patient_type == "External consultation";
                },
            },
        ];
    });
    return {
        patientType,
    };
};
