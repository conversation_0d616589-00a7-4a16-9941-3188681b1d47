import StandardValidations from "@/validations/StandardValidations";
import { computed, ref } from "vue";
import { useLocation } from "@/composables/useLocation";
import { icons } from "@/utils/svg";

export const useCountry = () => {
    const { countriesList, getCountries } = useLocation();
    getCountries();
    const country = computed(() => {
        return [
            {
                componentType: "Heading",
                name: "Country",
                position: "center",
            },
            {
                componentType: "multiSelectInputField",
                header: "Country",
                name: "country",
                trackBy: "district_id",
                openDirection: "auto",
                icon: icons.search,
                validation: (value: string) => {
                    return StandardValidations.required(value);
                },
                options: countriesList.value,
            },
        ];
    });
    return {
        country,
    };
};
