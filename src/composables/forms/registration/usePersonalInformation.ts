import { computed } from "vue";
import { FormElement } from "@/components/Forms/interfaces/FormElement";
import { icons } from "@/utils/svg";
import HisDate from "@/utils/Date";
import StandardValidations from "@/validations/StandardValidations";

export const usePersonalInformation = () => {
    function subtractYearsFromDateString(dateStr: string, years: number): string {
        const date = new Date(dateStr);
        date.setFullYear(date.getFullYear() - years);
        return date.toISOString().split("T")[0];
    }
    const personalInformation = computed(() => {
        return [
            {
                componentType: "Heading",
                name: "Personal Information",
                position: "center",
            },
            {
                componentType: "inputField",
                header: "National ID",
                name: "nationalID",
                placeholder: "__-__-__-__",
                icon: icons.nationalID,
                validation: (value: string) => {
                    return StandardValidations.isMWNationalID(value);
                },
            },

            {
                componentType: "inputField",
                header: "First name",
                name: "firstname",
                icon: icons.fullName,
                validation: (value: string) => {
                    return StandardValidations.isName(value);
                },
            },
            {
                componentType: "inputField",
                header: "Middle name",
                name: "middleName",
                icon: icons.fullName,
                validation: (value: string) => {
                    return StandardValidations.isNamesEmpty(value);
                },
            },

            {
                componentType: "inputField",
                header: "Last name",
                name: "lastname",
                icon: icons.fullName,
                validation: (value: string) => {
                    return StandardValidations.isName(value);
                },
            },
            {
                componentType: "radioButtonField",
                header: "Gender",
                name: "gender",
                type: "inline",
                validation: (value: string) => {
                    return StandardValidations.required(value);
                },
                options: [
                    {
                        label: "Male",
                        value: "M",
                    },
                    {
                        label: "Female",
                        value: "F",
                    },
                ],
            },
            {
                componentType: "dateInputField",
                header: "Date of birth",
                name: "birthdate",
                icon: icons.calenderPrimary,
                minDate: subtractYearsFromDateString(HisDate.sessionDate(), 110),
                validation: (value: string) => {
                    return StandardValidations.required(value);
                },
                condition: (formData: any) => {
                    return !formData.Estimate;
                },
                disabled: (formData: any) => {
                    return formData.Estimate;
                },
            },
            {
                componentType: "inputField",
                header: "Estimated age",
                name: "estimation",
                icon: icons.time,
                validation: (value: boolean) => {
                    return StandardValidations.isEstimationDate(value);
                },
                initialUnit: "Years",
                unitOptions: [
                    { label: "Days", value: "Days" },
                    { label: "Weeks", value: "Weeks" },
                    { label: "Months", value: "Months" },
                    { label: "Years", value: "Years" },
                ],
                unitValidation: (unitValue: string | null) => {
                    if (!unitValue || unitValue === "") {
                        return "Please select a unit.";
                    }
                    return null;
                },
                condition: (formData: any) => {
                    return formData.Estimate;
                },
            },
            {
                componentType: "checkboxField",
                name: "Estimate",
                type: "single",
                label: "Estimate age",
            },

            {
                componentType: "phoneInputField",
                name: "Phone Number",
                header: "Phone Number",
                validation: (value: any) => {
                    return StandardValidations.isMWPhoneNumber(value.phoneNumber);
                },
            },
        ] satisfies FormElement[];
    });
    return {
        personalInformation,
    };
};
