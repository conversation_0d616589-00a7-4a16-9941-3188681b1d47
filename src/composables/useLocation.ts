import { ref, computed, onMounted, watch } from "vue";
import { LocationService } from "@/services/location_service";
import { getOfflineRecords } from "@/services/offline_service";
import { useStatusStore } from "@/stores/StatusStore";
import { Service } from "@/services/service";

export function useLocation() {
    // Reactive data
    const districtList = ref<any>([]);
    const TAs = ref<any[]>([]);
    const villages = ref<any[]>([]);
    const countriesList = ref<any>([]);
    const countryID = ref(null);
    const facilityList = ref<any>([]);
    const locations = ref<any[]>([]);
    const selectedDistrictId = ref(null);
    const selectedTraditionalAuthorityId = ref(null);
    const isLoading = ref(false);

    // Store
    const statusStore = useStatusStore();

    // Computed properties
    const apiStatus = computed(() => statusStore.apiStatus);
    // Watch for district changes and update TAs
    watch(selectedDistrictId, async (newDistrictId) => {
        if (newDistrictId) {
            try {
                TAs.value = await getTAs(newDistrictId);
            } catch (error) {
                TAs.value = [];
            }
        } else {
            TAs.value = [];
        }
    });
    watch(selectedTraditionalAuthorityId, async (newSelectedTraditionalAuthorityId) => {
        if (newSelectedTraditionalAuthorityId) {
            try {
                villages.value = await getVillages(newSelectedTraditionalAuthorityId);
            } catch (error) {
                villages.value = [];
            }
        } else {
            villages.value = [];
        }
    });

    const getVillages = async (targetId: any) => {
        try {
            if (Service.getAPIStatus() && !Service.getModsStatus() && !Service.getUseIndexDBStatus()) {
                return await LocationService.getVillages(targetId);
            } else {
                const offlineVillage: any = await getOfflineRecords("villages", {
                    whereClause: { traditional_authority_id: targetId },
                });
                return offlineVillage;
            }
        } catch (error) {
            console.error("Error getting villages:", error);
            return [];
        }
    };

    const getTAs = async (targetId: any) => {
        try {
            if (Service.getAPIStatus() && !Service.getModsStatus() && !Service.getUseIndexDBStatus()) {
                return await LocationService.getTraditionalAuthorities(targetId);
            } else {
                const offlineTA: any = await getOfflineRecords("TAs", {
                    whereClause: { district_id: targetId },
                });
                return offlineTA;
            }
        } catch (error) {
            console.error("Error getting TAs:", error);
            return [];
        }
    };

    const getDistricts = async () => {
        isLoading.value = true;
        try {
            if (Service.getAPIStatus() && !Service.getModsStatus() && !Service.getUseIndexDBStatus()) {
                for (let i of [1, 2, 3]) {
                    const districts = await LocationService.getDistricts(i);
                    districtList.value.push(...districts);
                }
            } else districtList.value = await getOfflineRecords("districts");

            return districtList.value;
        } catch (error) {
            console.error("Error loading location data:", error);
        } finally {
            isLoading.value = false;
        }
    };

    const getCountries = async () => {
        isLoading.value = true;
        try {
            if (Service.getAPIStatus() && !Service.getModsStatus() && !Service.getUseIndexDBStatus())
                countriesList.value = await LocationService.getDistricts(4);
            else countriesList.value = await getOfflineRecords("countries");

            return countriesList.value;
        } catch (error) {
            console.error("Error loading location data:", error);
        } finally {
            isLoading.value = false;
        }

        // return countriesList.value.filter((obj: any) => obj.region_id === targetId);
    };
    const getCountryID = async (name: string) => {
        let countryData;
        if (Service.getAPIStatus() && !Service.getModsStatus() && !Service.getUseIndexDBStatus())
            countryData = await LocationService.getDistricts(4, name);
        else {
            countryData = await getOfflineRecords("countries", { whereClause: { name: name } });
        }
        countryID.value = countryData[0].district_id;
    };
    const getFacilities = async () => {
        facilityList.value = await LocationService.getAllFacilities();
    };

    // Return reactive state and methods
    return {
        // State
        districtList: districtList,
        countriesList: countriesList,
        locations: locations,
        isLoading: isLoading,
        selectedDistrictId,
        TAs,
        villages,
        selectedTraditionalAuthorityId,
        facilityList,
        countryID,

        // Computed
        apiStatus,

        // Methods
        getVillages,
        getTAs,
        getDistricts,
        getCountries,
        getFacilities,
        getCountryID,
    };
}
