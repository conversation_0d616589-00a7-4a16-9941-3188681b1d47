// composables/useAutoLogout.ts
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { AuthService } from '@/services/auth_service';
import { toastWarning } from '@/utils/Alerts';
import { toastWarningWithButton } from "@/utils/Alerts";

export default function useAutoLogout(timeoutMinutes: number = 30) {
    const router = useRouter();
    const route = useRoute();
    const auth = new AuthService();
    const isActive = ref(true);
    const timeoutId = ref<NodeJS.Timeout | null>(null);
    const warningTimeoutId = ref<NodeJS.Timeout | null>(null);
    
    const TIMEOUT_DURATION = timeoutMinutes * 60 * 1000; // Convert minutes to milliseconds
    const WARNING_DURATION = 5 * 60 * 1000; // 5 minutes warning before logout
    
    // Events that indicate user activity
    const activityEvents = [
        'mousedown',
        'mousemove',
        'keypress',
        'scroll',
        'touchstart',
        'click'
    ];

    // Routes where auto-logout should be disabled
    const excludedRoutes = ['/login', '/register', '/forgot-password'];

    const shouldMonitor = () => {
        const currentPath = route.path;
        
        // Don't monitor if:
        // 1. User is not authenticated
        // 2. Current route is in excluded routes
        return !excludedRoutes.includes(currentPath);
    };

    const resetTimer = () => {
        // Don't reset timer if we shouldn't be monitoring
        if (!shouldMonitor()) {
            return;
        }

        // Clear existing timers
        if (timeoutId.value) {
            clearTimeout(timeoutId.value);
        }
        if (warningTimeoutId.value) {
            clearTimeout(warningTimeoutId.value);
        }

        isActive.value = true;

        // Set warning timer (25 minutes for 30-minute timeout)
        warningTimeoutId.value = setTimeout(() => {
            showWarning();
        }, TIMEOUT_DURATION - WARNING_DURATION);

        // Set logout timer
        timeoutId.value = setTimeout(() => {
            performLogout();
        }, TIMEOUT_DURATION);
    };

    const showWarning = () => {
        if (shouldMonitor()) {
            toastWarningWithButton(`You will be logged out in 5 minutes due to inactivity`, 'Click here reset timer', startMonitoring, 10000, 'top-left');
            // toastWarning(`You will be logged out in 5 minutes due to inactivity`, 10000, 'top-left');
        }
    };

    const performLogout = () => {
        // Only perform logout if we should be monitoring
        if (!shouldMonitor()) {
            return;
        }

        isActive.value = false;
        router.push("/login");
        
        toastWarning('You have been logged out due to inactivity', 10000, 'top-left');
    };

    const startMonitoring = () => {
        // Only start if we should be monitoring
        if (!shouldMonitor()) {
            return;
        }

        resetTimer();
        
        // Add event listeners for user activity
        activityEvents.forEach(event => {
            document.addEventListener(event, resetTimer, true);
        });
    };

    const stopMonitoring = () => {
        // Remove event listeners
        activityEvents.forEach(event => {
            document.removeEventListener(event, resetTimer, true);
        });
        
        // Clear timers
        if (timeoutId.value) {
            clearTimeout(timeoutId.value);
            timeoutId.value = null;
        }
        if (warningTimeoutId.value) {
            clearTimeout(warningTimeoutId.value);
            warningTimeoutId.value = null;
        }
    };

    const extendSession = () => {
        if (shouldMonitor()) {
            resetTimer();
            toastWarning('Session extended');
        }
    };

    // Watch for route changes to start/stop monitoring accordingly
    watch(() => route.path, (newPath) => {
        if (excludedRoutes.includes(newPath)) {
            stopMonitoring();
        }
    });

    onMounted(() => {
        startMonitoring();
    });

    onUnmounted(() => {
        stopMonitoring();
    });

    return {
        isActive,
        startMonitoring,
        stopMonitoring,
        extendSession,
        resetTimer
    };
}