import { ref, computed, type Ref } from "vue";
import type StandardForm from "@/components/Forms/StandardForm.vue";

export function useExposeFromStandardForm() {
    const formRef = ref<InstanceType<typeof StandardForm> | null>(null);

    const currentFormValues = computed(() => {
        return formRef.value?.formValues || {};
    });

    const validateForm = () => formRef.value?.validateForm();
    const getFormValues = () => formRef.value?.getFormValues();
    const setFormValue = (fieldName: string, value: any) => formRef.value?.setFormValue(fieldName, value);
    const resetForm = () => formRef.value?.resetForm();

    const exposeFormMethods = () => ({
        currentFormValues,
        validateForm,
        getFormValues,
        resetForm,
        setFormValue,
    });

    return {
        formRef,
        currentFormValues,
        validateForm,
        getFormValues,
        resetForm,
        exposeFormMethods,
        setFormValue,
    };
}
