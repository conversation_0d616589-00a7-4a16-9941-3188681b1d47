import { ref, onMounted, nextTick, toRaw } from "vue";

export const useFormWizard = (
    title: string,
    options?: {
        onTabChange?: (newIndex: number, oldIndex: number) => Promise<void> | void;
        tabValidations?: Record<number, () => Promise<boolean> | boolean>;
    }
) => {
    const currentTabIndex = ref("") as any;

    const onChangeCurrentTab = async (index: number, oldIndex: number) => {
        if (index % 1 === 0) {
            // Call custom tab change handler if provided
            if (options?.onTabChange) {
                try {
                    await options.onTabChange(index, oldIndex);
                } catch (error) {
                    console.error("Error in custom tab change handler:", error);
                    // Optionally prevent tab change on error
                    return;
                }
            }
            currentTabIndex.value = index;
        }
    };

    const onTabBeforeChange = async (oldIndex: number, newIndex: number) => {
        if (currentTabIndex.value === 0) {
            console.log("First Tab");
        }

        // Run tab-specific validation if provided
        if (options?.tabValidations?.[oldIndex]) {
            try {
                const isValid = await options.tabValidations[oldIndex]();
                if (!isValid) {
                    console.log("Tab validation failed, preventing navigation");
                    return false; // Prevent tab change
                }
            } catch (error) {
                console.error("Error in tab validation:", error);
                return false; // Prevent tab change on error
            }
        }

        console.log("All Tabs");
        return true; // Allow tab change
    };

    const addHeaderAboveList = (customTitle?: string) => {
        const formWizards = document.getElementsByClassName("form-wizard-vue");
        if (!formWizards.length) return;

        const ulElements = formWizards[0].getElementsByClassName("fw-body-list");
        if (!ulElements.length) return;

        const existingTitle = ulElements[0].querySelector(".fw-custom-title");
        if (existingTitle) existingTitle.remove();

        const titleLi = document.createElement("li");
        titleLi.className = "fw-custom-title";
        titleLi.textContent = customTitle || title;
        titleLi.style.cssText = `
            text-align: center;
            font-weight: 700;
            font-size: 18px;
            flex-grow: 0.08;
            padding: 10px 0;
        `;

        ulElements[0].insertBefore(titleLi, ulElements[0].firstChild);
    };

    const changeBtnIconPosition = () => {
        nextTick(() => {
            const button = document.querySelector(".fw-footer-left .fw-btn");

            if (!button) return;

            const span = button.querySelector("span");
            const icon = button.querySelector("i");

            if (!span || !icon) return;

            button.removeChild(span);
            button.removeChild(icon);

            button.appendChild(icon);
            button.appendChild(span);
        });
    };

    // Initialize on component mount
    onMounted(() => {
        addHeaderAboveList();
    });

    return {
        currentTabIndex,
        onChangeCurrentTab,
        onTabBeforeChange,
        changeBtnIconPosition,
        addHeaderAboveList,
    };
};
