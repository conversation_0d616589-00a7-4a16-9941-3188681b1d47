import { useDemographicsStore } from "@/stores/DemographicStore";
import { storeToRefs } from "pinia";
import { ref, computed, onMounted, onUnmounted } from "vue";
import HisDate from "@/utils/Date";

export function useRegistrationPagination() {
    const currentPage = ref(1);
    const columnsPerRow = ref(4);
    const totalComponents = 4;
    const demographicsStore = useDemographicsStore();
    const { patient } = storeToRefs(demographicsStore) as any;
    const getColumnsPerRow = () => {
        const width = window.innerWidth;
        if (width >= 992) {
            return 4;
        } else if (width >= 768) {
            return 3;
        } else if (width >= 576) {
            return 2;
        } else {
            return 1;
        }
    };

    const totalPages = computed(() => {
        if (columnsPerRow.value === 0) return totalComponents;
        return Math.ceil(totalComponents / columnsPerRow.value);
    });

    const showPaginationButtons = computed(() => {
        return totalPages.value > 1;
    });

    const showPreviousButton = computed(() => {
        return showPaginationButtons.value && currentPage.value > 1;
    });

    const showNextButton = computed(() => {
        return showPaginationButtons.value && currentPage.value < totalPages.value;
    });

    const isCardVisible = (cardIndex: number) => {
        if (totalPages.value === 1) {
            return true;
        }
        const startIndex = (currentPage.value - 1) * columnsPerRow.value;
        const endIndex = startIndex + columnsPerRow.value - 1;
        return cardIndex >= startIndex && cardIndex <= endIndex;
    };

    const nextPage = () => {
        if (currentPage.value < totalPages.value) {
            currentPage.value++;
        }
    };

    const prevPage = () => {
        if (currentPage.value > 1) {
            currentPage.value--;
        }
    };

    const goToPage = (pageNumber: number) => {
        if (pageNumber >= 1 && pageNumber <= totalPages.value) {
            currentPage.value = pageNumber;
        }
    };

    const handleResize = () => {
        columnsPerRow.value = getColumnsPerRow();
        if (currentPage.value > totalPages.value) {
            currentPage.value = totalPages.value;
        }
        if (currentPage.value === 0 && totalPages.value > 0) {
            currentPage.value = 1;
        }
    };

    onMounted(() => {
        window.addEventListener("resize", handleResize);
        handleResize();
    });

    onUnmounted(() => {
        window.removeEventListener("resize", handleResize);
    });
    return {
        // State
        currentPage,
        columnsPerRow,

        // Computed
        totalPages,
        showPaginationButtons,
        showPreviousButton,
        showNextButton,

        // Methods
        nextPage,
        prevPage,
        goToPage,
        handleResize,
        isCardVisible,
    };
}
