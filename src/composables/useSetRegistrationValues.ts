import { useDemographicsStore } from "@/stores/DemographicStore";
import { storeToRefs } from "pinia";
import HisDate from "@/utils/Date";

export function useSetRegistrationValues() {
    const demographicsStore = useDemographicsStore();
    const { patient } = storeToRefs(demographicsStore) as any;

    const setFormValues = (allFormRef: any) => {
        const { personalInformationRef, socialHistoryRef, countryRef, currentLocationRef, homeLocationRef, guardianInformationRef } =
            allFormRef.value;
        const personInformation = patient?.value?.personInformation;

        personalInformationRef?.setFormValue("nationalID", patient?.otherPersonInformation?.nationalID);
        personalInformationRef?.setFormValue("firstname", personInformation?.given_name);
        personalInformationRef?.setFormValue("middleName", personInformation?.middle_name);
        personalInformationRef?.setFormValue("lastname", personInformation?.family_name);
        personalInformationRef?.setFormValue("gender", personInformation?.gender);
        personalInformationRef?.setFormValue(
            "birthdate",
            personInformation?.birthdate ? HisDate.toStandardHisDisplayFormat(personInformation?.birthdate) : ""
        );
        personalInformationRef?.setFormValue("Phone Number", personInformation?.cell_phone_number ? personInformation?.cell_phone_number : "");
        homeLocationRef?.setFormValue("home_district", { name: personInformation?.home_district });
        homeLocationRef?.setFormValue("home_traditional_authority", { name: personInformation?.home_traditional_authority });
        homeLocationRef?.setFormValue("home_village", { name: personInformation?.home_village });
        currentLocationRef?.setFormValue("current_district", { name: personInformation?.current_district });
        currentLocationRef?.setFormValue("current_traditional_authority", { name: personInformation?.current_traditional_authority });
        currentLocationRef?.setFormValue("current_village", { name: personInformation?.current_village });
        currentLocationRef?.setFormValue("closestLandmark", { name: personInformation?.landmark });
        countryRef?.setFormValue("country", personInformation?.country ? { name: personInformation?.country } : { name: "Malawi" });
        socialHistoryRef?.setFormValue("religion", { name: personInformation?.religion });
        socialHistoryRef?.setFormValue("occupation", personInformation?.occupation);
        socialHistoryRef?.setFormValue("maritalStatus", personInformation?.marital_status);
        socialHistoryRef?.setFormValue("highestLevelOfEducation", personInformation?.education_level);
        setGuardianInformation(guardianInformationRef);
    };
    const setGuardianInformation = (guardianInformationRef: any) => {
        const guardianInformation = patient?.value?.guardianInformation?.saved[0];
        const relationship = guardianInformation?.relationship_type?.b_is_to_a
            ? {
                  name: guardianInformation.relationship_type.b_is_to_a,
                  id: guardianInformation.relationship_type.relationship_type_id,
                  trackByID: guardianInformation.relationship_type.relationship_type_id + guardianInformation.relationship_type.b_is_to_a,
              }
            : "";
        guardianInformationRef?.setFormValue("guardianNationalID", guardianInformation?.national_id || "");
        guardianInformationRef?.setFormValue("guardianFirstname", guardianInformation?.given_name || "");
        guardianInformationRef?.setFormValue("guardianMiddleName", guardianInformation?.middle_name || "");
        guardianInformationRef?.setFormValue("guardianLastname", guardianInformation?.family_name || "");
        guardianInformationRef?.setFormValue(
            "guardianPhoneNumber",
            guardianInformation?.cell_phone_number ? guardianInformation?.cell_phone_number : ""
        );
        guardianInformationRef?.setFormValue("relationship", relationship || "");
    };

    return {
        setFormValues,
    };
}
