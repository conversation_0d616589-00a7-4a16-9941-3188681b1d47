// src/composables/

import { ref, type Ref } from "vue";

// Re-define interfaces to ensure they are available in this file context
interface PatientDemographics {
    given_name: string;
    middle_name: string;
    family_name: string;
    gender: string;
    birthdate: string;
    birthdate_estimated: boolean;
    home_region: string;
    home_district: string;
    home_traditional_authority: string;
    home_village: string;
    current_region: string;
    current_district: string;
    current_traditional_authority: string;
    current_village: string;
    country: string;
    landmark: string;
    cell_phone_number: string;
    occupation: string;
    marital_status: string;
    religion: string;
    education_level: string;
}

interface Patient {
    id: string;
    patientID: string;
    message: string;
    timestamp: string;
    createdAt: string;
    updatedAt: string;
    data: any;
}

interface DuplicateMatchResultResponse {
    isPossibleDuplicate: boolean;
    bestMatchScore: number;
    bestMatchPatient: Patient | null;
    potentialMatches: Array<{ patient: Patient; score: number }>;
}

// Define the type for the data passed to the modal
interface ModalData {
    originalPatientInfo: PatientDemographics;
    duplicateResult: DuplicateMatchResultResponse;
}

// Global reactive state for the modal
const showModal: Ref<boolean> = ref(false);
const modalData: Ref<ModalData | null> = ref(null);

// Internal variables to hold the promise's resolve/reject functions
// These will be set when the modal is opened and called when the modal emits a decision
let resolvePromise: ((value: "proceed" | "cancel") => void) | null = null;
let rejectPromise: ((reason?: any) => void) | null = null;

/**
 * Composable to manage a global modal's state and interaction.
 * Provides a way to open the modal and wait for a user's decision.
 */
export function useModal() {
    /**
     * Opens the modal with the given data and returns a Promise that
     * resolves with the user's decision ('proceed' or 'cancel').
     * @param data The data to pass to the modal (original input and duplicate check result).
     * @returns A Promise that resolves with 'proceed' or 'cancel'.
     */
    const openModal = (data: ModalData): Promise<"proceed" | "cancel"> => {
        return new Promise((resolve, reject) => {
            // Set the resolve/reject functions for this specific promise instance
            resolvePromise = resolve;
            rejectPromise = reject;

            // Set the modal data and show the modal
            modalData.value = data;
            showModal.value = true;
        });
    };

    /**
     * Function called when the modal emits a decision.
     * This resolves the Promise returned by `openModal`.
     * @param decision 'proceed' or 'cancel'.
     */
    const handleModalDecision = (decision: "proceed" | "cancel") => {
        if (resolvePromise) {
            resolvePromise(decision);
            // Reset after resolution
            resolvePromise = null;
            rejectPromise = null;
        }
        // Hide the modal regardless
        showModal.value = false;
        modalData.value = null; // Clear data
    };

    /**
     * Function called to update the modal's show state directly (e.g., via v-model).
     * @param value The new boolean value for show.
     */
    const updateShow = (value: boolean) => {
        showModal.value = value;
        // If modal is being explicitly closed by v-model binding,
        // and a promise is still pending, reject it (or resolve as 'cancel').
        // Here, we'll assume external close means cancellation.
        if (!value && resolvePromise) {
            handleModalDecision("cancel"); // Force resolve as cancel
        }
    };

    return {
        // Expose reactive state for the App.vue or parent component to bind to
        showModal,
        modalData,
        // Expose function to open the modal (returns a Promise)
        openModal,
        // Expose handler for modal's decision emit (for App.vue binding)
        handleModalDecision,
        updateShow, // For v-model binding
    };
}

// Make the useModal composable available globally for easier import
// In your main.ts or App.vue, you would use it once to set up the global modal instance.
