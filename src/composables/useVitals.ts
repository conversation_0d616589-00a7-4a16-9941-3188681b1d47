import { ref, computed, watch } from "vue";
import { iconBloodPressure } from "@/utils/SvgDynamicColor";
import { BMIService } from "@/services/bmi_service";
import { useDemographicsStore } from "@/stores/DemographicStore";
import HisDate from "@/utils/Date";
import { PatientService } from "@/services/patient_service";
import Validation from "@/validations/StandardValidations";
import { ObservationService } from "@/services/observation_service";
import { modifyFieldValue, modifyCheckboxValue, modifyAlertsValue } from "@/services/data_helpers";
import { useVitalsStore } from "@/apps/NCD/stores/VitalsStore";
import { vi } from "date-fns/locale";
import dayjs from "dayjs";
import { getOfflineFirstObsValue } from "@/services/offline_service";
import { Service } from "@/services/service";

export function useVitals() {
    // Reactive state
    const BMI = ref<any>({});
    const BPStatus = ref<any>({});
    let vitalsData = ref<any>({});

    // Store
    const demographicsStore = useDemographicsStore();
    const patient = computed(() => demographicsStore.patient);

    const vitalsStore = useVitalsStore();
    vitalsData = computed(() => {
        return vitalsStore.vitals;
    });
    const checkHeightServer = async () => {
        const recentHeight: any = await ObservationService.getFirstValueNumber(patient.value.patientID, "Height (cm)");
        const obs_datetime = await ObservationService.getFirstObsDatetime(patient.value.patientID, "Height (cm)");
        const patientService = new PatientService();

        if (recentHeight) {
            const patientAgeAtPrevRecordedHeight = dayjs(obs_datetime).diff(patientService.getBirthdate(), "year");
            /**
             * For a scenario where a patient's height was last updated when they were a minor
             * and they return as an adult, provide an option to update their height.
             */
            if (!(patientAgeAtPrevRecordedHeight < 18 || patientService.getAge() < 18)) {
                return recentHeight;
            }
        }
    };
    const checkHeight = async () => {
        let recentHeight;
        let obs_datetime;
        let patientService;
        if (Service.getModsStatus() && Service.getUseIndexDBStatus()) {
            recentHeight = await ObservationService.getFirstValueNumber(patient.value.patientID, "Height (cm)");
            obs_datetime = await ObservationService.getFirstObsDatetime(patient.value.patientID, "Height (cm)");
            patientService = new PatientService();
        } else {
            const vitals = [...(patient.value?.vitals?.saved || []), ...(patient.value?.vitals?.unsaved || [])];
            recentHeight = await getOfflineFirstObsValue(vitals, "value_numeric", 5090);
            obs_datetime = await getOfflineFirstObsValue(vitals, "obs_datetime", 5090);
            patientService = new PatientService();
        }

        if (recentHeight) {
            const patientAgeAtPrevRecordedHeight = dayjs(obs_datetime).diff(patientService.getBirthdate(), "year");
            /**
             * For a scenario where a patient's height was last updated when they were a minor
             * and they return as an adult, provide an option to update their height.
             */
            if (!(patientAgeAtPrevRecordedHeight < 18 || patientService.getAge() < 18)) {
                return recentHeight;
            }
        }
    };
    /**
     * Set today's vitals data
     */
    const setTodayVitals = async () => {
        const array = ["Height (cm)", "Weight", "Systolic", "Diastolic", "Temperature", "Pulse", "SAO2", "Respiratory rate"];
        const age = HisDate.getAgeInYears(patient.value?.personInformation?.birthdate);

        const promises = array.map(async (item: string) => {
            const firstDate = await ObservationService.getFirstObsDatetime(patient.value.patientID, item);

            if (firstDate && HisDate.toStandardHisFormat(firstDate) == HisDate.sessionDate()) {
                // Hide checkboxes for items that have today's data
                if (item == "Weight") {
                    modifyCheckboxValue(vitalsData.value, "Height And Weight Not Done", "displayNone", true);
                }
                if (item == "Systolic") {
                    modifyCheckboxValue(vitalsData.value, "Blood Pressure Not Done", "displayNone", true);
                }
                if (item == "Pulse") {
                    modifyCheckboxValue(vitalsData.value, "Pulse Rate Not Done", "displayNone", true);
                }
                if (item == "Respiratory rate") {
                    modifyCheckboxValue(vitalsData.value, "Respiratory rate Not Done", "displayNone", true);
                }

                // Set the value and disable the field
                modifyFieldValue(
                    vitalsData.value,
                    item,
                    "value",
                    await ObservationService.getFirstValueNumber(patient.value.patientID, item, HisDate.sessionDate())
                );
                modifyFieldValue(vitalsData.value, item, "disabled", true);
            } else {
                modifyFieldValue(vitalsData.value, item, "value", "");
            }

            // Special handling for respiratory rate in children
            if (item === "Respiratory rate" && age <= 5) {
                modifyFieldValue(vitalsData.value, item, "required", true);
                modifyFieldValue(vitalsData.value, item, "inputHeader", "Respiratory rate*");
            }
        });

        await Promise.all(promises);
    };

    /**
     * Calculate and set BMI
     */
    const setBMI = async (height: any, weight: any) => {
        if (
            patient.value?.personInformation?.gender &&
            patient.value?.personInformation?.birthdate &&
            Validation.vitalsHeight(height) == null &&
            Validation.vitalsWeight(weight) == null
        ) {
            BMI.value = await BMIService.getBMI(
                parseInt(weight),
                parseInt(height),
                patient.value?.personInformation?.gender,
                HisDate.calculateAge(patient.value?.personInformation?.birthdate, HisDate.sessionDate())
            );
        } else {
            BMI.value = {};
        }
        return await updateBMI();
    };

    /**
     * Update BMI display
     */
    const updateBMI = async () => {
        if (!vitalsData.value[0]) return;

        const bmiColor = BMI.value?.color ?? [];
        return updateRate(
            "bmi",
            "BMI " + (BMI.value?.index ?? ""),
            "",
            { colors: bmiColor, value: BMI.value?.result ?? "" },
            BMIService.iconBMI(bmiColor)
        );
    };

    /**
     * Update blood pressure status
     */
    const updateBP = async (systolic: any, diastolic: any) => {
        BPStatus.value = getBloodPressureStatus(systolic, diastolic);

        if (!(Validation.vitalsSystolic(systolic) == null && Validation.vitalsDiastolic(diastolic) == null)) {
            BPStatus.value = {};
        }

        const bpColor = BPStatus.value?.colors ?? [];
        return updateRate(
            "bp",
            systolic + "/" + diastolic,
            "mmHg",
            { colors: bpColor, value: BPStatus.value?.value ?? "" },
            iconBloodPressure(bpColor)
        );
    };

    /**
     * Generic method to update vital sign rates
     */
    const updateRate = async (name: string, value: any, units: string, obj: any, icon: any = "") => {
        if (!value) return;

        const index = value + " " + units;
        const bpColor = obj?.colors ?? [];

        return {
            icon: icon || "",
            textColor: bpColor[1],
            index: index,
            backgroundColor: bpColor[0],
            value: obj?.value ?? "",
        };
    };

    /**
     * Get blood pressure status based on age-specific ranges
     */
    const getBloodPressureStatus = (systolic: any, diastolic: any) => {
        if (systolic && diastolic) {
            let ageGroup: string;
            let minSystolic: number;
            let maxSystolic: number;
            let minDiastolic: number;
            let maxDiastolic: number;

            const patientService = new PatientService();
            const age = patientService.getAge();

            // Determine age group and corresponding normal ranges
            if (age < 1) {
                ageGroup = "less than 1 year";
                minSystolic = 75;
                maxSystolic = 100;
                minDiastolic = 50;
                maxDiastolic = 70;
            } else if (age >= 1 && age < 6) {
                ageGroup = "1-5 years";
                minSystolic = 80;
                maxSystolic = 110;
                minDiastolic = 50;
                maxDiastolic = 80;
            } else if (age >= 6 && age < 13) {
                ageGroup = "6-13 years";
                minSystolic = 85;
                maxSystolic = 120;
                minDiastolic = 55;
                maxDiastolic = 80;
            } else if (age >= 13 && age < 18) {
                ageGroup = "13-18 years";
                minSystolic = 95;
                maxSystolic = 140;
                minDiastolic = 60;
                maxDiastolic = 90;
            } else {
                ageGroup = "above 18 years";
                minSystolic = 100;
                maxSystolic = 130;
                minDiastolic = 60;
                maxDiastolic = 90;
            }

            // Evaluate blood pressure status
            if (systolic < minSystolic && diastolic < minDiastolic) {
                return { colors: ["#B9E6FE", "#026AA2", "#9ADBFE"], value: "Low BP " };
            } else if (systolic >= minSystolic && systolic <= maxSystolic && diastolic >= minDiastolic && diastolic <= maxDiastolic) {
                return { colors: ["#DDEEDD", "#016302", "#BBDDBC"], value: "Normal BP " };
            } else if (systolic > 140 && diastolic > 90) {
                return { colors: ["#FECDCA", "#B42318", "#FDA19B"], value: "High BP" };
            } else {
                // Use systolic only when diastolic is not in normal range
                if (systolic < minSystolic) {
                    return { colors: ["#B9E6FE", "#026AA2", "#9ADBFE"], value: "Low BP (Using Systolic Only)" };
                } else if (systolic >= minSystolic && systolic <= maxSystolic) {
                    return { colors: ["#DDEEDD", "#016302", "#BBDDBC"], value: "Normal BP (Using Systolic Only)" };
                } else {
                    return { colors: ["#FECDCA", "#B42318", "#FDA19B"], value: "High BP (Using Systolic Only)" };
                }
            }
        }
        return {};
    };

    /**
     * Get temperature status based on age-specific ranges
     */
    const getTemperatureStatus = (value: any) => {
        if (value && Validation.vitalsTemperature(value) == null) {
            let ageGroup: string;
            let minTemp: number;
            let maxTemp: number;

            const patientService = new PatientService();
            const age = patientService.getAge();

            // Determine age group and corresponding normal ranges (Axillary)
            if (age <= 1) {
                ageGroup = "(less than 1 year)";
                minTemp = 35.5;
                maxTemp = 37.4;
            } else if (age >= 1 && age <= 18) {
                ageGroup = "(1-18 years)";
                minTemp = 35.5;
                maxTemp = 37.4;
            } else if (age >= 19 && age <= 64) {
                ageGroup = "(above 18 years)";
                minTemp = 35.5;
                maxTemp = 37.4;
            } else if (age >= 65) {
                ageGroup = "(above 18 years)";
                minTemp = 35.5;
                maxTemp = 37.4;
            } else {
                return {};
            }

            if (value < minTemp) {
                return { colors: ["#B9E6FE", "#026AA2", "#9ADBFE"], value: "Low Temperature " };
            } else if (value >= minTemp && value <= maxTemp) {
                return { colors: ["#DDEEDD", "#016302", "#BBDDBC"], value: "Normal Temperature " };
            } else if (value > maxTemp) {
                return { colors: ["#FECDCA", "#B42318", "#FDA19B"], value: "High Temperature " };
            }
        }
        return {};
    };

    /**
     * Get pulse rate status based on age-specific ranges
     */
    const getPulseRateStatus = (value: any) => {
        if (Validation.vitalsPulseRate(value) == null) {
            let ageGroup: string;
            let minPulse: number;
            let maxPulse: number;

            const patientService = new PatientService();
            const age = patientService.getAge();

            // Determine age group and corresponding normal ranges
            if (age <= 0.25) {
                ageGroup = "(0-3 month)";
                minPulse = 100;
                maxPulse = 160;
            } else if (age >= 0.25 && age <= 1) {
                ageGroup = "(1-12 months)";
                minPulse = 80;
                maxPulse = 120;
            } else if (age >= 1 && age <= 2) {
                ageGroup = "(1-2 years)";
                minPulse = 80;
                maxPulse = 120;
            } else if (age >= 6 && age <= 12) {
                ageGroup = "(6-12 years)";
                minPulse = 70;
                maxPulse = 100;
            } else if (age >= 13 && age <= 18) {
                ageGroup = "(13-18 years)";
                minPulse = 55;
                maxPulse = 90;
            } else if (age >= 18) {
                ageGroup = "(Above 10 years)";
                minPulse = 60;
                maxPulse = 100;
            } else {
                return {};
            }

            if (value < minPulse) {
                return { colors: ["#B9E6FE", "#026AA2", "#9ADBFE"], value: "Low Pulse Rate " };
            } else if (value >= minPulse && value <= maxPulse) {
                return { colors: ["#DDEEDD", "#016302", "#BBDDBC"], value: "Normal Pulse Rate " };
            } else if (value > maxPulse) {
                return { colors: ["#FECDCA", "#B42318", "#FDA19B"], value: "High Pulse Rate " };
            }
        }
        return {};
    };

    /**
     * Get oxygen saturation status
     */
    const getOxygenSaturationStatus = (value: any) => {
        if (value && Validation.vitalsOxygenSaturation(value) == null) {
            const minOxygenSaturation = 95;
            const maxOxygenSaturation = 100;

            if (value < minOxygenSaturation) {
                return { colors: ["#B9E6FE", "#026AA2", "#9ADBFE"], value: "Low oxygen saturation" };
            } else if (value >= minOxygenSaturation && value <= maxOxygenSaturation) {
                return { colors: ["#DDEEDD", "#016302", "#BBDDBC"], value: "Normal oxygen saturation" };
            }
        }
        return {};
    };

    /**
     * Get respiratory rate status based on age-specific ranges
     */
    const getRespiratoryRateStatus = (value: any) => {
        if (Validation.vitalsRespiratoryRate(value) == null) {
            let ageGroup: string;
            let minRespiratoryRate: number;
            let maxRespiratoryRate: number;

            const patientService = new PatientService();
            const age = patientService.getAge();

            // Determine age group and corresponding normal ranges
            if (age <= 1) {
                ageGroup = "(0-1 year)";
                minRespiratoryRate = 30;
                maxRespiratoryRate = 60;
            } else if (age >= 1 && age < 3) {
                ageGroup = "(1-3 years)";
                minRespiratoryRate = 24;
                maxRespiratoryRate = 40;
            } else if (age >= 3 && age <= 6) {
                ageGroup = "(3-6 years)";
                minRespiratoryRate = 22;
                maxRespiratoryRate = 34;
            } else if (age >= 6 && age <= 12) {
                ageGroup = "(6-12 years)";
                minRespiratoryRate = 18;
                maxRespiratoryRate = 30;
            } else if (age >= 12 && age <= 18) {
                ageGroup = "(12-18 years)";
                minRespiratoryRate = 12;
                maxRespiratoryRate = 16;
            } else if (age >= 19) {
                ageGroup = "(Above 19 years)";
                minRespiratoryRate = 12;
                maxRespiratoryRate = 20;
            } else {
                return {};
            }

            if (value < minRespiratoryRate) {
                return { colors: ["#B9E6FE", "#026AA2", "#9ADBFE"], value: "Low respiratory rate" };
            } else if (value >= minRespiratoryRate && value <= maxRespiratoryRate) {
                return { colors: ["#DDEEDD", "#016302", "#BBDDBC"], value: "Normal respiratory rate" };
            } else if (value > maxRespiratoryRate) {
                return { colors: ["#FECDCA", "#B42318", "#FDA19B"], value: "High respiratory rate" };
            }
        }
        return {};
    };

    return {
        // Reactive state
        BMI,
        BPStatus,
        vitalsData,
        patient,

        // Methods
        setTodayVitals,
        setBMI,
        updateBMI,
        updateBP,
        updateRate,
        getBloodPressureStatus,
        getTemperatureStatus,
        getPulseRateStatus,
        getOxygenSaturationStatus,
        getRespiratoryRateStatus,
        checkHeight,
        checkHeightServer,
    };
}
