import { ref, computed, watch, onMounted } from "vue";
import { useRoute } from "vue-router";
import { useStatusStore } from "@/stores/StatusStore";
import { RelationsService } from "@/services/relations_service";
import { getOfflineRecords } from "@/services/offline_service";
import { Service } from "@/services/service";

export function useRelationships() {
    // Reactive data
    const relationships = ref<any[]>([]);
    const relationshipsData = ref<any>([]);
    const filteredRelationships = ref<any[]>([]);

    // Store instances
    const statusStore = useStatusStore();
    const route = useRoute();

    // Computed properties
    const apiStatus = computed(() => statusStore.apiStatus);

    // Methods
    const filterRelationships = (gender: string) => {
        const maleRelationships = ["Brother", "Father", "Son", "Grandfather", "<PERSON><PERSON>", "Boyfriend", "Stepfather", "<PERSON><PERSON>"];
        const femaleRelationships = ["Sister", "Mother", "Daughter", "Grandmother", "Granddaughter", "Girlfriend", "Stepmother", "Stepdaughter"];
        const commonRelationships = [
            "Spouse/Partner",
            "Aunt/Uncle",
            "Niece/Nephew",
            "Doctor",
            "Other",
            "Patient",
            "TB Contact Person",
            "TB Patient",
            "treatment suporter",
        ];

        if (relationshipsData.value?.length > 0) {
            filteredRelationships.value = relationshipsData.value.filter((relationship: any) => {
                if (gender === "M") {
                    return maleRelationships.includes(relationship.a_is_to_b) || commonRelationships.includes(relationship.a_is_to_b);
                } else if (gender === "F") {
                    return femaleRelationships.includes(relationship.a_is_to_b) || commonRelationships.includes(relationship.a_is_to_b);
                }
                return false;
            });
        }
    };

    const getRelationships = async (gender: any) => {
        if (gender) {
            if (Service.getUseIndexDBStatus() && Service.getModsStatus()) {
                relationshipsData.value = await getOfflineRecords("relationship");
            } else {
                relationshipsData.value = await RelationsService.getRelations();
            }

            filterRelationships(gender);

            relationships.value = relationships.value = filteredRelationships.value
                .map((r: any) => {
                    return [
                        {
                            name: r.b_is_to_a,
                            id: r.relationship_type_id,
                            trackByID: r.relationship_type_id + r.b_is_to_a,
                        },
                    ];
                })
                .reduce((acc: any, val: any) => acc.concat(val), []);
            return relationships.value;
        }
    };

    // Return reactive data and methods
    return {
        // Reactive data
        relationships,
        relationshipsData,
        filteredRelationships,

        // Computed properties
        apiStatus,

        // Methods
        getRelationships,
        filterRelationships,
    };
}
