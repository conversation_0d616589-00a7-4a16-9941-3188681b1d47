ion-content,
.main-content {
    --background: #fff;
}

.full-height-container {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    min-height: calc(100vh - 120px);
}

.centered-content {
    width: 100%;
    max-width: 1200px;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.header-section {
    text-align: center;
    margin-bottom: 1rem;

    .title-container {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.75rem;
        margin-bottom: 0.5rem;
    }

    .title-icon {
        font-size: 1.75rem;
        color: var(--ion-color-primary);
    }

    .page-title {
        font-size: 2rem;
        font-weight: 700;
        color: var(--ion-color-dark);
        margin: 0;
        letter-spacing: -0.02em;
    }

    .page-subtitle {
        font-size: 1rem;
        color: var(--ion-color-dark);
        margin: 0;
        font-weight: 400;
    }
}

.search-card {
    margin-bottom: 1rem;

    .search-content {
        padding: 0px;
    }
}

.search-row {
    gap: 1rem;
}

.search-col {
    padding: 0;
}

.patient-section {
    height: 100%;
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid transparent;
    transition: all 0.3s ease;
    background: #fff;

    &.primary-section {
        border-color: rgba(76, 175, 80, 0.2);
    }

    &.secondary-section {
        border-color: rgba(33, 150, 243, 0.2);
    }

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    }
}

.section-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    flex-wrap: wrap;

    .section-icon {
        font-size: 1.1rem;

        &.primary-icon {
            color: var(--ion-color-primary);
        }

        &.secondary-icon {
            color: var(--ion-color-secondary);
        }
    }

    .section-title {
        font-weight: 600;
        font-size: 1rem;
        color: var(--ion-color-dark);
    }

    .section-note {
        font-size: 0.8rem;
        margin-left: auto;
    }
}

.input-group {
    display: flex;
    gap: 0.75rem;
    align-items: flex-end;
    border: 1px solid #efefef;
    border-radius: 8px;
    padding-left: 8px;

    .patient-input {
        flex: 1;
        --border-width: 2px;
        --highlight-color: var(--ion-color-primary);
    }

    .search-button {
        --border-radius: 8px;
        height: 56px;
        width: 56px;

        &:hover {
            --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }
    }
}

.preview-card {
    margin-bottom: 1rem;
    border-radius: 4px;
    border: 1px solid #efefef;

    .preview-content {
        padding: 1rem;
    }
}

.preview-header {
    text-align: center;
    margin-bottom: 1rem;

    .preview-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--ion-color-dark);
        margin: 0 0 0.5rem 0;
    }

    .preview-note {
        font-size: 0.9rem;
    }
}

.preview-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 20rem;
    border-radius: 12px;

    .placeholder-image {
        max-width: 100%;
        height: auto;
        opacity: 0.8;
        border-radius: 8px;
        max-height: 19rem;
    }
}

.action-section {
    text-align: center;

    .merge-button {
        --border-radius: 8px;
        font-weight: 600;
        font-size: 1.1rem;
        margin-bottom: 0.75rem;

        &:not([disabled]):hover {
            transform: translateY(-1px);
        }
    }

    .action-note {
        font-size: 0.9rem;
        display: block;
    }
}

ion-header {
    border-bottom: 1px solid transparent;

    &::after {
        background-image: none;
    }
}

ion-card,
ion-card-content {
    box-shadow: none;
    --box-shadow: none;
}

.results-container {
    margin-top: 1rem;
}

.results-section {
    margin-bottom: 1.5rem;
}

.results-title {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--ion-color-dark);
}

.results-icon {
    margin-right: 0.5rem;
    font-size: 1.2rem;
}

.primary-icon {
    color: var(--ion-color-primary);
}

.secondary-icon {
    color: var(--ion-color-secondary);
}

.patient-list {
    background: transparent;
}

.patient-item {
    --padding-start: 1rem;
    --padding-end: 1rem;
    margin-bottom: 0.5rem;
    border-radius: 8px;
    transition: all 0.2s ease;
    background: #fff;

    &.selected {
        --background: var(--ion-color-light);
        border: 2px solid var(--ion-color-primary);
    }

    &:hover {
        --background: var(--ion-color-light-tint);
    }
}

.patient-avatar {
    --size: 45px;
    margin-right: 1rem;
}

.avatar-placeholder {
    border-radius: 10px;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--ion-color-primary);
    color: #fff;
    font-weight: bold;
    font-size: 1.2rem;
}

.patient-name {
    font-weight: 600;
    color: var(--ion-color-dark);
    margin-bottom: 0.25rem;
}

.patient-details {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
    color: var(--ion-color-dark);
}

.gender-badge {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
}

.patient-ids,
.patient-location {
    font-size: 0.8rem;
    color: var(--ion-color-dark-shade);
    margin-bottom: 0.1rem;
}

.selected-icon {
    font-size: 1.5rem;
}

.search-button {
    min-width: 44px;

    ion-spinner {
        --color: white;
    }
}

.scrollable-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--ion-color-light-shade);
    border-radius: 8px;
}

.results-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--ion-color-dark);
}

.selection-count {
    font-size: 0.7rem;
    padding: 0.3rem 0.6rem;
    margin-left: auto;
}

.scrollable-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--ion-color-light-shade);
    border-radius: 8px;
    background: transparent;
}

.patient-item {
    --padding-start: 1rem;
    --padding-end: 1rem;
    margin-bottom: 0.5rem;
    border-radius: 8px;
    transition: all 0.2s ease;

    &.selected {
        --background: var(--ion-color-light);
        border: 2px solid var(--ion-color-secondary);
        transform: translateX(4px);
    }

    &:hover {
        --background: var(--ion-color-light-tint);
    }
}

.merge-count {
    margin-left: 0.5rem;
    font-size: 0.8rem;
    font-weight: 600;
}

.action-note {
    font-size: 0.9rem;
    display: block;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.4;
}

.scrollable-list::-webkit-scrollbar {
    width: 6px;
}

.scrollable-list::-webkit-scrollbar-track {
    background: var(--ion-color-light);
    border-radius: 3px;
}

.scrollable-list::-webkit-scrollbar-thumb {
    background: var(--ion-color-dark);
    border-radius: 3px;
}

.scrollable-list::-webkit-scrollbar-thumb:hover {
    background: var(--ion-color-dark-shade);
}

.patient-item.selected {
    .patient-name {
        color: var(--ion-color-secondary);
        font-weight: 700;
    }

    .avatar-placeholder {
        background: var(--ion-color-secondary);
    }
}

.primary-results .patient-item.selected {
    border-color: var(--ion-color-primary);

    .patient-name {
        color: #10a200;
    }

    .avatar-placeholder {
        background: #10a200;
    }
}
