.custom-header {
    --color: white;
    padding: 8px 16px;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.title-section {
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-icon {
    font-size: 24px;
    color: white;
}

.custom-title {
    font-size: 20px;
    font-weight: 600;
    color: white;
    margin: 0;
}

.custom-content {
    --background: #fff;
}

.progress-overview {
    background: white;
    margin: 16px;
    margin-bottom: 8px;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.progress-stats h3 {
    margin: 0;
    font-size: 24px;
    font-weight: 700;
    color: #1e293b;
}

.progress-stats p {
    margin: 4px 0 0 0;
    font-size: 14px;
    color: #64748b;
}

.completion-badge {
    font-size: 14px;
    font-weight: 600;
    padding: 8px 12px;
}

.custom-progress {
    height: 8px;
    border-radius: 4px;
    --background: #e2e8f0;
}

.tasks-container {
    padding: 8px;
}

.tasks-grid {
    padding: 0;
}

.task-col {
    padding: 8px;
}

.task-card {
    background: white;
    border-radius: 16px;
    padding: 20px;
    border: 1px solid #e2e8f0;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    min-height: 140px;
}

.task-card:hover:not(.task-disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: #3b82f6;
}

.task-completed {
    border-color: #10b981;
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
}

.task-disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.task-icon-container {
    margin-bottom: 12px;
    padding: 16px;
    border-radius: 50%;
    background: #f1f5f9;
    display: flex;
    align-items: center;
    justify-content: center;
}

.task-completed .task-icon-container {
    background: #d1fae5;
}

.task-icon {
    font-size: 32px;
}

.task-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.task-label {
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 8px;
    text-align: center;
}

.task-status {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: #10b981;
    font-weight: 500;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
    background: white;
    border-radius: 16px;
    margin: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.empty-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: #f1f5f9;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 24px;
}

.empty-icon ion-icon {
    font-size: 40px;
    color: #64748b;
}

.empty-state h3 {
    margin: 0 0 12px 0;
    font-size: 20px;
    font-weight: 600;
    color: #1e293b;
}

.empty-state p {
    margin: 0 0 24px 0;
    font-size: 16px;
    color: #64748b;
    max-width: 300px;
    line-height: 1.5;
}

.empty-action {
    --border-radius: 8px;
    height: 44px;
    font-weight: 500;
}

@media (max-width: 768px) {
    .progress-header {
        flex-direction: column;
        gap: 12px;
        text-align: center;
    }

    .task-card {
        min-height: 120px;
        padding: 16px;
    }

    .task-icon {
        font-size: 28px;
    }

    .task-label {
        font-size: 14px;
    }
}
