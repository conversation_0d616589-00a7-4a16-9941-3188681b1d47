/* _utilities.css */

.color_white {
    color: #fff !important;
}

ion-icon {
    font-size: 25px;
}

ion-select .select-wrapper .select-wrapper-inner {
    width: 100%;
    color: red;
}

ion-accordion {
    z-index: unset;
}

.searchField {
    --border-radius: 4px;
    --box-shadow: inset 0 -3em 3em #fff, 0 0 0 2px white, 0.3em 0.3em 1em rgba(44, 44, 44, 0.6);
    width: 60vw;
    padding: 10px;
    text-align: left;
    /* max-width: 800px; */
    color: #000;
    background-color: #fff;
    border-radius: 5px;
    margin: 5px;
}

.search_card {
    width: 1290px;
    --padding: 5px;
    --background: #fff;
    left: 10px;
}
