/* _tables.css */

div.dt-buttons > .dt-button:first-child {
    border: 1px solid #fff;
    background: #046c04;
    border-radius: 5px;
}

div.dt-buttons > .dt-button:hover:not(.disabled) {
    background: #188907 !important;
    border: 1px solid #fff !important;
}

.dt-type-date {
    text-align: left !important;
}

.dt-type-numeric {
    text-align: center !important;
}

/* Modern table styling */
.modern-table {
    border-collapse: separate !important;
    border-spacing: 0 !important;
    background: white !important;
    border-radius: 8px !important;
    overflow: hidden !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
}

.modern-table thead th {
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%) !important;
    color: #334155 !important;
    font-weight: 600 !important;
    padding: 15px 12px !important;
    border: none !important;
    text-transform: uppercase !important;
    font-size: 11px !important;
    letter-spacing: 0.5px !important;
}

.modern-table tbody tr {
    transition: all 0.2s ease !important;
}

.modern-table tbody tr:nth-child(even) {
    background-color: #f8fafc !important;
}

.modern-table tbody tr:hover {
    background-color: #e2e8f0 !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
}

.modern-table tbody td {
    padding: 12px !important;
    border-bottom: 1px solid #e2e8f0 !important;
    border-top: none !important;
    vertical-align: middle !important;
}

.modern-table tbody tr:last-child td {
    border-bottom: none !important;
}

/* DataTable controls styling */
.dataTables_wrapper .dataTables_filter input {
    border: 2px solid #e2e8f0 !important;
    border-radius: 6px !important;
    padding: 6px 12px !important;
    margin-left: 8px !important;
}

.dataTables_wrapper .dataTables_filter input:focus {
    border-color: #667eea !important;
    outline: none !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    border: 1px solid #e2e8f0 !important;
    border-radius: 4px !important;
    margin: 0 2px !important;
    padding: 6px 12px !important;
    color: #4a5568 !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background: #667eea !important;
    color: white !important;
    border-color: #667eea !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background: #667eea !important;
    color: white !important;
    border-color: #667eea !important;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 10px;
    border-bottom: 2px solid #e2e8f0;
}

.encounter-title {
    margin: 0;
    color: #2d3748;
    font-size: 18px;
    font-weight: 600;
}

.text-white {
    color: #fff !important;
}

.btn-sm {
    background: none;
    margin-left: 10px;
}

.btn-outline-success {
    border-color: #28a745 !important;
    border-radius: 5px !important;
    border: #046c04 1px solid !important;
    padding: 8px 10px !important;
}
