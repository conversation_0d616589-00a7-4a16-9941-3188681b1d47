/* _modals.css */

.action-sheet-modal {
    --height: 60%;
    --width: 80%;
}

.action-sheet-modal::part(content) {
    --width: 50%;
    --height: 34%;
}

.modal_title {
    font-weight: 700;
    font-size: 24px;
    margin-bottom: 15px;
}

.modal_wrapper {
    padding: 2px;
    background-color: #fff;
}

ion-modal > .ion-page {
    background: #fff;
    overflow: auto; /* Added from original for consistency */
}

.module-picker-modal {
    --background: transparent;
    backdrop-filter: blur(10px) !important;
    --width: 100% !important;
    --height: 100% !important;
    position: fixed;
    background: transparent !important;
    top: 0;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 999;
}

.module-picker-modal > *,
.module-picker-modal > * > * {
    background: transparent !important;
    backdrop-filter: blur(10px);
    box-shadow: none !important;
    border: none !important;
}

.modal-content {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.module-picker-modal .ion-page,
.module-picker-modal .ion-page ion-content {
    --background: transparent !important;
    backdrop-filter: blur(10px) !important;
}

.large-modal {
    --border-radius: 10px;
    --width: 35vw; /* Adjusted to 98% based on media queries for consistency */
    --height: 700px; /* Adjusted to 90% based on media queries for consistency */
    --box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.otherVitalsModal {
    /* --height: 530px; */
    --width: 95vw;
    --max-width: 550px;
    --max-height: 95vh;
    --border-radius: 10px;
    --overflow: auto;
}

.activity-modal {
    --width: 58%;
    --height: 60%;
    --border-radius: 8px;
    --box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.large-modal-x10 {
    --width: 98%;
    --height: 90%;
    --border-radius: 8px;
    --box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.lab-results-modal {
    --height: 95vh;
    --border-radius: 8px;
}

.vaccineHistoryModal {
    --height: 90vh;
    --max-width: 500px;
}

.qr_code_modal {
    background-color: #fff;
}

.small-modal {
    --border-radius: 16px;
    --width: 24vw;
}

.visit-modal {
    --border-radius: 8px;
    --width: 594px;
}

.small-confirm-modal {
    --width: 50%;
    --height: 30vh;
}

.delete-popover {
    --border-radius: 16px;
    --width: 235px;
    --offset-y: 15px;
}

.nationalIDModal {
    /* --width: 50vw; */
    --border-radius: 16px;
}

ion-modal {
    --height: none;
}

.followUpVisitModal {
    --height: 80vh;
    --width: 95vw;
    --max-width: 550px;
    --border-radius: 10px;
    overflow: auto;
}

.modalTitle {
    padding: 10px;
}

.largeModal {
    --height: 95vh;
}

.fullScreenModal {
    --height: 98vh;
    --width: 98% !important;
    --max-width: 98% !important;
}

.mediumModal {
    --height: 50vh;
    --width: 50vh;
    --border-radius: 10px;
}

.alert_content {
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
