/* _components.css */

ion-card {
    background-color: #fff;
    border-radius: 5px;
}

video {
    border-radius: 16px;
}

ion-toast.custom-toast {
    --start: unset;
    --border-color: red;
    --width: 370px;
}

ion-toast.custom-toast::part(container) {
    border: solid 1px;
    border-radius: 4px;
}

.toast-button {
    margin-left: 8px;
    padding: 4px 8px;
    background-color: #36d827;
    color: #333;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    font-size: 11px;
    transition: background-color 0.2s ease;
}

.toast-button:hover {
    background-color: #91eb1c;
}

.custom_card {
    border-radius: 5px;
    background-color: rgb(255, 255, 255);
    box-shadow: rgba(0, 0, 0, 0.2) 0px 3px 1px -2px, rgba(0, 0, 0, 0.14) 0px 2px 2px 0px, rgba(0, 0, 0, 0.12) 0px 1px 5px 0px;
}

#cbtn {
    --background: #b42318;
}

ion-button {
    --border-width: 1px;
}

ion-header {
    background: #fff;
}

ion-footer {
    background: #fff;
}

.custom-card {
    background-color: var(--ion-color-light);
    border-radius: 5px;
    border: solid 1px #e7e7e7;
    padding: 10px;
}

.card-margin-top {
    margin-top: 16px;
}

.input-fill-outline.sc-ion-input-md-h {
    --padding-end: 5px;
}

.unit-dropdown[data-v-73ca9311]::part(icon) {
    margin-left: 15px;
}
