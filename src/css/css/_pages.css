/* _pages.css */

/* registration */
.input_fields {
    text-align: left;
}

.card_content {
    width: 360px;
    max-width: 0 auto;
    text-align: left;
}

.card_hearder {
    font-weight: 600;
    color: #00190e;
    font-size: 16px;
    text-align: center;
}

.sub_title {
    font-weight: 400;
    font-size: 14px;
    color: #636363;
    text-align: left;
    display: flex;
}

.demographics {
    display: block;
    width: 570px;
    /* margin: 0 auto; */
    text-align: center;
    margin-top: 0px;
}

.registration_ion_card {
    display: flex;
    justify-content: center;
    padding: 1.5vw;
    margin-bottom: 20px;
    contain: unset;
    overflow: unset;
}

.no_content {
    justify-content: center;
    align-items: center;
    align-content: center;
    display: flex;
    height: 250px;
}

.start_consultation {
    height: 21px;
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 150%;
    text-align: center;
    text-decoration-line: underline;
    color: #00190e;
    cursor: pointer;
}

.no_content_title {
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 150%;
    text-align: center;
    color: #636363;
}

.previousView {
    width: 100%;
    border-radius: 5px;
    margin-top: 10px;
}

.previousLabel {
    font-weight: 600;
    color: #000;
}

/* vitals */
.vitals_overview {
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
    padding-top: 10px;
}

.notes_content {
    color: #00190e;
    font-size: 14px;
}

.notes_content li {
    padding-bottom: 15px;
    line-height: 19px;
}

.v_result {
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
}

.obese {
    padding: 0.7vw;
    border-radius: 8px;
    padding-right: 3vw;
    padding-left: 30px;
    display: flex;
    color: #016302;
    align-items: center;
}

.bmi {
    background-color: #e6e6e6;
    padding: 0.7vw;
    border-radius: 8px;
    padding-right: 3vw;
    padding-left: 30px;
    display: flex;
    color: #00190e;
    align-items: center;
}

/* medications */
.m_name {
    font-weight: 400;
    color: #00190e;
    line-height: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.m_name_dosage {
    border-bottom: 1px solid #ccc;
    border-bottom-style: dashed;
    padding-bottom: 20px;
}

.m_btns {
    justify-content: space-between;
    display: flex;
    width: 50px;
    padding-top: 5px;
    align-items: center;
}

/* investigations */
.laboratory {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #00190e;
    border-bottom: solid 1px #ccc;
    border-bottom-style: dashed;
    line-height: 60px;
}

.visitContent {
    background: #fff;
    margin-top: 1px;
    padding: 20px;
}
