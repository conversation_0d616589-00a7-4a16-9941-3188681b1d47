/* _wizard.css */

.wizard_title {
    color: #00190e;
    text-align: center;
    width: inherit;
    font-size: 18px;
    padding-top: 20px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.checked_step {
    font-size: 17px;
    font-weight: 700;
    margin-top: 1px;
    margin-left: -8px;
    position: absolute;
}

.open_step {
    border: 2px solid var(--ion-color-primary) !important;
    background-color: var(--ion-color-primary);
    color: #fff !important;
}

.color_white {
    color: #fff !important;
}

.wizard_verticle ul.wizard_steps li a .common_step {
    width: 25px;
    height: 25px;
    line-height: 21px;
    border: 1px solid #b4b4b4;
    border-radius: 100px;
    display: block;
    margin: 0 auto 5px;
    font-size: 13px;
    text-align: center;
    position: relative;
    color: #b4b4b4;
    z-index: 5;
}

.wizard_verticle ul.wizard_steps {
    display: table;
    list-style: none;
    position: relative;
    width: 20%;
    float: left;
    margin: 0 0 20px;
}

.list-unstyled {
    padding-left: 0;
}

.wizard_verticle ul.wizard_steps li a:before {
    content: "";
    position: absolute;
    height: 28px;
    background: #b4b4b4;
    top: 27px;
    width: 1px;
    z-index: 4;
    left: 49%;
}

.wizard_verticle ul.wizard_steps li a {
    height: 80px;
}

.wizard_verticle ul.wizard_steps li a,
.wizard_verticle ul.wizard_steps li:hover {
    display: block;
    position: relative;
    -moz-opacity: 1;
    filter: alpha(opacity=100);
    opacity: 1;
    color: #666;
}

.wizard_verticle ul.wizard_steps li a {
    height: 30px;
}

.wizard_verticle ul.wizard_steps li a:first-child {
    margin-top: 27px;
}

.wizard_text {
    position: absolute;
    width: 14vw;
    font-size: 16px;
    text-align: left;
    margin-left: 20px;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #636363;
}

.last_step ::before {
    display: none;
}

/* Specific to form-wizard-vue */
.fw-step-title {
    font-size: 17px;
    font-weight: 600;
}

.fw-list-wrapper-icon {
    font-size: 1.2rem;
}

@media (max-width: 937px) {
    .form-wizard-vue .fw-body-list {
        display: none;
    }
}
