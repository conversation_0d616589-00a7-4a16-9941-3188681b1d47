/* _base.css */

* {
    padding: revert-layer;
}

body {
    /* Add any global body styles here if not already defined elsewhere */
}

a {
    text-decoration: none;
}

.position_content {
    max-width: 88vw;
    margin: 0 auto;
}

.primary_color_background {
    background-color: var(--ion-color-primary);
    --background: var(--ion-color-primary);
}

.dashed_bottom_border {
    border-bottom: 1px solid #b3b3b3;
    border-bottom-style: dashed;
    --border-color: transparent;
}

.solid_bottom_border {
    border-bottom: 1px solid #eee;
}

.display_center {
    display: flex;
    align-items: center;
}

.popover {
    --bs-popover-max-width: none;
}

.ellipsis {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.dash_box {
    border: 1px solid #b3b3b3;
    border-style: dashed;
    padding: 15px;
    width: 100%;
    text-align: center;
    border-radius: 5px;
    color: #b3b3b3;
}

.add_item {
    color: var(--ion-color-primary);
    font-weight: 700;
    margin-top: 20px;
    font-size: inherit;
}

.input_item {
    border: 1px solid #b3b3b3;
    --border-width: 0 0 0 0;
    border-radius: 5px;
    --background: #fff;
}

.input_item:focus {
    border: 1px solid var(--ion-color-primary);
    --border-width: 0 0 0 0;
    border-radius: 5px;
}

.orange_background {
    --background: #f9a639;
    color: #fff;
}

.position_center_vetical {
    display: flex;
    align-items: center;
}

.primary_btn {
    font-weight: 600;
    font-size: var(--ion-button-font);
}

.diplay_space_between {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.text_header_16 {
    color: #00190e;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    padding-bottom: 10px;
}

.text_header_14 {
    font-weight: 400;
    font-size: 14px;
    padding: 6px 0px;
    color: #00190e;
}

.bold {
    font-weight: 700;
}

.ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.table-responsive {
    width: 100%;
    overflow-x: auto;
}

/* Remove number input arrows if rendered */
ion-input input::-webkit-outer-spin-button,
ion-input input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
    margin: 0 !important;
}

ion-input input[type="number"] {
    -moz-appearance: textfield !important;
}

ion-button {
    text-transform: none;
}
