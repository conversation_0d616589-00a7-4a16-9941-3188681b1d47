/* _third-party.css */

/* Multiselect */
@media not all and (display-mode: browser) {
    .multiselect {
        transform: translateZ(0) !important;
        -webkit-transform: translateZ(0) !important;
    }
}

.multiselect__content-wrapper {
    width: auto;
    z-index: 999999 !important;
}

.multiselect__input,
.multiselect__single {
    min-height: 40px;
    line-height: 40px;
}

.groupInput .multiselect__tags {
    padding-left: revert;
    border: none;
    border-left: solid 1px #ccc;
    border-radius: unset;
    background: unset;
}

.groupInput .multiselect {
    width: 110px;
}

.multiselect__tags {
    min-height: 56px;
    border: 1px solid #b3b3b3;
    padding-left: 35px;
}

.multiselect--disabled .multiselect__tags {
    background: #ededed !important;
}

.multiselect--disabled .multiselect__input,
.multiselect--disabled .multiselect__single {
    background: #ededed !important;
}

.multiselect__input {
    background: unset !important;
}

.multiselect__select::before {
    top: 95%;
}

.multiselect::before {
    content: url("/svg/searchIcon.svg");
    position: absolute;
    font-size: 40px;
    margin-left: 15px;
    top: -7px;
}

.multiselect__placeholder {
    padding-top: 9px;
    padding-left: 10px;
}

/* ApexCharts */
.apexcharts-zoomin-icon,
.apexcharts-zoomout-icon {
    display: none;
}

/* Toastify */
@media only screen and (max-width: 480px) {
    .Toastify__toast-container {
        left: unset;
        margin: 0;
        padding: 0;
        width: unset;
    }
}

.Toastify__toast-container--top-right {
    right: 0.3em;
    top: 0.5em;
}

.Toastify__toast {
    padding: 3px;
    margin-bottom: 5px;
    border-radius: 5px;
    border: solid 1px transparent;
    box-shadow: 0 0 0 1px rgba(230, 230, 230, 0.5);
}

.Toastify__toast-body > div:last-child {
    font-size: 12px;
}

/* Vue Tel Input */
.vti__dropdown-list {
    z-index: 10;
}

.vue-tel-input {
    height: 56px;
}

/* Vue Datepicker */
.dp__theme_light {
    /* Variables are in _variables.css */
}

/* Carousel */
.carousel__pagination-button--active::after {
    background-color: #cfcfcf;
}

.carousel__pagination-button::after {
    display: block;
    content: "";
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #39bca4;
}

.carousel__pagination-button--active {
    background-color: #e7e7e7;
    border-radius: 50%;
}
