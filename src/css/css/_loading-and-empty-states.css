/* _loading-and-empty-states.css */

.spinner-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.5);
    z-index: 9999;
}

ion-spinner {
    width: 80px;
    height: 80px;
}

.loading-text {
    margin-top: 20px;
    font-size: 18px;
    color: #333;
}

.loading {
    pointer-events: none;
}

/* Loading and empty states */
.loading-state,
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6b7280;
    font-size: 14px;
}

.loading-state {
    background: #f9fafb;
    border-radius: 8px;
    border: 1px dashed #d1d5db;
}

.empty-state {
    background: #fefefe;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}
