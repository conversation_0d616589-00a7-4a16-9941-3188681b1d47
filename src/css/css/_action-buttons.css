/* _action-buttons.css */

.action-button {
    border: none;
    color: white;
    padding: 8px 12px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 14px;
    margin: 4px 2px;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.3s ease, transform 0.2s ease;
}

.action-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.test-print-button {
    background-color: #4caf50; /* Green */
}

.test-print-button:hover {
    background-color: #45a049;
}

.edit-button {
    background-color: #2196f3; /* Blue */
}

.edit-button:hover {
    background-color: #1976d2;
}

.delete-button {
    background-color: #f44336; /* Red */
}

.delete-button:hover {
    background-color: #d32f2f;
}

.alert {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 50vh;
    text-align: center;
    flex-direction: column;
}
