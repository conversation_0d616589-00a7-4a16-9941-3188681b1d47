<template>
    <ion-page>
        <Toolbar />
        <ion-content>
            <div class="form-container">
                <div class="form-header">
                    <h2>Data Cleaning Verification</h2>
                    <button @click="handleCancel" class="cancel-button">X</button>
                </div>

                <form @submit.prevent="handleSubmit" class="form-body">
                    <!-- Date Picker -->
                    <div class="form-group">
                        <label for="date">Date</label>
                        <input type="date" id="date" v-model="form.date" required />
                    </div>

                    <!-- Name Input -->
                    <div class="form-group">
                        <label for="name">Supervisor Name</label>
                        <input type="text" id="name" v-model="form.name" required placeholder="Enter supervisor name" />
                    </div>

                    <!-- Comment Input -->
                    <div class="form-group">
                        <label for="comment">Comment (optional)</label>
                        <textarea id="comment" v-model="form.comment" placeholder="Enter your comment"></textarea>
                    </div>

                    <!-- Submit Button -->
                    <div class="form-actions">
                        <button type="submit" class="submit-button">Submit</button>
                    </div>
                </form>
            </div>
        </ion-content>
    </ion-page>
</template>

<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { Service } from '@/services/service';
import Toolbar from '@/components/Toolbar.vue';
import { IonContent, IonPage } from "@ionic/vue";

const router = useRouter();

const form = ref({
    date: '',
    name: '',
    comment: '',
});

const formatDate = () => {
    if (form.value.date) {
        const dateObj = new Date(form.value.date);
        const year = dateObj.getFullYear();
        const month = String(dateObj.getMonth() + 1).padStart(2, '0');
        const day = String(dateObj.getDate()).padStart(2, '0');
        form.value.date = `${year}-${month}-${day}`;
    }
};

const handleSubmit = async () => {
    formatDate();

    const payload = {
        data_cleaning_datetime: form.value.date,
        supervisors: form.value.name,
        comments: form.value.comment,
    };

    console.log('Payload to send:', payload);

    try {
        await Service.postJson('data_cleaning_supervisions', payload);
        alert('Form submitted successfully!');
    } catch (error) {
        console.error('Error submitting form:', error);
        alert('Failed to submit form.');
    }
};

const handleCancel = () => {
    router.back();
};
</script>

<style scoped>
.form-container {
    border: 1px solid #ccc;
    border-radius: 8px;
    padding: 16px;
    background: #fff;
    position: relative;
    max-width: 500px;
    margin: 0 auto;
}

.form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cancel-button {
    background: transparent;
    border: none;
    font-size: 20px;
    cursor: pointer;
}

.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: bold;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
    border: 1px solid #ccc;
    border-radius: 4px;
    background-color: #fff;
    color: #000;
}

.form-group input:focus,
.form-group textarea:focus {
    border-color: #007bff;
    outline: none;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
}

.submit-button {
    background-color: #e53935;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.submit-button:hover {
    background-color: #c62828;
}
</style>
