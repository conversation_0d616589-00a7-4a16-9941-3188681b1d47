<template>
  <ion-page :class="{ loading: isLoading }">
    <div v-if="isLoading" class="spinner-overlay">
      <ion-spinner name="bubbles"></ion-spinner>
      <div class="loading-text">Please wait...</div>
    </div>

    <Toolbar />

    <ion-content>
      <div class="container">
        <h4 style="text-align: center; font-weight: bold; margin-bottom: 20px">
          Select Identifier Type
        </h4>

        <div class="select-group-wide">
  <ion-item>
    <ion-label position="stacked">Identifier Type</ion-label>
    <ion-select
      interface="popover"
      placeholder="Select Identifier Type"
      v-model="selectedIdentifier"
      @ionChange="selectIdentifier($event.detail.value)"
      class="wide-select"
    >
      <ion-select-option 
        v-for="identifierType in identifierTypes" 
        :key="identifierType.id"
        :value="identifierType.id"
      >
        {{ identifierType.name }}
      </ion-select-option>
    </ion-select>
  </ion-item>
</div>

        <!-- Dynamic Content Area -->
        <div v-if="selectedIdentifier" class="content-area">
          <!-- Dynamic Table Component -->
          <div class="table-section">
            <h4 style="width: 100%; text-align: center; font-weight: 700; margin: 20px 0">
              Multiple Identifier by {{ getSelectedIdentifierName() }}
            </h4>

            <div v-if="hasTableData" class="table-responsive">
              <DataTable 
                ref="dataTable" 
                :options="tableOptions" 
                :data="reportData" 
                class="display nowrap" 
                width="100%"
              >
                <thead>
                  <tr>
                    <th>First Name</th>
                    <th>Last Name</th>
                    <th>Gender</th>
                    <th>Number of Identifiers</th>
                    <th>View</th>
                  </tr>
                </thead>
              </DataTable>
            </div>
            
            <div v-else class="placeholder-content">
              <p style="text-align: center; color: #666; font-style: italic;">
                {{ getSelectedIdentifierName() }} identifier functionality will be implemented here
              </p>
            </div>
          </div>
        </div>
      </div>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from "vue";
import { IonPage, IonContent, IonButton, IonSpinner, IonItem, IonLabel, IonSelect, IonSelectOption } from "@ionic/vue";
import Toolbar from "@/components/Toolbar.vue";
import DataTable from "datatables.net-vue3";
import DataTablesCore from "datatables.net";
import DataTablesResponsive from "datatables.net-responsive";
import "datatables.net-buttons";
import "datatables.net-buttons/js/buttons.html5";
import "datatables.net-buttons-dt";
import "datatables.net-responsive";
import "datatables.net-select";
import { createModal, toastWarning } from "@/utils/Alerts";
import OfflineMoreDetailsModal from "@/components/Modal/OfflineMoreDetailsModal.vue";
import { getOfflineRecords } from "@/services/offline_service";
import { IdentifierService } from "@/services/identifier_service";

interface IdentifierType {
  id: number;
  name: string;
}

const isLoading = ref(false);
const selectedIdentifier = ref<number | null>(null);
const identifierTypes = ref<IdentifierType[]>([]);
const reportData = ref([]);
const dataTable = ref(null);
const hasTableData = ref(false);

const tableOptions = {
  responsive: true,
  select: false,
  layout: {
    topStart: null,
    topEnd: "search",
    bottomStart: "info",
    bottomEnd: "paging",
  },
} as any;

const loadIdentifierTypes = async () => {
  try {
    isLoading.value = true;
    const response = await IdentifierService.getIdentifierTypes();
    identifierTypes.value = response.map((type: any) => ({
      id: type.patient_identifier_type_id || type.id,
      name: type.name
    }));
  } catch (error) {
    console.error("Error loading identifier types:", error);
    toastWarning("Failed to load identifier types.");
  } finally {
    isLoading.value = false;
  }
};

const selectIdentifier = async (identifierId: number) => {
  selectedIdentifier.value = identifierId;
  await buildTableData(identifierId);
};

const shouldShowTableData = (identifierType: IdentifierType): boolean => {
  // Show table data for all identifier types
  return true;
};

const buildTableData = async (identifierId: number) => {
  isLoading.value = true;
  try {
    const identifierService = new IdentifierService();
    identifierService.setIdentifierType(identifierId);
    
    // Get multiple identifiers data from the service
    const multipleIdentifiers = await identifierService.getMultipleIdentifiers();
    
    if (multipleIdentifiers && multipleIdentifiers.length > 0) {
      reportData.value = multipleIdentifiers.map((item: any, index: number) => [
        item.given_name || 'N/A',
        item.family_name || 'N/A', 
        item.gender || 'N/A',
        item.identifiers ? item.identifiers.length.toString() : '0',
        `<button class="btn btn-sm btn-primary view-btn" data-index="${index}" data-identifiers='${JSON.stringify(item.identifiers || [])}'>View</button>`,
      ]);
      hasTableData.value = true;
    } else {
      // No multiple identifiers found, show empty state
      reportData.value = [];
      hasTableData.value = false;
    }
    
    DataTable.use(DataTablesCore);
    
    // Wait for next tick to ensure DOM is updated
    await nextTick();
    
    // Setup event listeners for the table
    setTimeout(() => {
      setupTableEventListeners();
    }, 100);
    
  } catch (error) {
    console.error("Error building table data:", error);
    toastWarning("An error occurred while loading data.");
    hasTableData.value = false;
    reportData.value = [];
  } finally {
    isLoading.value = false;
  }
};

const loadOfflineRecords = async () => {
  try {
    const documents: any = await getOfflineRecords("patientRecords");
    if (documents && documents.length > 0) {
      reportData.value = documents.map((item: any) => [
        `${item.personInformation.given_name} ${item.personInformation.family_name}`,
        item.ID,
        item.saveStatusPersonInformation,
        item.saveStatusBirthRegistration,
        item.saveStatusGuardianInformation,
        "",
        "",
        `<button class="btn btn-sm btn-primary edit-btn" data-id='${JSON.stringify(item)}'>More details</button>`,
      ]);
      hasTableData.value = true;
    } else {
      hasTableData.value = false;
    }
  } catch (error) {
    console.error("Error loading offline records:", error);
    hasTableData.value = false;
  }
};

const setupTableEventListeners = () => {
  if (dataTable.value && hasTableData.value) {
    const table = (dataTable.value as any).dt;
    table.columns.adjust().draw();
    
    // Remove existing event listeners to prevent duplicates
    table.off("click", ".view-btn");
    
    // Add new event listeners
    table.on("click", ".view-btn", (e: Event) => {
      const target = e.target as HTMLElement;
      const identifiers = target.getAttribute("data-identifiers");
      const index = target.getAttribute("data-index");
      handleViewIdentifiers(identifiers, index);
    });
  }
};

const getSelectedIdentifierName = (): string => {
  const selectedType = identifierTypes.value.find(type => type.id === selectedIdentifier.value);
  return selectedType ? selectedType.name : '';
};

const handleViewIdentifiers = async (identifiersJson: string | null, rowIndex: string | null) => {
  if (!identifiersJson || !rowIndex) return;
  
  try {
    const identifiers = JSON.parse(identifiersJson);
    await openIdentifierDetailsModal(identifiers, parseInt(rowIndex));
  } catch (error) {
    console.error("Error parsing identifiers:", error);
    toastWarning("Error viewing identifier details");
  }
};

const openIdentifierDetailsModal = async (identifiers: any[], rowIndex: number) => {
  const identifierService = new IdentifierService();
  identifierService.setIdentifierType(selectedIdentifier.value || 0);
  
  // Create modal data for the drill-down view
  const modalData = {
    title: `Identifiers belonging to client`,
    identifiers: identifiers,
    onVoidIdentifier: async (identifierId: number, index: number) => {
      await handleVoidIdentifier(identifierId, identifiers, index, rowIndex);
    }
  };
  
  const data = await createModal(
    OfflineMoreDetailsModal, 
    { class: "fullScreenModal" }, 
    true, 
    { clientData: modalData }
  );
  
  if (data === "dismiss" && selectedIdentifier.value) {
    await buildTableData(selectedIdentifier.value);
  }
};

const handleVoidIdentifier = async (identifierId: number, identifiers: any[], identifierIndex: number, rowIndex: number) => {
  // This would typically show a void reason modal
  // For now, we'll use a simple confirm dialog
  if (confirm("Are you sure you want to void this identifier? Please provide a reason.")) {
    try {
      const reason = prompt("Please provide a reason for voiding this identifier:") || "No reason provided";
      
      const identifierService = new IdentifierService();
      identifierService.setIdentifierType(selectedIdentifier.value || 0);
      
      await identifierService.voidMultipleIdentifiers(
        [identifierId], 
        reason, 
        identifiers[0]?.identifier_type || selectedIdentifier.value
      );
      
      // Remove the voided identifier from the list
      identifiers.splice(identifierIndex, 1);
      
      // If only one identifier remains, remove the entire row
      if (identifiers.length <= 1) {
        reportData.value.splice(rowIndex, 1);
        // Refresh the table
        if (dataTable.value) {
          const table = (dataTable.value as any).dt;
          table.clear();
          table.rows.add(reportData.value);
          table.draw();
        }
      } else {
        // Update the count in the table
        reportData.value[rowIndex][3] = identifiers.length.toString();
        if (dataTable.value) {
          const table = (dataTable.value as any).dt;
          table.clear();
          table.rows.add(reportData.value);
          table.draw();
        }
        // Reopen the modal with updated identifiers
        await openIdentifierDetailsModal(identifiers, rowIndex);
      }
      
    } catch (error) {
      console.error("Error voiding identifier:", error);
      toastWarning("Failed to void identifier");
    }
  }
};

onMounted(async () => {
  DataTable.use(DataTablesCore);
  await loadIdentifierTypes();
});
</script>

<style scoped>
@import "datatables.net-dt";
@import "datatables.net-buttons-dt";
@import "datatables.net-responsive-dt";
@import "datatables.net-select-dt";

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.select-group {
  max-width: 500px;
  margin: 0 auto 30px auto;
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: 15px;
  max-width: 500px;
  margin: 0 auto 30px auto;
}

.content-area {
  margin-top: 30px;
  width: 100%;
}

.table-section {
  width: 100%;
}

.table-responsive {
  width: 100%;
  overflow-x: auto;
  margin-top: 20px;
}

.placeholder-content {
  padding: 40px;
  text-align: center;
  border: 2px dashed #ddd;
  border-radius: 8px;
  margin-top: 20px;
}

.loading-text {
  color: #000;
  text-align: center;
  margin-top: 10px;
  font-size: 16px;
}

.spinner-overlay {
  position: fixed;
  z-index: 9999;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* Custom button styling for active state */
ion-button[color="primary"],
ion-button[color="secondary"] {
  --box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

ion-button[color="medium"] {
  --box-shadow: none;
}


.select-group-wide {
  max-width: 800px; /* Increased from 500px */
  width: 100%;
  margin: 0 auto 30px auto;
  padding: 0 20px; /* Add some padding for mobile */
}

.wide-select {
  width: 100%;
  min-width: 300px; /* Ensure minimum width */
}

/* Ensure the ion-item takes full width */
.select-group-wide ion-item {
  width: 100%;
  --inner-padding-end: 16px;
  --padding-start: 16px;
}

/* Style the select popover for better visibility */
.select-group-wide ion-select {
  --placeholder-color: #666;
  --color: #000;
  width: 100%;
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
  .select-group-wide {
    max-width: 100%;
    padding: 0 10px;
  }
}

/* Alternative: If you want even more width, use this instead */
.select-group-full {
  max-width: 1000px; /* Even wider option */
  width: 100%;
  margin: 0 auto 30px auto;
  padding: 0 20px;
}
</style>