<template>
  <ion-page>
    <Toolbar title="System Audits" />
    <ion-content>
      <ion-grid>
        <ion-row>
          <ion-col size="12" size-md="12" size-lg="12">
            <div class="card-wrapper">
              <ion-card>
                <ion-card-header>
                  <ion-card-title>{{ title }}</ion-card-title>
                  <ion-card-subtitle v-if="period">{{ period }}</ion-card-subtitle>
                </ion-card-header>
                <ion-card-content>
                  <div class="date-controls">
                    <div class="date-inputs">
                      <div class="date-input-group">
                        <label>Start Date:</label>
                        <input type="date" v-model="startDateInput" :max="endDateInput || getCurrentDateInput()"
                          class="date-input" />
                      </div>
                      <div class="date-input-group">
                        <label>End Date:</label>
                        <input type="date" v-model="endDateInput" :min="startDateInput" :max="getCurrentDateInput()"
                          class="date-input" />
                      </div>
                      <ion-button fill="solid" @click="generate" :disabled="!startDateInput || !endDateInput">
                        Generate Report
                      </ion-button>
                    </div>
                  </div>

                  <div v-if="reportData.length > 0" class="table-responsive">
                    <table class="audits-table">
                      <thead>
                        <tr>
                          <th>Encounter</th>
                          <th>Action Type</th>
                          <th>User</th>
                          <th>Timestamp</th>
                          <th>Changes</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr v-for="audit in paginatedData" :key="audit.id">
                          <td>{{ audit.auditable_type }}</td>
                          <td>{{ audit.action }}</td>
                          <td>{{ formatUser(audit.user) }}</td>
                          <td>{{ formatTimestamp(audit.created_at) }}</td>
                          <td>
                            <ion-button size="small" fill="outline" @click="viewChanges(audit)"
                              :disabled="!audit.changes || audit.changes.length === 0">
                              {{ audit.changes?.length ?? 0 }} Changes
                            </ion-button>
                          </td>
                        </tr>
                      </tbody>
                    </table>

                    <!-- Pagination -->
                    <div class="pagination-container" v-if="totalPages > 1">
                      <ion-button fill="clear" @click="previousPage" :disabled="currentPage === 1">
                        Previous
                      </ion-button>
                      <span class="page-info">
                        Page {{ currentPage }} of {{ totalPages }}
                      </span>
                      <ion-button fill="clear" @click="nextPage" :disabled="currentPage === totalPages">
                        Next
                      </ion-button>
                    </div>
                  </div>

                  <div v-else-if="reportData.length === 0 && hasGenerated" class="ion-text-center ion-padding">
                    <p>No audit records found for the selected period</p>
                  </div>

                  <div v-else class="ion-text-center ion-padding">
                    <p>Select date range and generate report to view audit data</p>
                  </div>
                </ion-card-content>
              </ion-card>
              </div>
          </ion-col>
        </ion-row>
      </ion-grid>
    </ion-content>

    <!-- Changes Detail Modal -->
    <ion-modal :is-open="isModalOpen" @didDismiss="closeModal">
      <ion-header>
        <ion-toolbar>
          <ion-title>{{ modalTitle }}</ion-title>
          <ion-buttons slot="end">
            <ion-button @click="closeModal">Close</ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>
      <ion-content>
        <ion-card>
          <ion-card-content>
            <div class="table-responsive">
              <table class="changes-table">
                <thead>
                  <tr>
                    <th>Attribute</th>
                    <th>Previous Value</th>
                    <th>Current Value</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(change, index) in modalData" :key="index">
                    <td>{{ change.attribute }}</td>
                    <td>{{ change.previous || 'N/A' }}</td>
                    <td>{{ change.current || 'N/A' }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-content>
    </ion-modal>
  </ion-page>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, watch } from "vue";
import {
  IonPage, IonContent, IonGrid, IonRow, IonCol, IonCard, IonCardHeader,
  IonCardTitle, IonCardSubtitle, IonCardContent, IonList, IonItem, IonButton,
  IonLabel, IonDatetime, IonModal, IonHeader, IonToolbar, IonTitle, IonButtons
} from "@ionic/vue";
import Toolbar from "@/components/Toolbar.vue";
import { UserService } from "@/services/user_service";
import { toastWarning } from "@/utils/toasts";
import { loader } from "@/utils/loader";
import HisDate from "@/utils/Date";

const title = ref("System Audits");
const reportData = ref<any[]>([]);
const period = ref("");
const startDate = ref("");
const endDate = ref("");
const startDateInput = ref("");
const endDateInput = ref("");
const hasGenerated = ref(false);
const currentPage = ref(1);
const rowsPerPage = 20;

// Modal state
const isModalOpen = ref(false);
const modalTitle = ref("");
const modalData = ref<any[]>([]);

// Computed properties for pagination
const totalPages = computed(() => Math.ceil(reportData.value.length / rowsPerPage));
const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * rowsPerPage;
  const end = start + rowsPerPage;
  return reportData.value.slice(start, end);
});

// Watch for date changes to update period display
watch([startDateInput, endDateInput], ([newStart, newEnd]) => {
  if (newStart && newEnd) {
    period.value = `${formatDateForDisplay(newStart)} - ${formatDateForDisplay(newEnd)}`;
  }
});

onMounted(() => {
  // Set default dates (both start and end as today)
  const today = new Date();

  endDateInput.value = today.toISOString().split('T')[0];
  startDateInput.value = today.toISOString().split('T')[0];
});

const getCurrentDateInput = () => {
  return new Date().toISOString().split('T')[0];
};

const formatDateForDisplay = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-GB', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
};

const generate = async () => {
  if (!startDateInput.value || !endDateInput.value) {
    toastWarning("Please select both start and end dates");
    return;
  }

  await loader.show();
  try {
    reportData.value = await UserService.getAuditReports(startDateInput.value, endDateInput.value);
    hasGenerated.value = true;
    currentPage.value = 1; // Reset to first page
  } catch (error) {
    console.error("Error generating audit report:", error);
    toastWarning("Failed to generate audit report");
    reportData.value = [];
  } finally {
    await loader.hide();
  }
};

const formatUser = (user: any) => {
  return user ? `${user.username} (${user.name})` : "";
};

const formatTimestamp = (timestamp: string) => {
  return HisDate.toStandardHisDisplayFormat(timestamp);
};

const viewChanges = async (audit: any) => {
  if (!audit.changes || audit.changes.length === 0) return;

  const drillData = audit.changes
    .map((change: any) => {
      const [attribute, value]: [string, any] = Object.entries(change)[0] || [];
      if (!attribute) return null;

      return /password/i.test(attribute)
        ? { attribute, previous: "*******", current: "*******" }
        : { attribute, ...value };
    })
    .filter((entry: any) => entry !== null);

  modalTitle.value = `Changes made by ${audit.user.name} on ${audit.auditable_type}`;
  modalData.value = drillData;
  isModalOpen.value = true;
};

const closeModal = () => {
  isModalOpen.value = false;
  modalTitle.value = "";
  modalData.value = [];
};

// Pagination functions
const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
};

const previousPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
};
</script>

<style scoped>
ion-card {
  margin: 16px;
}

.table-responsive {
  overflow-x: auto;
}

.audits-table,
.changes-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1rem;
}

.audits-table th,
.audits-table td,
.changes-table th,
.changes-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #dee2e6;
}

.audits-table th,
.changes-table th {
  font-weight: 600;
  background-color: #f8f9fa;
}

.audits-table tr:hover,
.changes-table tr:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

.pagination-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-top: 1rem;
}

.page-info {
  font-size: 0.9rem;
  color: var(--ion-color-medium);
}

/* Date controls styling */
.date-controls {
  margin-bottom: 1rem;
}

.date-inputs {
  display: flex;
  align-items: end;
  gap: 1rem;
  flex-wrap: wrap;
}

.date-input-group {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.date-input-group label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--ion-color-medium);
}

.date-input {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.875rem;
  background-color: white;
  min-width: 140px;
}

.date-input:focus {
  outline: none;
  border-color: var(--ion-color-primary);
  box-shadow: 0 0 0 2px rgba(var(--ion-color-primary-rgb), 0.2);
}

@media (max-width: 768px) {
  .date-inputs {
    flex-direction: column;
    align-items: stretch;
  }

    .date-input {
      min-width: auto;
    }
}

/* Modal specific styles */
ion-modal {
  --width: 80%;
  --height: 70%;
  --border-radius: 8px;
}

@media (max-width: 768px) {
  ion-modal {
    --width: 95%;
    --height: 80%;
  }
}
.card-wrapper {
  display: flex;
  justify-content: center;
}
ion-card {
  max-width: 1000px;
  width: 100%;
}
</style>