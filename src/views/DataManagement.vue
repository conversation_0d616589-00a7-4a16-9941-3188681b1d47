<template>
    <ion-page>
        <Toolbar />
        <ion-content class="ion-padding">
            <ion-card style="margin:0px auto; width:70%;">
                <ion-card-content>
                    <!-- Search Box -->
                     <ion-searchbar 
                        v-model="searchTerm" 
                        placeholder="Search indicators..."
                        show-clear-button="focus"
                        :debounce="300">
                    </ion-searchbar>

                    <div class="config-section">
                        
                        <ion-card class="ion-margin-top">
                            <ion-card-content>
                                <ion-list class="scrollable-list" style="background: white;">
                                    <ion-item v-for="(item, idx) in displayedIndicators" :key="idx" 
                                        button
                                        @click="handleIndicatorClick({ name: item.label })"
                                        :class="{ 'selected-item': selectedIndicators.includes(item.label) }">
                                        <ion-label>{{ item.label }}</ion-label>
                                        <ion-checkbox 
                                            slot="end" 
                                            :checked="selectedIndicators.includes(item.label)"
                                            @update:model-value="(checked) => toggleIndicator(item.label, checked)" />
                                    </ion-item>
                                </ion-list>
                            </ion-card-content>
                        </ion-card>
                    </div>

                    <!-- Supervision History -->
                    <ion-card class="ion-margin-top" v-if="supervisionHistory.length > 0">
                        <ion-card-header>
                            <ion-card-title>Supervision History</ion-card-title>
                        </ion-card-header>
                        <ion-card-content>
                            <ion-list class="supervision-history">
                                <ion-item v-for="(supervision, index) in supervisionHistory" :key="index" class="history-item">
                                    <ion-label>
                                        <h2>{{ formatDate(supervision.data_cleaning_datetime) }}</h2>
                                        <p>Supervisors: {{ supervision.supervisors }}</p>
                                        <p>Comments: {{ supervision.comments }}</p>
                                    </ion-label>
                                </ion-item>
                            </ion-list>
                        </ion-card-content>
                    </ion-card>

                    <!-- Loading Spinner -->
                    <div class="loading-container" v-if="isLoading">
                        <ion-spinner name="circular"></ion-spinner>
                        <ion-text color="medium">Loading...</ion-text>
                    </div>

                    <!-- Next Button -->
                    <div class="ion-padding" style="text-align: center; margin-top: 20px;">
                        <ion-button 
                            expand="block" 
                            color="primary" 
                            size="large"
                            :disabled="selectedIndicators.length === 0"
                            @click="proceedToNext">
                            <ion-icon :icon="arrowForward" slot="end"></ion-icon>
                            Next ({{ selectedIndicators.length }} selected)
                        </ion-button>
                    </div>
                </ion-card-content>
            </ion-card>
        </ion-content>

        <!-- Date Range Modal -->
        <ion-modal :is-open="isDateModalOpen" @didDismiss="closeDateModal">
            <div class="fixed-height-modal">
                <ion-header>
                    <ion-toolbar>
                        <ion-title>{{ selectedDateRangeIndicator }}</ion-title>
                        <ion-buttons slot="end">
                            <ion-button @click="closeDateModal">Close</ion-button>
                        </ion-buttons>
                    </ion-toolbar>
                </ion-header>

                <div class="modal-content">
                    <div class="date-selection-container">
                        <div class="date-chips-container">
                            <div class="date-chip" :class="{ active: !!startDate }">
                                <ion-text>
                                    <strong>Start:</strong> {{ formatDate(startDate) || 'date' }}
                                </ion-text>
                            </div>
                            <ion-icon :icon="arrowForward"></ion-icon>
                            <div class="date-chip" :class="{ active: !!endDate }">
                                <ion-text>
                                    <strong>End:</strong> {{ formatDate(endDate) || 'date' }}
                                </ion-text>
                            </div>
                        </div>
                        
                        <ion-text class="instruction-text" color="medium">
                            {{ dateSelectionMessage }}
                        </ion-text>

                        <ion-datetime
                            class="date-picker"
                            presentation="date"
                            :value="currentSelectionDate"
                            :max="todayDateString"
                            @ionChange="handleDateSelection($event)"
                            :highlight-dates="highlightedDates"
                            :show-default-buttons="false"
                            :showDefaultTimeLabel="false"
                            @ionCancel="() => {}"
                            :prefer-wheel="false">
                        </ion-datetime>
                    </div>
                    <div class="button-container">
                        <ion-button expand="block" 
                            class="apply-button"
                            @click="applyDateRange"
                            :disabled="!startDate || !endDate">
                            Apply
                        </ion-button>
                        <ion-button expand="block" 
                            fill="clear"
                            class="clear-button"
                            @click="clearDates"
                            :disabled="!startDate && !endDate">
                            Clear
                        </ion-button>
                    </div>
                </div>
            </div>
        </ion-modal>

        <!-- Verification Modal -->
        <ion-modal :is-open="showVerificationModal" @didDismiss="closeVerificationModal">
            <ion-header>
                <ion-toolbar>
                    <ion-title>Data Cleaning Verification</ion-title>
                    <ion-buttons slot="end">
                        <ion-button @click="closeVerificationModal">Close</ion-button>
                    </ion-buttons>
                </ion-toolbar>
            </ion-header>
            <ion-content>


                <form @submit.prevent="submitVerification" class="ion-padding">
                    <ion-item>
                        <ion-label position="stacked">Supervisors</ion-label>
                        <ion-input v-model="verificationForm.supervisors" required></ion-input>
                    </ion-item>
                    <ion-item>
                        <ion-label position="stacked">Comments</ion-label>
                        <ion-textarea v-model="verificationForm.comments" required></ion-textarea>
                    </ion-item>
                    <ion-button type="submit" expand="block" class="ion-margin-top">
                        Submit Verification
                    </ion-button>
                </form>
            </ion-content>
        </ion-modal>

        <!-- Results Modal -->
        <data-cleaning-results-modal
            :is-open="showResultsModal"
            :title="currentIndicator"
            :data="currentResults"
            :indicator="currentIndicator"
            :period="startDate && endDate ? `${formatDate(startDate)} - ${formatDate(endDate)}` : ''"
            :allow-verification="true"
            @close="showResultsModal = false"
            @verify="openVerificationModal"
        />
    </ion-page>
</template>

<script setup lang="ts">
import {
    IonCard,
    IonCardContent,
    IonList,
    IonSpinner,
    IonIcon,
    IonPage,
    IonItem,
    IonContent,
    IonLabel,
    IonSearchbar,
    IonButton,
    IonCheckbox,
    IonModal,
    IonHeader,
    IonToolbar,
    IonTitle,
    IonButtons,
    IonDatetime,
    IonText,
} from "@ionic/vue";
import { ref, computed, onMounted } from 'vue';
import Toolbar from "@/components/Toolbar.vue";
import {
    serverOutline,
    arrowForward,
} from "ionicons/icons";
import DataCleaningResultsModal from '@/components/DataCleaning/DataCleaningResultsModal.vue';
import { DataCleaningReportService, CtIndicator, VerificationData } from "@/services/reports/data_cleaning_report_service";
import { toastSuccess, toastWarning } from "@/utils/Alerts";
import { Service } from "@/services/service";
import { toStandardFmt } from "@/utils/his_date";
import { isEmpty } from "lodash";

interface CleaningSupervision {
    data_cleaning_datetime: string;
    supervisors: string;
    comments: string;
}

// Format date for display
const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', { 
        day: '2-digit', 
        month: 'short', 
        year: 'numeric' 
    });
};

interface Indicator {
    name: string;
    selected?: boolean;
    dateRange?: {
        start: string;
        end: string;
    };
}

// Define props with default value
const props = withDefaults(defineProps<{
    indicators?: Indicator[];
}>(), {
    indicators: () => []
});

// Define emits
const emit = defineEmits<{
    (e: 'updateIndicator', payload: { index: number; dateRange?: { start: string; end: string }; selected?: boolean }): void;
}>();

const cleaningService = new DataCleaningReportService();
const supervisionHistory = ref<CleaningSupervision[]>([]);
const isLoading = ref(false);
const searchTerm = ref('');
const selectedIndicators = ref<string[]>([]);
const isDateModalOpen = ref(false);
const startDate = ref('');
const endDate = ref('');
const selectedDateRangeIndicator = ref('');
const todayDateString = ref(new Date().toISOString());
const showVerificationModal = ref(false);
const verificationForm = ref<VerificationData>({
    data_cleaning_datetime: new Date().toISOString(),
    supervisors: '',
    comments: ''
});
const showResultsModal = ref(false);
const currentResults = ref<any[]>([]);
const currentIndicator = ref('');

// Get supervision history
const loadSupervisionHistory = async () => {
    try {
        isLoading.value = true;
        supervisionHistory.value = await cleaningService.getSupervisionHistory();
    } catch (error) {
        console.error('Error loading supervision history:', error);
        toastWarning('Failed to load supervision history');
    } finally {
        isLoading.value = false;
    }
};

// Save cleaning verification
const saveVerification = async (data: VerificationData) => {
    try {
        await cleaningService.saveDataCleaningVerification(data);
        toastSuccess('Data cleaning verification saved successfully');
        await loadSupervisionHistory(); // Refresh history after save
    } catch (error) {
        console.error('Error saving verification:', error);
        toastWarning('Failed to save verification');
    }
};


onMounted(async () => {
    await loadSupervisionHistory();
});

// Handle date selection for both start and end dates
// Computed properties for UI state
const dateSelectionMessage = computed(() => {
    if (!startDate.value) return 'Select start date';
    if (!endDate.value) return 'Select end date';
    return 'Date range selected';
});

const currentSelectionDate = computed(() => {
    if (!startDate.value) return '';
    if (!endDate.value) return '';
    return startDate.value;
});

const highlightedDates = computed(() => {
    const dates = [];
    if (startDate.value) {
        dates.push({
            date: startDate.value,
            textColor: '#ffffff',
            backgroundColor: '#3880ff'
        });
    }
    if (endDate.value) {
        dates.push({
            date: endDate.value,
            textColor: '#ffffff',
            backgroundColor: '#3880ff'
        });
    }
    return dates;
});

// Handle date selection
const handleDateSelection = (event: CustomEvent) => {
    // Only process if we have a valid date value
    if (!event.detail.value) return;
    
    // Convert to Date object to check if it's a valid date
    const selectedDate = new Date(event.detail.value);
    if (isNaN(selectedDate.getTime())) return;

    if (!startDate.value) {
        startDate.value = event.detail.value;
    } else if (!endDate.value && event.detail.value >= startDate.value) {
        endDate.value = event.detail.value;
    } else {
        // Reset selection if a new start date is selected
        startDate.value = event.detail.value;
        endDate.value = '';
    }
};

// Clear date selections
const clearDates = () => {
    startDate.value = '';
    endDate.value = '';
};

// Data Quality Indicators
const dataQualityIndicators = [
    { label: 'DOB > Date enrolled', value: 'DOB MORE THAN DATE ENROLLED',daterange: true },
    { label: 'Date enrolled < Earliest start date', value: 'DATE ENROLLED LESS THAN EARLIEST START DATE',daterange: true},
    { label: 'Encounters after Death',value: 'CLIENTS WITH ENCOUNTERS AFTER DECLARED DEAD',daterange: true},
    { label: 'Incomplete visits', value: 'INCOMPLETE VISITS',daterange: true},
    { label: 'Enrolled on ART before birth', value: 'Enrolled on ART before birth',daterange: false },
    { label: 'Missing VL Results', value: 'MISSING VL RESULTS',daterange: false },
    { label: 'Male patients with female observations', value: 'MALE CLIENTS WITH FEMALE OBS',daterange: true},
    { label: 'Missing important demographics elements', value: 'MISSING DEMOGRAPHICS',daterange: true },
    { label: 'Missing start reason',value: 'MISSING START REASONS',daterange: true},
    { label: 'Missing ART start date',value: 'MISSING ART START DATE',daterange: true},
    { label: 'Multiple start reasons',value: 'MULTIPLE START REASONS',daterange: true},
    { label: 'Patients with Pre-ART / Unknown outcome', value: 'PRE ART OR UNKNOWN OUTCOMES',daterange: true},
    { label: 'Prescriptions without dispensations', value: 'PRESCRIPTION WITHOUT DISPENSATION',daterange: true},
    { label: 'Different pregnancy value on same date', value: 'DIFFERENT PREGNANCY VALUE ON SAME DATE',daterange: true },
    { label: 'Active Clients with Adverse Outcomes', value: 'ACTIVE CLIENTS WITH ADVERSE OUTCOMES',daterange: true}
];

// Computed property for filtered indicators based on search
const filteredIndicators = computed(() => {
    if (!searchTerm.value) return dataQualityIndicators;
    
    return dataQualityIndicators.filter(indicator => 
        indicator.label.toLowerCase().includes(searchTerm.value.toLowerCase())
    );
});

// Computed property to display only first 10 items
const displayedIndicators = computed(() => {
    return filteredIndicators.value.slice(0, 10);
});

// Open date range modal
const openDateModal = (indicator: Indicator) => {
    selectedDateRangeIndicator.value = indicator.name;
    isDateModalOpen.value = true;
};

// Close date range modal
const closeDateModal = () => {
    isDateModalOpen.value = false;
};

// Apply date range
const applyDateRange = () => {
    if (!selectedDateRangeIndicator.value) {
        return;
    }
    // Add or update the date range indicator in selectedIndicators
    const isAlreadySelected = selectedIndicators.value.includes(selectedDateRangeIndicator.value);
    if (!isAlreadySelected) {
        selectedIndicators.value.push(selectedDateRangeIndicator.value);
    }

    // Store the date range in localStorage or state management if needed
    // For now, we'll just close the modal as the dates are already stored in refs
    closeDateModal();
};

// Open verification modal
const openVerificationModal = () => {
    verificationForm.value = {
        data_cleaning_datetime: new Date().toISOString(),
        supervisors: '',
        comments: ''
    };
    showVerificationModal.value = true;
};

// Close verification modal
const closeVerificationModal = () => {
    showVerificationModal.value = false;
};

// Handle indicator click
const handleIndicatorClick = (indicator: { name: string }) => {
    const foundIndicator = dataQualityIndicators.find(x => x.label === indicator.name);
    if (foundIndicator?.daterange) {
        startDate.value = '';
        endDate.value = '';
        openDateModal(indicator);
    } else {
        const isSelected = selectedIndicators.value.includes(indicator.name);
        toggleIndicator(indicator.name, !isSelected);
    }
};

// Toggle indicator selection
const toggleIndicator = (indicator: string, checked: boolean) => {
    if (checked) {
        if (!selectedIndicators.value.includes(indicator)) {
            selectedIndicators.value.push(indicator);
        }
    } else {
        const index = selectedIndicators.value.indexOf(indicator);
        if (index > -1) {
            selectedIndicators.value.splice(index, 1);
        }
    }
};

// Proceed to next step
const proceedToNext = async () => {
    if (selectedIndicators.value.length === 0) {
        toastWarning('Please select at least one indicator');
        return;
    }

    // Load reports for selected indicators
    for (const indicator of selectedIndicators.value) {
        const foundIndicator = dataQualityIndicators.find(x => x.label === indicator);
        if (foundIndicator) {
            try {
                isLoading.value = true;
                const { value: indicatorName, daterange: requiresDateRange, label } = foundIndicator;

                // Add date range parameters if required
                if (requiresDateRange) {
                    if (!startDate.value || !endDate.value) {
                        toastWarning('Please select a date range for this indicator');
                        continue;
                    }
                    
                    // Set the date range on the existing service instance
                    cleaningService.setStartDate(startDate.value);
                    cleaningService.setEndDate(endDate.value);
                }

                let data;
                if (indicatorName === 'INCOMPLETE VISITS') {
                    data = await cleaningService.getIncompleteVisits();
                } else if (indicatorName === 'Enrolled on ART before birth') {
                    data = await cleaningService.getEnrolledOnArtBeforeBirth();
                } else {
                    data = await cleaningService.getCleaningToolReport(indicatorName as CtIndicator);
                }

                console.log(data, "found....data")
                /*data = [
                    {
                        patient_id: '12345',
                        name: 'John Doe',
                        art_number: 'ART001',
                        gender: 'M',
                        age: 35,
                        dob: '1988-01-01',
                        date_enrolled: '2020-01-15'
                    },
                    {
                        patient_id: '12346',
                        name: 'Jane Smith',
                        art_number: 'ART002',
                        gender: 'F',
                        age: 28,
                        dob: '1995-03-15',
                        date_enrolled: '2021-02-20'
                    }
                ];*/

                if (data) {
                   let formattedData;
                    if (Array.isArray(data)) {
                        formattedData = data;
                    } else if (typeof data === 'object') {
                        if (data.records || data.results || data.data) {
                            formattedData = data.records || data.results || data.data;
                            if (!Array.isArray(formattedData)) {
                                formattedData = [formattedData];
                            }
                        } else {
                            formattedData = [data];
                        }
                    } else {
                        console.warn('Unexpected data format:', data);
                        formattedData = [];
                    }
                    currentResults.value = formattedData;
                    currentIndicator.value = label;
                    showResultsModal.value = true;
                    return; // Show one set of results at a time
                }
            } catch (error) {
                console.error('Error loading cleaning report:', error);
                toastWarning('Failed to load cleaning report');
            } finally {
                isLoading.value = false;
            }
        }
    }

    toastWarning('No data found for selected indicators');
};

// Submit verification form
const submitVerification = async () => {
    if (!verificationForm.value.supervisors || !verificationForm.value.comments) {
        toastWarning('Please fill in all required fields');
        return;
    }

    try {
        const data: VerificationData = {
            data_cleaning_datetime: new Date().toISOString(),
            supervisors: verificationForm.value.supervisors,
            comments: verificationForm.value.comments
        };
        await cleaningService.saveDataCleaningVerification(data);
        toastSuccess('Data cleaning verification saved successfully');
        await loadSupervisionHistory();
        closeVerificationModal();
    } catch (error) {
        console.error('Error saving verification:', error);
        toastWarning('Failed to save verification');
    }
};

// Removed duplicate functions as they are handled by proceedToNext and submitVerification
</script>

<style scoped>
.settings-card {
    margin: 0 auto;
    width: 70%;
}

.setting-item {
    margin-bottom: 1rem;
}

.custom-searchbar {
    margin-bottom: 20px;
    --background: #f8f9fa;
    --border-radius: 12px;
}

.selected-item {
    --background: #e3f2fd;
    --color: #1976d2;
}

ion-button[disabled] {
    --opacity: 0.5;
}

.ion-padding {
    padding: 16px;
}

.scrollable-list {
    max-height: 400px;
    overflow-y: auto;
    border-radius: 8px;
}

/* Custom scrollbar styles */
.scrollable-list::-webkit-scrollbar {
    width: 8px;
}

.scrollable-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.scrollable-list::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

.scrollable-list::-webkit-scrollbar-thumb:hover {
    background: #666;
}

.date-range-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding: 20px;
}

.date-input {
    background: var(--ion-color-light);
    border-radius: 8px;
    padding: 15px;
}

.date-input h2 {
    margin: 0 0 10px 0;
    color: var(--ion-color-medium);
    font-size: 16px;
}

ion-modal {
    --height: auto;
    --width: 90%;
    --max-width: 500px;
}

.fixed-height-modal {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.modal-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 16px;
    max-height: calc(90vh - 56px);
    overflow-y: auto;
}

.date-selection-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.date-chips-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 8px;
    margin-bottom: 8px;
}

.date-chip {
    background: var(--ion-color-light);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    flex: 1;
    text-align: center;
    transition: all 0.2s ease;
}

.date-chip.active {
    background: var(--ion-color-primary-light);
    color: var(--ion-color-primary);
}

.date-chip strong {
    color: inherit;
    margin-right: 4px;
}

.instruction-text {
    font-size: 0.9rem;
    margin: 8px 0;
    text-align: center;
}

.date-picker {
    --background: white;
    --height: 340px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    margin: 0;
    padding: 0;
}

.button-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 16px;
    margin-top: auto;
}

.apply-button {
    margin: 0;
}

.clear-button {
    margin: 0;
    --color: var(--ion-color-medium);
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    gap: 10px;
}

form {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

ion-textarea {
    min-height: 100px;
}

.supervision-history {
    max-height: 300px;
    overflow-y: auto;
}

.history-item {
    border-bottom: 1px solid var(--ion-color-light);
    padding: 12px 0;
}

.history-item:last-child {
    border-bottom: none;
}

@media (min-width: 768px) {
    .date-range-container {
        flex-direction: row;
        align-items: flex-start;
    }

    .date-input {
        flex: 1;
    }
}
</style>