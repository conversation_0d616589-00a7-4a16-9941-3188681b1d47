<template>
    <ion-page>
        <Toolbar />
        <ion-content>
            <report-table title="Data Cleaning Supervision History"
                subtitle="Supervision records for data cleaning activities" report-type="Data Management"
                :columns="columns" :rows="rows" :period="period" :total-clients="totalClients"
                :drill-title="drilldownTitleBuilder" useDateRangeFilter showIndices @generate="fetchData" />
        </ion-content>
    </ion-page>
</template>


<script lang="ts" setup>
import { ref } from "vue";
import { loader } from "@/utils/loader";
import ReportTable, { DrilldownData } from "@/components/ReportTable.vue";
import { TableColumnInterface } from "@uniquedj95/vtable";
import { DataCleaningReportService } from "@/services/reports/data_cleaning_report_service";
import { toastWarning } from "@/utils/toasts";
import { toDate } from "@/utils/Strs";
import { uniq } from "lodash";
import Toolbar from "@/components/Toolbar.vue";
import { IonContent, IonPage } from "@ionic/vue";

const period = ref("-");
const rows = ref<any[]>([]);
const totalClients = ref<Array<number>>();
const report = new DataCleaningReportService();

const columns: TableColumnInterface[] = [
    { path: "all_supervisors", label: "Supervisors", formatter: (val: any) => val.join("<br/>") },
    { path: "data_cleaning_datetime", label: "Data Cleaning Date", formatter: toDate },
    { path: "created_by", label: "Creator", formatter: (val: any) => val?.name ?? "-" },
    { path: "date_created", label: "Created On", formatter: toDate },
    { path: "comments", label: "Comment" },
];

// fetchData function will handle the report generation with date filtering
async function fetchData({ dateRange }: Record<string, any>, rebuild: boolean = false) {
    try {
        await loader.show();

        // If your DataCleaningReportService can handle date filters, set them here
        if (dateRange?.startDate && dateRange?.endDate) {
            report.setStartDate?.(dateRange.startDate);
            report.setEndDate?.(dateRange.endDate);
            period.value = `${dateRange.startDate} - ${dateRange.endDate}`;
        } else {
            period.value = "-";
        }

        const data: any = await report.getSupervisionHistory();

        rows.value = data;

        // Since this report might not have client totals, we still populate totalClients for compatibility
        totalClients.value = uniq(data.map((_: any, index: number) => index + 1));
    } catch (error) {
        toastWarning("ERROR! Unable to load report data");
        console.error(error);
    }
    await loader.hide();
}

function drilldownTitleBuilder(data: DrilldownData) {
    return `${data.column.label}`;
}
</script>
