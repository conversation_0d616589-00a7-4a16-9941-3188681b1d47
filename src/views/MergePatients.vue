<script lang="ts" setup>
import { ref, onMounted, computed } from "vue";
import { useRoute } from "vue-router";
import Toolbar from "@/components/Toolbar.vue";
import ActionButtons from "@/components/ActionButtons.vue";
import { searchOutline, personOutline, swapHorizontalOutline, checkmarkCircleOutline } from "ionicons/icons";
import {
    IonCard,
    IonCardContent,
    IonCol,
    IonContent,
    IonGrid,
    IonNote,
    IonPage,
    IonRow,
    IonInput,
    IonButton,
    IonIcon,
    IonSpinner,
    IonItem,
    IonLabel,
    IonRadio,
    IonRadioGroup,
    IonCheckbox,
    IonList,
    IonAvatar,
    IonBadge,
} from "@ionic/vue";
import { PatientService } from "@/services/patient_service";
import { Patient } from "@/interfaces/patient";
import { useDemographicsStore } from "@/stores/DemographicStore";
import { alertConfirmation, toastDanger } from "@/utils/Alerts";
import { printNpidLbl } from "@/apps/ART/Labels";

const route = useRoute();
const primaryInputRef = ref();
const primarySearchText = ref("");
const primaryLoading = ref(false);
const secondaryLoading = ref(false);
const secondarySearchText = ref("");
const primaryPatients = ref<any>([]);
const secondaryPatients = ref<any>([]);
const selectedSecondaryPatients = ref<any>([]);
const selectedPrimaryPatient = ref<any | null>(null);

const canMerge = computed(() => {
    return selectedPrimaryPatient.value && selectedSecondaryPatients.value.length > 0;
});

const hasSearchResults = computed(() => {
    return primaryPatients.value.length > 0 || secondaryPatients.value.length > 0;
});

const searchPatients = async (searchText: string, isPrimary: boolean = true) => {
    if (!searchText.trim()) return;

    const loading = isPrimary ? primaryLoading : secondaryLoading;
    const patientsRef = isPrimary ? primaryPatients : secondaryPatients;

    loading.value = true;
    patientsRef.value = [];

    try {
        const [givenName, familyName] = searchText.trim().split(" ");

        const searchParams = {
            ...(givenName && { given_name: givenName }),
            ...(familyName && { family_name: familyName }),
        };

        const patients = await PatientService.search(searchParams);

        patientsRef.value = await Promise.all(
            patients.map(async (patient: Patient, index: number) => {
                const patientData = useDemographicsStore();
                await patientData.setPatientRecord(patient);

                const patientService = new PatientService();

                return {
                    index,
                    id: patientService.getID(),
                    name: patientService.getFullName() || "Unknown",
                    docID: patientService.getDocID() || "NA",
                    birthdate: patientService.getBirthdate(),
                    arvNum: patientService.getArvNumber(),
                    npid: patientService.getNationalID(),
                    gender: patientService.getGender(),
                    homeDistrict: patientService.getHomeDistrict(),
                    homeVillage: patientService.getHomeVillage(),
                    currentDistrict: patientService.getCurrentDistrict(),
                    currentVillage: patientService.getCurrentVillage(),
                    isChecked: false,
                };
            })
        );
    } catch (error) {
        console.error("Patient search failed:", error);
        patientsRef.value = [];
    } finally {
        loading.value = false;
    }
};

const searchPrimary = () => {
    searchPatients(primarySearchText.value, true);
};

const searchSecondary = () => {
    selectedSecondaryPatients.value = [];
    searchPatients(secondarySearchText.value, false);
};

const selectPrimaryPatient = (patient: any) => {
    selectedPrimaryPatient.value = patient;
};

const toggleSecondaryPatient = (patient: any, isChecked: boolean) => {
    if (isChecked) {
        if (!selectedSecondaryPatients.value.find((p) => p.id === patient.id)) {
            selectedSecondaryPatients.value.push(patient);
        }
    } else {
        selectedSecondaryPatients.value = selectedSecondaryPatients.value.filter((p) => p.id !== patient.id);
    }
};

const isSecondaryPatientSelected = (patientId: string) => {
    return selectedSecondaryPatients.value.some((p) => p.id === patientId);
};

const formatDate = (dateString: string) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString();
};

const getGenderBadgeColor = (gender: string) => {
    return gender.toLowerCase() === "m" ? "primary" : "secondary";
};

const mergePatients = async () => {
    if (!canMerge.value) return;

    if (
        !(await alertConfirmation(
            `Are you sure you want to merge selected ${selectedSecondaryPatients.value.length} secondary patient(s) to merge with primary patient.`
        ))
    ) {
        return;
    }

    try {
        const patient = await PatientService.mergePatients({
            primary: {
                patient_id: selectedPrimaryPatient.value.id,
                doc_id: selectedPrimaryPatient.value.docID,
            },
            secondary: selectedSecondaryPatients.value.map((s: any) => ({
                patient_id: s.id,
                doc_id: s.docID,
            })),
        });

        await printNpidLbl(patient["patient_id"]);

        primaryPatients.value = [];
        secondaryPatients.value = [];
        selectedPrimaryPatient.value = [];
        selectedSecondaryPatients.value = [];

        primarySearchText.value = "";
        secondarySearchText.value = "";
    } catch (e) {
        toastDanger(`${e}`);
    }
};

const handleUrlQuery = () => {
    const queryParam = route.query.q as string;

    if (queryParam && queryParam.trim()) {
        primarySearchText.value = queryParam.trim();
        secondarySearchText.value = queryParam.trim();

        searchPatients(queryParam.trim(), true);
        searchPatients(queryParam.trim(), false);
    }
};

onMounted(() => {
    handleUrlQuery();

    if (!route.query.q && primaryInputRef.value) {
        setTimeout(() => {
            primaryInputRef.value.$el.setFocus();
        }, 200);
    }
});
</script>

<template>
    <IonPage>
        <Toolbar />
        <ActionButtons />
        <IonContent :fullscreen="true" class="main-content">
            <div class="full-height-container">
                <div class="centered-content">
                    <div class="header-section">
                        <div class="title-container">
                            <IonIcon :icon="swapHorizontalOutline" class="title-icon" />
                            <h1 class="page-title">Merge Clients</h1>
                        </div>
                        <p class="page-subtitle">Select primary and secondary patients to merge their records</p>
                    </div>

                    <div class="search-card" mode="ios">
                        <div class="search-content">
                            <IonRow class="search-row">
                                <IonCol class="search-col">
                                    <div class="patient-section primary-section">
                                        <div class="section-header">
                                            <IonIcon :icon="personOutline" class="section-icon primary-icon" />
                                            <span class="section-title">Primary Patient</span>
                                            <IonNote class="section-note">Main record</IonNote>
                                        </div>
                                        <div class="input-group">
                                            <IonInput
                                                ref="primaryInputRef"
                                                v-model="primarySearchText"
                                                label="Search primary patient"
                                                label-placement="floating"
                                                fill="outline"
                                                placeholder="Enter patient name or ID"
                                                class="patient-input"
                                                @keyup.enter="searchPrimary"
                                            />
                                            <IonButton
                                                fill="solid"
                                                color="primary"
                                                class="search-button"
                                                :disabled="primaryLoading || !primarySearchText.trim()"
                                                @click="searchPrimary"
                                            >
                                                <IonSpinner v-if="primaryLoading" name="crescent" />
                                                <IonIcon v-else :icon="searchOutline" />
                                            </IonButton>
                                        </div>
                                    </div>
                                </IonCol>

                                <IonCol class="search-col">
                                    <div class="patient-section secondary-section">
                                        <div class="section-header">
                                            <IonIcon :icon="personOutline" class="section-icon secondary-icon" />
                                            <span class="section-title">Secondary Patient</span>
                                            <IonNote class="section-note">Will be merged into primary</IonNote>
                                        </div>
                                        <div class="input-group">
                                            <IonInput
                                                v-model="secondarySearchText"
                                                label="Search secondary patient"
                                                label-placement="floating"
                                                fill="outline"
                                                placeholder="Enter patient name or ID"
                                                class="patient-input"
                                                @keyup.enter="searchSecondary"
                                            />
                                            <IonButton
                                                fill="solid"
                                                color="secondary"
                                                class="search-button"
                                                :disabled="secondaryLoading || !secondarySearchText.trim()"
                                                @click="searchSecondary"
                                            >
                                                <IonSpinner v-if="secondaryLoading" name="crescent" />
                                                <IonIcon v-else :icon="searchOutline" />
                                            </IonButton>
                                        </div>
                                    </div>
                                </IonCol>
                            </IonRow>
                        </div>
                    </div>

                    <IonCard class="preview-card" mode="ios">
                        <IonCardContent class="preview-content">
                            <div class="preview-header">
                                <h2 class="preview-title">Patient Search Results</h2>
                                <IonNote class="preview-note">Select patients from search results to preview merge</IonNote>
                            </div>

                            <div v-if="hasSearchResults" class="results-container">
                                <IonRow>
                                    <IonCol v-if="primaryPatients.length > 0">
                                        <div class="results-section primary-results">
                                            <h3 class="results-title">
                                                <IonIcon :icon="personOutline" class="results-icon primary-icon" />
                                                Primary Patient Options
                                            </h3>
                                            <IonRadioGroup
                                                :value="selectedPrimaryPatient?.id"
                                                @ionChange="selectPrimaryPatient(primaryPatients.find((p) => p.id === $event.detail.value))"
                                            >
                                                <IonList class="patient-list scrollable-list ion-padding">
                                                    <IonItem
                                                        v-for="patient in primaryPatients"
                                                        :key="patient.id"
                                                        class="patient-item"
                                                        lines="none"
                                                        :class="{ selected: selectedPrimaryPatient?.id === patient.id }"
                                                    >
                                                        <IonAvatar class="patient-avatar">
                                                            <div class="avatar-placeholder">
                                                                {{ patient.name.charAt(0) }}
                                                            </div>
                                                        </IonAvatar>
                                                        <IonLabel>
                                                            <h3 class="patient-name">{{ patient.name }}</h3>
                                                            <p class="patient-details">
                                                                <IonBadge :color="getGenderBadgeColor(patient.gender)" class="gender-badge">
                                                                    {{ patient.gender }}
                                                                </IonBadge>
                                                                DOB: {{ formatDate(patient.birthdate) }}
                                                            </p>
                                                            <p class="patient-ids">ID: {{ patient.id }} | ARV: {{ patient.arvNum || "N/A" }}</p>
                                                            <p class="patient-location">
                                                                {{ patient.currentDistrict }}, {{ patient.currentVillage }}
                                                            </p>
                                                        </IonLabel>
                                                        <IonRadio :value="patient.id" color="primary">
                                                            <IonIcon
                                                                v-if="selectedPrimaryPatient?.id === patient.id"
                                                                :icon="checkmarkCircleOutline"
                                                                color="primary"
                                                                class="selected-icon"
                                                            />
                                                        </IonRadio>
                                                    </IonItem>
                                                </IonList>
                                            </IonRadioGroup>
                                        </div>
                                    </IonCol>

                                    <IonCol v-if="secondaryPatients.length > 0">
                                        <div class="results-section secondary-results">
                                            <h3 class="results-title">
                                                <IonIcon :icon="personOutline" class="results-icon secondary-icon" />
                                                Secondary Patient Options
                                                <IonBadge v-if="selectedSecondaryPatients.length > 0" color="secondary" class="selection-count">
                                                    {{ selectedSecondaryPatients.length }} selected
                                                </IonBadge>
                                            </h3>
                                            <IonList class="patient-list scrollable-list ion-padding">
                                                <IonItem
                                                    v-for="patient in secondaryPatients"
                                                    :key="patient.id"
                                                    class="patient-item"
                                                    lines="none"
                                                    :class="{ selected: isSecondaryPatientSelected(patient.id) }"
                                                >
                                                    <IonAvatar class="patient-avatar">
                                                        <div class="avatar-placeholder">
                                                            {{ patient.name.charAt(0) }}
                                                        </div>
                                                    </IonAvatar>
                                                    <IonLabel>
                                                        <h3 class="patient-name">{{ patient.name }}</h3>
                                                        <p class="patient-details">
                                                            <IonBadge :color="getGenderBadgeColor(patient.gender)" class="gender-badge">
                                                                {{ patient.gender }}
                                                            </IonBadge>
                                                            DOB: {{ formatDate(patient.birthdate) }}
                                                        </p>
                                                        <p class="patient-ids">ID: {{ patient.id }} | ARV: {{ patient.arvNum || "N/A" }}</p>
                                                        <p class="patient-location">{{ patient.currentDistrict }}, {{ patient.currentVillage }}</p>
                                                    </IonLabel>
                                                    <IonCheckbox
                                                        :checked="isSecondaryPatientSelected(patient.id)"
                                                        color="secondary"
                                                        @ionChange="toggleSecondaryPatient(patient, $event.detail.checked)"
                                                    />
                                                </IonItem>
                                            </IonList>
                                        </div>
                                    </IonCol>
                                </IonRow>
                            </div>

                            <div v-else class="preview-placeholder">
                                <img
                                    src="../assets/images/merge-patient-search-placeholder.png"
                                    alt="Patient search placeholder"
                                    class="placeholder-image"
                                />
                            </div>
                        </IonCardContent>
                    </IonCard>

                    <div class="action-section">
                        <IonButton expand="block" size="large" color="success" class="merge-button" :disabled="!canMerge" @click="mergePatients">
                            <IonIcon :icon="swapHorizontalOutline" />
                            Merge Selected Patients
                            <IonBadge v-if="selectedSecondaryPatients.length > 1" color="light" class="merge-count">
                                1 → {{ selectedSecondaryPatients.length }}
                            </IonBadge>
                        </IonButton>
                        <IonNote class="action-note">
                            {{
                                canMerge
                                    ? `Ready to merge ${selectedSecondaryPatients.length} secondary patient(s) into primary patient`
                                    : "Select one primary patient and at least one secondary patient to enable merge"
                            }}
                        </IonNote>
                    </div>
                </div>
            </div>
        </IonContent>
    </IonPage>
</template>

<style lang="scss" scoped>
@import url("../css/merge-patients.scss");
</style>
