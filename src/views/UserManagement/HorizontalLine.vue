<template>
    <div 
      :class="containerClasses"
      :style="containerStyles"
    >
      <div 
        :class="lineClasses"
        :style="lineStyles"
      ></div>
      <slot v-if="$slots.default" class="divider-content">
        <span :class="contentClasses">
          <slot></slot>
        </span>
      </slot>
    </div>
  </template>
  
  <script setup lang="ts">
  import { computed, defineProps, withDefaults, useSlots } from 'vue'

  interface Props {
    color?: string
    thickness?: number
    variant?: 'solid' | 'dashed' | 'dotted' | 'gradient' | 'glow'
    spacing?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
    animated?: boolean
    opacity?: number
    gradientFrom?: string
    gradientTo?: string
    glowColor?: string
  }

  const slots = useSlots() as any
  
  const props = withDefaults(defineProps<Props>(), {
    color: 'var(--ion-color-medium)',
    thickness: 1,
    variant: 'solid',
    spacing: 'md',
    animated: false,
    opacity: 1,
    gradientFrom: '#667eea',
    gradientTo: '#764ba2',
    glowColor: '#667eea'
  })
  
  const containerClasses = computed(() => [
    'modern-divider',
    `spacing-${props.spacing}`,
    {
      'has-content': !!slots.default,
      'animated': props.animated
    }
  ])
  
  const containerStyles = computed(() => ({
    '--divider-color': props.color,
    '--divider-thickness': `${props.thickness}px`,
    '--divider-opacity': props.opacity,
    '--gradient-from': props.gradientFrom,
    '--gradient-to': props.gradientTo,
    '--glow-color': props.glowColor
  }))
  
  const lineClasses = computed(() => [
    'divider-line',
    `variant-${props.variant}`
  ])
  
  const lineStyles = computed(() => {
    const baseStyles: Record<string, string> = {}
    
    if (props.variant === 'gradient') {
      baseStyles.background = `linear-gradient(90deg, ${props.gradientFrom}, ${props.gradientTo})`
    } else if (props.variant === 'glow') {
      baseStyles.background = props.color
      baseStyles.boxShadow = `0 0 10px ${props.glowColor}, 0 0 20px ${props.glowColor}40`
    }
    
    return baseStyles
  })
  
  const contentClasses = computed(() => [
    'divider-text',
    `text-${props.spacing}`
  ])
  </script>
  
  <style scoped>
  .modern-divider {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
  }
  
  /* Spacing variants */
  .spacing-xs {
    margin: 8px 0;
  }
  
  .spacing-sm {
    margin: 12px 0;
  }
  
  .spacing-md {
    margin: 16px 0;
  }
  
  .spacing-lg {
    margin: 24px 0;
  }
  
  .spacing-xl {
    margin: 32px 0;
  }
  
  /* Base line styles */
  .divider-line {
    flex: 1;
    height: var(--divider-thickness);
    opacity: var(--divider-opacity);
    transition: all 0.3s ease;
  }
  
  /* Variant styles */
  .variant-solid {
    background-color: var(--divider-color);
  }
  
  .variant-dashed {
    border-top: var(--divider-thickness) dashed var(--divider-color);
    background: none;
    height: 0;
  }
  
  .variant-dotted {
    border-top: var(--divider-thickness) dotted var(--divider-color);
    background: none;
    height: 0;
  }
  
  .variant-gradient {
    border-radius: calc(var(--divider-thickness) / 2);
  }
  
  .variant-glow {
    border-radius: calc(var(--divider-thickness) / 2);
  }
  
  /* Content with divider */
  .has-content .divider-line {
    flex: 1;
  }
  
  .has-content .divider-line:first-child {
    margin-right: 16px;
  }
  
  .has-content .divider-line:last-child {
    margin-left: 16px;
  }
  
  .divider-content {
    display: flex;
    align-items: center;
  }
  
  .divider-text {
    color: var(--ion-color-medium);
    font-weight: 500;
    white-space: nowrap;
    padding: 0 8px;
    background: var(--ion-background-color, #fff);
    position: relative;
    z-index: 1;
  }
  
  .text-xs {
    font-size: 0.75rem;
  }
  
  .text-sm {
    font-size: 0.875rem;
  }
  
  .text-md {
    font-size: 1rem;
  }
  
  .text-lg {
    font-size: 1.125rem;
  }
  
  .text-xl {
    font-size: 1.25rem;
  }
  
  /* Animation */
  .animated .divider-line {
    position: relative;
    overflow: hidden;
  }
  
  .animated .variant-solid::before,
  .animated .variant-gradient::before,
  .animated .variant-glow::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.4),
      transparent
    );
    animation: shimmer 2s infinite;
  }
  
  @keyframes shimmer {
    0% {
      left: -100%;
    }
    100% {
      left: 100%;
    }
  }
  
  /* Dark mode support */
  @media (prefers-color-scheme: dark) {
    .divider-text {
      background: var(--ion-background-color, #000);
    }
  }
  
  /* Hover effects */
  .modern-divider:hover .divider-line {
    opacity: calc(var(--divider-opacity) + 0.2);
  }
  
  .modern-divider:hover .variant-glow {
    box-shadow: 0 0 15px var(--glow-color), 0 0 30px var(--glow-color)60;
  }
  </style>