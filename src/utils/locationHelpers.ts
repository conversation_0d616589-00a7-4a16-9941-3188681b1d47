import type { Ref } from "vue";

export interface LocationFormData {
    current_district?: any;
    current_traditional_authority?: any;
    current_village?: any;
}

export interface FormRef {
    setFormValue: (field: string, value: any[]) => void;
}

/**
 * Copies current location data to home location fields
 */
export const copyCurrentToHome = (currentLocationFormValues: Ref<LocationFormData> | undefined, formRef: Ref<FormRef> | undefined): void => {
    if (!currentLocationFormValues?.value || !formRef?.value) return;

    const { current_district, current_traditional_authority, current_village } = currentLocationFormValues.value;

    formRef.value.setFormValue("home_district", current_district);
    formRef.value.setFormValue("home_traditional_authority", current_traditional_authority);
    formRef.value.setFormValue("home_village", current_village);
};

/**
 * Clears all home location fields
 */
export const clearHomeLocation = (formRef: Ref<FormRef> | undefined): void => {
    if (!formRef?.value) return;

    formRef.value.setFormValue("home_district", []);
    formRef.value.setFormValue("home_traditional_authority", []);
    formRef.value.setFormValue("home_village", []);
};

/**
 * Handles district selection change
 */
export const handleDistrictChange = (value: any, selectedDistrictId: Ref<string | null>, formRef: Ref<FormRef> | undefined): void => {
    selectedDistrictId.value = value?.district_id || null;

    // Clear dependent fields
    if (formRef?.value) {
        formRef.value.setFormValue("home_traditional_authority", []);
        formRef.value.setFormValue("home_village", []);
    }
};

/**
 * Handles traditional authority selection change
 */
export const handleTAChange = (value: any, selectedTraditionalAuthorityId: Ref<string | null>, formRef: Ref<FormRef> | undefined): void => {
    selectedTraditionalAuthorityId.value = value?.traditional_authority_id || null;

    // Clear dependent village field
    formRef?.value?.setFormValue("home_village", []);
};

/**
 * Determines if a field should be disabled based on form data and available options
 */
export const isFieldDisabled = (data: any, optionsArray: any[]): boolean => {
    return data?.same_as_current ? false : !optionsArray?.length;
};

/**
 * Creates the onChange handler for the "same as current" checkbox
 */
export const createSameAsCurrentHandler = (currentLocationFormValues: Ref<LocationFormData> | undefined, formRef: Ref<FormRef> | undefined) => {
    return (value: boolean): void => {
        // Use setTimeout to ensure form updates happen after current render
        setTimeout(() => {
            value ? copyCurrentToHome(currentLocationFormValues, formRef) : clearHomeLocation(formRef);
        }, 0);
    };
};
