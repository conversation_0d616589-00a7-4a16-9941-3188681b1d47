import { PatientService } from "@/services/patient_service";
import { ProgramService } from "@/services/program_service";
import { NotFoundError, Service } from "@/services/service";

const isTaskPermitted = async (taskName: string) => {
    const adminTasks = await getAdminSetUserActivities();
    if (!adminTasks.length) {
        return true; // If no admin tasks are set, allow all tasks
    }
    return adminTasks.some((permitted: string) => {
        return permitted.toLowerCase() === `${taskName}`.toLowerCase();
    });
};

const isTaskUserSelected = async (taskName: string) => {
    const userActivities = await getUserSelectedActivities();
    if (!userActivities.length) {
        return true; // If no user activities are set, allow all tasks
    }
    return userActivities.some((activity: string) => {
        return activity.toLowerCase() === `${taskName}`.toLowerCase();
    });
};

const getAdminSetUserActivities = async () => {
    try {
        const data = await Service.getJson("user_properties", {
            property: "ART_Admin_assigned_activities",
        });
        return data.property_value.split(",");
    } catch (e) {
        return [];
    }
};

const getUserSelectedActivities = async () => {
    try {
        const data = await Service.getJson("user_properties", {
            property: "activities",
        });
        return data.property_value.split(",");
    } catch (e) {
        return [];
    }
};

const getCanEditActivities = async () => {
    try {
        return (await ProgramService.getJson("user_properties", { property: "lock_user_to_art_activities" })).property_value;
    } catch (e) {
        if (e instanceof NotFoundError) {
            return "Yes";
        } else {
            return "_error_";
        }
    }
};

async function canDoActivity(activity: string) {
    const activities = await getUserSelectedActivities();
    const status = await getCanEditActivities();
    if (!/yes/i.test(status) && !activities.includes(activity)) {
        return false;
    }
    return true;
}

function isEncounterAfterDeath(p: any) {
    return /died/i.test(`${p?.program?.outcome}`) && new Date(PatientService.getSessionDate()) >= new Date(p?.program?.startDate);
}

export { isTaskUserSelected, isTaskPermitted, getAdminSetUserActivities, getUserSelectedActivities, getCanEditActivities, canDoActivity, isEncounterAfterDeath };
