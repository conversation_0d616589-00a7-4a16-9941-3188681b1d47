export default [
    {
        categories: [],
        concept_id: 7759,
        name: "Workstation location",
    },
    {
        categories: ["tb_treatment"],
        concept_id: 10687,
        name: "TB treatment start date",
    },
    {
        categories: ["tb_treatment"],
        concept_id: 10686,
        name: "TB treatment period",
    },
    {
        categories: ["hiv_viral_load_reasons"],
        concept_id: 10609,
        name: "Follow up after Low Level Viremia",
    },
    {
        categories: ["hiv_viral_load_reasons"],
        concept_id: 10610,
        name: "Follow up after High Viral Load",
    },
    {
        categories: [],
        concept_id: 8259,
        name: "Routine TB Screening",
    },
    {
        categories: [],
        concept_id: 7459,
        name: "TB Status",
    },
    {
        categories: [],
        concept_id: 7454,
        name: "TB Not Suspected",
        shortName: "Nosup",
    },
    {
        categories: [],
        concept_id: 7455,
        name: "TB Suspected",
    },
    {
        categories: [],
        concept_id: 6195,
        name: "Why does the woman not use birth control",
    },
    {
        categories: [],
        concept_id: 6328,
        name: "New",
    },
    {
        categories: [],
        concept_id: 7977,
        name: "He<PERSON><PERSON>hage",
    },
    {
        categories: [],
        concept_id: 7156,
        name: "<PERSON><PERSON><PERSON><PERSON>",
    },
    {
        categories: [],
        concept_id: 8758,
        name: "Episiotomy",
    },
    {
        categories: [],
        concept_id: 228,
        name: "APH",
    },
    {
        categories: [],
        concept_id: 230,
        name: "PPH",
    },
    {
        categories: [],
        concept_id: 2895,
        name: "Alive",
    },
    {
        categories: [],
        concept_id: 9656,
        name: "Previous HIV Test Results",
    },
    {
        categories: [],
        concept_id: 2473,
        name: "B-HCG",
    },
    {
        categories: [],
        concept_id: 9655,
        name: "Previous HIV Test Done",
    },
    {
        categories: [],
        concept_id: 9657,
        name: "Previous HIV Test Date",
    },
    {
        categories: [],
        concept_id: 3340,
        name: "Currently taking alcohol",
    },
    {
        categories: [],
        concept_id: 1057,
        name: "Never Married",
    },
    {
        categories: [],
        concept_id: 5555,
        name: "Married",
    },
    {
        categories: [],
        concept_id: 1056,
        name: "Seperated",
    },
    {
        categories: [],
        concept_id: 1058,
        name: "Divorced",
    },
    {
        categories: [],
        concept_id: 6686,
        name: "Ultrasound",
    },
    {
        categories: [],
        concept_id: 7433,
        name: "Presentation",
    },
    {
        categories: [],
        concept_id: 7979,
        name: "Fetal Heart Beat",
    },
    {
        categories: [],
        concept_id: 9637,
        name: "Fetal movement heard",
    },
    {
        categories: [],
        concept_id: 9562,
        name: "Fetal movement felt",
    },
    {
        categories: [],
        concept_id: 7835,
        name: "Fundus",
    },
    {
        categories: [],
        concept_id: 7837,
        name: "Position",
    },
    {
        categories: [],
        concept_id: 7142,
        name: "Multiple gestation",
    },
    {
        categories: [],
        concept_id: 7919,
        name: "LIQUOR",
    },
    {
        categories: [],
        concept_id: 9563,
        name: "Last Fetal movement felt",
    },
    {
        categories: [],
        concept_id: 7836,
        name: "Lie",
    },
    {
        categories: [],
        concept_id: 9654,
        name: "Fetal heart movement seen",
    },
    {
        categories: [],
        concept_id: 7839,
        name: "Fetal heart rate",
    },
    {
        categories: ["anc_medical_history"],
        concept_id: 5,
        name: "Asthma",
    },
    {
        categories: ["anc_medical_history"],
        concept_id: 155,
        name: "Epilepsy",
    },
    {
        categories: ["anc_medical_history"],
        concept_id: 174,
        name: "Sexually transmitted infection",
    },
    {
        categories: ["anc_medical_history"],
        concept_id: 1063,
        name: "Blood transfusion",
    },
    {
        categories: ["anc_medical_history"],
        concept_id: 6033,
        name: "Renal disease",
    },
    {
        categories: ["anc_medical_history"],
        concept_id: 7978,
        name: "Spine/Leg Deformity",
    },
    {
        categories: ["anc_medical_history"],
        concept_id: 8809,
        name: "Hypertension",
    },
    {
        categories: [],
        concept_id: 7439,
        name: "Procedure done",
    },
    {
        categories: ["anc_surgical_history"],
        concept_id: 1171,
        name: "Caesarean section",
    },
    {
        categories: ["anc_surgical_history"],
        concept_id: 1719,
        name: "Tubal ligation",
    },
    {
        categories: ["anc_surgical_history"],
        concept_id: 3593,
        name: "Biopsy",
    },
    {
        categories: ["anc_surgical_history", "reason_for_no_cxca"],
        concept_id: 5276,
        name: "Hysterectomy",
    },
    {
        categories: ["anc_surgical_history"],
        concept_id: 7440,
        name: "Uterine evacuation",
    },
    {
        categories: ["anc_surgical_history"],
        concept_id: 7441,
        name: "Exam under anaesthesia",
    },
    {
        categories: ["anc_surgical_history"],
        concept_id: 7443,
        name: "Total abdominal hysterectomy +/- adnexectomy",
    },
    {
        categories: ["anc_surgical_history"],
        concept_id: 7444,
        name: "Subtotal abdominal hysterectomy +/- adnexectomy",
    },
    {
        categories: ["anc_surgical_history"],
        concept_id: 7462,
        name: "Suturing of cervical or vaginal injury under anaesthesia",
    },
    {
        categories: ["anc_surgical_history"],
        concept_id: 7469,
        name: "Vaginal Reconstruction",
    },
    {
        categories: ["anc_surgical_history"],
        concept_id: 7470,
        name: "Exploratory laparatomy +/- adnexectomy",
    },
    {
        categories: ["anc_surgical_history"],
        concept_id: 7471,
        name: "Cauterization of vulva or perineal warts",
    },
    {
        categories: ["anc_surgical_history"],
        concept_id: 7472,
        name: "Marsupilisation",
    },
    {
        categories: ["anc_surgical_history"],
        concept_id: 7474,
        name: "Salpingectomy",
    },
    {
        categories: ["anc_surgical_history"],
        concept_id: 7475,
        name: "Dilation and curettage",
    },
    {
        categories: ["anc_medical_history", "anc_surgical_history"],
        concept_id: 7477,
        name: "Fistula repair",
    },
    {
        categories: ["anc_surgical_history"],
        concept_id: 7478,
        name: "Myomectomy",
    },
    {
        categories: ["anc_surgical_history"],
        concept_id: 7480,
        name: "Hysterescopy",
    },
    {
        categories: ["anc_surgical_history"],
        concept_id: 7481,
        name: "Cystoscopy",
    },
    {
        categories: ["anc_surgical_history"],
        concept_id: 7482,
        name: "Vulvectomy",
    },
    {
        categories: ["anc_surgical_history"],
        concept_id: 8380,
        name: "Suturing of perineum",
    },
    {
        categories: ["anc_surgical_history"],
        concept_id: 8381,
        name: "Repair of uterus",
    },
    {
        categories: ["anc_surgical_history"],
        concept_id: 8765,
        name: "Cystectomy",
    },
    {
        categories: ["anc_surgical_history"],
        concept_id: 8766,
        name: "Ceclage",
    },
    {
        categories: ["anc_surgical_history"],
        concept_id: 8767,
        name: "Bilateral tubal ligation",
    },
    {
        categories: ["anc_surgical_history"],
        concept_id: 8773,
        name: "Excision",
    },
    {
        categories: ["anc_surgical_history"],
        concept_id: 9058,
        name: "Evacuation/Manual Vacuum Aspiration",
    },
    {
        categories: ["anc_surgical_history"],
        concept_id: 9304,
        name: "Polypectomy",
    },
    {
        categories: ["anc_surgical_history"],
        concept_id: 9305,
        name: "Decapitation",
    },
    {
        categories: ["anc_surgical_history"],
        concept_id: 9306,
        name: "Abdominal Washout",
    },
    {
        categories: ["anc_surgical_history"],
        concept_id: 9307,
        name: "Vaginal hysterectomy",
    },
    {
        categories: ["anc_surgical_history"],
        concept_id: 9308,
        name: "Gaped episiotomy repair",
    },
    {
        categories: ["anc_surgical_history"],
        concept_id: 9309,
        name: "Macdonald suture",
    },
    {
        categories: ["anc_surgical_history"],
        concept_id: 9314,
        name: "Oophorectomy",
    },
    {
        categories: [],
        concept_id: 7124,
        name: "Tetanus Diphtheria",
    },
    {
        categories: [],
        concept_id: 6408,
        name: "Other conditions",
    },
    {
        categories: [],
        concept_id: 10546,
        name: "Punch Biopsy",
    },
    {
        categories: [],
        concept_id: 10547,
        name: "LLETZ sample",
    },
    {
        categories: [],
        concept_id: 1115,
        name: "Normal",
    },
    {
        categories: [],
        concept_id: 10551,
        name: "CIN 1",
    },
    {
        categories: [],
        concept_id: 10552,
        name: "CIN 2",
    },
    {
        categories: [],
        concept_id: 10553,
        name: "CIN 3",
    },
    {
        categories: [],
        concept_id: 10554,
        name: "Carcinoma in Situ",
    },
    {
        categories: [],
        concept_id: 2588,
        name: "Invasive cancer of cervix",
    },
    {
        categories: [],
        concept_id: 10550,
        name: "Benign cervical warts",
    },
    {
        categories: [],
        concept_id: 1107,
        name: "Not available",
    },
    {
        categories: [],
        concept_id: 7918,
        name: "Bleeding",
    },
    {
        categories: [],
        concept_id: 5276,
        name: "Hysterectomy",
    },
    {
        categories: [],
        concept_id: 10521,
        name: "Cryotherapy",
    },
    {
        categories: [],
        concept_id: 10002,
        name: "Leep",
    },
    {
        categories: [],
        concept_id: 9053,
        name: "Palliative Care",
    },
    {
        categories: [],
        concept_id: 10560,
        name: "LLETZ",
    },
    {
        categories: [],
        concept_id: 10557,
        name: "Conisation",
    },
    {
        categories: [],
        concept_id: 9996,
        name: "Thermocoagulation",
    },
    {
        categories: [],
        concept_id: 10549,
        name: "Chronic cervicitis",
    },
    {
        categories: [],
        concept_id: 3580,
        name: "Patient refused",
    },
    {
        categories: [],
        concept_id: 10556,
        name: "Hysyrectomy",
    },
    {
        categories: [],
        concept_id: 10558,
        name: "Trachelectomy",
    },
    {
        categories: [],
        concept_id: 3626,
        name: "Discharged",
    },
    {
        categories: [],
        concept_id: 8882,
        name: "Continue follow-up",
    },
    {
        categories: [],
        concept_id: 10559,
        name: "No Dysplasia/Cancer",
    },
    {
        categories: [],
        concept_id: 1742,
        name: "Patient died",
    },
    {
        categories: [],
        concept_id: 8739,
        name: "Cervical cancer stage 1",
    },
    {
        categories: [],
        concept_id: 8787,
        name: "Cervical cancer stage 2",
    },
    {
        categories: [],
        concept_id: 8788,
        name: "Cervical cancer stage 3",
    },
    {
        categories: [],
        concept_id: 8740,
        name: "Cervical cancer stage 4",
    },
    {
        categories: [],
        concept_id: 10041,
        name: "VIA Negative",
    },
    {
        categories: [],
        concept_id: 10032,
        name: "Suspect Cancer",
    },
    {
        categories: [],
        concept_id: 10023,
        name: "PAP Smear Normal",
    },
    {
        categories: [],
        concept_id: 10025,
        name: "HPV negative",
    },
    {
        categories: [],
        concept_id: 10028,
        name: "No visible Lesion",
    },
    {
        categories: [],
        concept_id: 6537,
        name: "Other Gynae",
    },
    {
        categories: [],
        concept_id: 10042,
        name: "VIA Positive",
    },
    {
        categories: [],
        concept_id: 10024,
        name: "PAP Smear Abnormal",
    },
    {
        categories: [],
        concept_id: 10026,
        name: "HPV Positive",
    },
    {
        categories: [],
        concept_id: 10027,
        name: "Visible Lesion",
    },
    {
        categories: [],
        concept_id: 174,
        name: "STI",
    },
    {
        categories: [],
        concept_id: 149,
        name: "Cervicitis",
    },
    {
        categories: [],
        name: "Result Given to Client",
        concept_id: 9764,
    },
    {
        categories: ["art_extra_medication_order"],
        name: "INH 300 / RFP 300 (3HP)",
        concept_id: 10565,
    },
    {
        categories: [],
        concept_id: 10563,
        name: "Drug received from previous facility",
    },
    {
        categories: [],
        concept_id: 10564,
        name: "Date drug received from previous facility",
    },
    {
        categories: [],
        concept_id: 6397,
        name: "Height for age percent of median",
    },
    {
        categories: [],
        concept_id: 6969,
        name: "Refer to clinician",
    },
    {
        categories: [],
        concept_id: 10031,
        name: "Further Investigation and Management",
    },
    {
        categories: [],
        concept_id: 9066,
        name: "Cancer of cervix",
    },
    {
        categories: [],
        concept_id: 10030,
        name: "Large Lesion (Greater than 75 percent)",
    },
    {
        categories: [],
        concept_id: 1739,
        name: "Referral reason",
    },
    {
        categories: [],
        concept_id: 9514,
        name: "VIA Results",
    },
    {
        categories: [],
        concept_id: 9522,
        name: "Patient went for VIA?",
    },
    {
        concept_id: 2690,
        name: "TB treatment",
    },
    {
        categories: [],
        concept_id: 7458,
        name: "Confirmed TB on treatment",
    },
    {
        categories: [],
        concept_id: 7456,
        name: "Confirmed TB NOT on treatment",
    },
    {
        categories: [],
        concept_id: 9195,
        name: "Discuss with spouse",
    },
    {
        categories: [],
        concept_id: 7951,
        name: "Other side effect",
    },
    {
        categories: [],
        concept_id: 7755,
        name: "Malawi ART side effects",
    },
    {
        categories: [],
        concept_id: 9994,
        name: "CxCa test date",
    },
    {
        categories: [],
        concept_id: 10008,
        name: "Reason for NOT offering CxCa",
    },
    {
        categories: [],
        concept_id: 9992,
        name: "Offer CxCa",
    },
    {
        categories: ["reason_for_no_cxca"],
        concept_id: 8387,
        name: "Not due for screening",
    },
    {
        categories: ["reason_for_no_cxca"],
        concept_id: 10007,
        name: "Client preferred counseling",
    },
    {
        categories: ["reason_for_no_cxca"],
        concept_id: 1175,
        name: "Not applicable",
    },
    {
        categories: ["reason_for_no_cxca"],
        concept_id: 3580,
        name: "Patient refused",
    },
    {
        categories: ["reason_for_no_cxca"],
        concept_id: 10001,
        name: "Chemotherapy",
    },
    {
        categories: ["reason_for_no_cxca"],
        concept_id: 10543,
        name: "Services not available",
    },
    {
        categories: ["reason_for_no_cxca"],
        concept_id: 10544,
        name: "Provider not available",
    },
    {
        categories: [],
        concept_id: 7215,
        name: "Other (Specify)",
    },
    {
        categories: [],
        concept_id: 1618,
        name: "Family planning",
    },
    {
        categories: [],
        concept_id: 374,
        name: "Method of family planning",
    },
    {
        categories: [],
        concept_id: 2431,
        name: "Other reason for not seeking services",
    },
    {
        categories: [],
        concept_id: 2592,
        name: "Clinician notes",
    },
    {
        categories: [],
        concept_id: 6986,
        name: "Transferred",
    },
    {
        categories: [],
        concept_id: 1588,
        name: "Previous TB treatment history",
    },
    {
        categories: ["bp_measures"],
        concept_id: 5085,
        name: "Systolic blood pressure",
    },
    {
        categories: ["bp_measures"],
        concept_id: 5086,
        name: "Diastolic blood pressure",
    },
    {
        categories: [],
        concept_id: 7054,
        name: "Art clinic location",
    },
    {
        categories: [],
        concept_id: 8677,
        name: "Specific presenting complaint",
    },
    {
        categories: [],
        concept_id: 8578,
        name: "Presenting complaint",
    },
    {
        categories: [],
        concept_id: 10319,
        name: "Presenting complaint group",
    },
    {
        categories: [],
        concept_id: 6266,
        name: "Trauma",
    },
    {
        categories: [],
        concept_id: 7027,
        name: "General",
    },
    {
        categories: [],
        concept_id: 7411,
        name: "ILI",
    },
    {
        categories: [],
        concept_id: 7644,
        name: "Respiratory",
    },
    {
        categories: [],
        concept_id: 7661,
        name: "Gastrointestinal",
    },
    {
        categories: [],
        concept_id: 9546,
        name: "Haema",
    },
    {
        categories: [],
        concept_id: 9551,
        name: "Nephro",
    },
    {
        categories: [],
        concept_id: 9560,
        name: "Cardiovascular",
    },
    {
        categories: ["art_patient_type"],
        order: 1,
        concept_id: 7572,
        name: "New patient",
    },
    {
        categories: ["art_patient_type"],
        order: 2,
        concept_id: 10522,
        name: "Emergency supply",
    },
    {
        categories: ["risk factors", "anc_medical_history"],
        concept_id: 3720,
        name: "Diabetes",
    },
    {
        categories: ["risk factors"],
        concept_id: 7623,
        name: "Chronic kidney disease",
    },
    {
        categories: ["risk factors"],
        concept_id: 9499,
        name: "Past history of IHD or CVD",
    },
    {
        categories: ["risk factors"],
        concept_id: 9501,
        name: "First degree relative with IHD or CVD <65",
    },
    {
        categories: ["risk factors"],
        concept_id: 3339,
        name: "patient smokes",
    },
    {
        categories: ["risk factors"],
        concept_id: 2318,
        name: "Does the patient drink alcohol?",
    },
    {
        categories: [],
        concept_id: 9675,
        name: "Referral",
    },
    {
        categories: [],
        concept_id: 1054,
        name: "Marital status",
    },
    {
        categories: [],
        concept_id: 8366,
        name: "Religion",
    },
    {
        categories: [],
        concept_id: 6541,
        name: "Referred",
    },
    {
        categories: [],
        concept_id: 8717,
        name: "Specialist clinic",
    },
    {
        categories: [],
        concept_id: 6734,
        name: "Admit to ward",
    },
    {
        categories: [],
        concept_id: 9674,
        name: "Re-visiting",
    },
    {
        categories: [],
        concept_id: 3753,
        name: "HIV status",
    },
    {
        categories: [],
        concept_id: 7878,
        name: "HIV test location",
    },
    {
        categories: [],
        concept_id: 7409,
        name: "Qech outpatient diagnosis list",
    },
    {
        categories: ["anc_diagnosis"],
        concept_id: 123,
        name: "Malaria",
    },
    {
        categories: ["anc_diagnosis"],
        concept_id: 3,
        name: "Anaemia",
    },
    {
        categories: ["anc_diagnosis"],
        concept_id: 7941,
        name: "Pre-eclampsia",
    },
    {
        categories: ["anc_diagnosis"],
        concept_id: 9665,
        name: "Vaginal bleeding",
    },
    {
        categories: ["anc_diagnosis"],
        concept_id: 9664,
        name: "Early rupture of membranes",
    },
    {
        categories: ["anc_diagnosis"],
        concept_id: 7883,
        name: "Premature labour",
    },
    {
        categories: ["anc_diagnosis"],
        concept_id: 43,
        name: "Pneumonia",
    },
    {
        categories: [],
        concept_id: 9227,
        name: "Malaria Test Result",
    },
    {
        categories: [],
        concept_id: 703,
        name: "Positive",
    },
    {
        categories: [],
        concept_id: 9436,
        name: "Inconclusive",
    },
    {
        categories: [],
        concept_id: 6698,
        name: "Trace",
    },
    {
        categories: [],
        concept_id: 664,
        name: "Negative",
    },
    {
        categories: [],
        concept_id: 6542,
        name: "Primary diagnosis",
    },
    {
        categories: [],
        concept_id: 6543,
        name: "Secondary diagnosis",
    },
    {
        categories: [],
        concept_id: 2688,
        name: "Clinical notes construct",
    },
    {
        categories: [],
        concept_id: 1837,
        name: "HIV test date",
    },
    {
        categories: [],
        concept_id: 6189,
        name: "Type of visit",
    },
    {
        categories: [],
        concept_id: 3003,
        name: "Transfer to",
    },
    {
        categories: [],
        concept_id: 7414,
        name: "Transfer from",
    },
    {
        categories: [],
        concept_id: 1744,
        name: "Patient transferred out",
    },
    {
        categories: [],
        concept_id: 9144,
        name: "Repeat / Missing",
    },
    {
        categories: [],
        concept_id: 3280,
        name: "Targeted",
    },
    {
        categories: [],
        concept_id: 1345,
        name: "Confirmatory",
    },
    {
        categories: [],
        concept_id: 6368,
        name: "Stat",
    },
    {
        categories: [],
        concept_id: 432,
        name: "Routine",
    },
    {
        categories: [],
        concept_id: 730,
        name: "CD4 percent",
    },
    {
        categories: [],
        concept_id: 2552,
        name: "Agrees to followup",
    },
    {
        categories: [],
        concept_id: 7882,
        name: "Confirmatory HIV test date",
    },
    {
        categories: [],
        concept_id: 2516,
        name: "Date ART started",
    },
    {
        categories: [],
        concept_id: 1499,
        name: "Drug start date",
    },
    {
        categories: [],
        concept_id: 7750,
        name: "Location of ART initiation",
    },
    {
        categories: [],
        concept_id: 6394,
        name: "Has the patient taken ART in the last two weeks",
    },
    {
        categories: [],
        concept_id: 7752,
        name: "Has the patient taken ART in the last two months",
    },
    {
        categories: [],
        concept_id: 7751,
        name: "Date ART last taken",
    },
    {
        categories: [],
        concept_id: 6981,
        name: "ART number at previous location",
    },
    {
        categories: [],
        concept_id: 7881,
        name: "Confirmatory HIV test location",
    },
    {
        categories: [],
        concept_id: 7754,
        name: "Ever received ART",
    },
    {
        categories: [],
        concept_id: 7937,
        name: "Ever registered at ART clinic",
    },
    {
        categories: [],
        concept_id: 6393,
        name: "Has transfer letter",
    },
    {
        categories: [],
        concept_id: 9685,
        name: "Phone",
    },
    {
        categories: [],
        concept_id: 9686,
        name: "Home visit",
    },
    {
        categories: ["art_extra_medication_order"],
        concept_id: 9974,
        name: "3HP (RFP + INH)",
    },
    {
        categories: [],
        concept_id: 656,
        name: "IPT",
    },
    {
        categories: ["side_effect"],
        sortIndex: {
            side_effect: 10,
        },
        concept_id: 1773,
        name: "Heavy alcohol use",
    },
    {
        categories: ["tb_symptom", "side_effect"],
        sortIndex: {
            side_effect: 4,
            tb_symptom: 4,
        },
        concept_id: 8260,
        name: "Weight loss / Failure to thrive / malnutrition",
    },
    {
        categories: [],
        concept_id: 9661,
        name: "Contraindications",
    },
    {
        categories: [],
        concept_id: 7567,
        name: "Drug induced",
    },
    {
        categories: ["tb_symptom", "side_effect", "adverse_effect"],
        sortIndex: {
            side_effect: 9,
            tb_symptom: 3,
        },
        concept_id: 6029,
        name: "Night sweats",
    },
    {
        categories: [
            "tb_symptom",
            "0_art_regimen_side_effect",
            "9_art_regimen_side_effect",
            "14_art_regimen_side_effect",
            "side_effect",
            "adverse_effect",
        ],
        sortIndex: {
            side_effect: 1,
            tb_symptom: 2,
        },
        concept_id: 5945,
        name: "Fever",
    },
    {
        categories: [
            "0_art_regimen_side_effect",
            "2_art_regimen_side_effect",
            "4_art_regimen_side_effect",
            "8_art_regimen_side_effect",
            "9_art_regimen_side_effect",
            "10_art_regimen_side_effect",
            "11_art_regimen_side_effect",
            "12_art_regimen_side_effect",
            "13_art_regimen_side_effect",
            "14_art_regimen_side_effect",
            "16_art_regimen_side_effect",
            "17_art_regimen_side_effect",
            "side_effect",
            "adverse_effect",
        ],
        sortIndex: {
            side_effect: 3,
        },
        concept_id: 5980,
        name: "Vomiting",
    },
    {
        categories: [
            "5_art_regimen_side_effect",
            "9_art_regimen_side_effect",
            "10_art_regimen_side_effect",
            "11_art_regimen_side_effect",
            "side_effect",
            "adverse_effect",
        ],
        sortIndex: {
            side_effect: 5,
        },
        concept_id: 877,
        name: "Dizziness",
    },
    {
        categories: [
            "9_art_regimen_side_effect",
            "10_art_regimen_side_effect",
            "11_art_regimen_side_effect",
            "13_art_regimen_side_effect",
            "14_art_regimen_side_effect",
            "side_effect",
            "adverse_effect",
        ],
        sortIndex: {
            side_effect: 7,
        },
        concept_id: 620,
        name: "Headache",
    },
    {
        categories: ["13_art_regimen_side_effect", "14_art_regimen_side_effect", "side_effect", "adverse_effect"],
        sortIndex: {
            side_effect: 2,
        },
        concept_id: 5978,
        name: "Nausea",
    },
    {
        categories: [
            "0_art_regimen_side_effect",
            "2_art_regimen_side_effect",
            "4_art_regimen_side_effect",
            "5_art_regimen_side_effect",
            "6_art_regimen_side_effect",
            "7_art_regimen_side_effect",
            "8_art_regimen_side_effect",
            "9_art_regimen_side_effect",
            "10_art_regimen_side_effect",
            "11_art_regimen_side_effect",
            "12_art_regimen_side_effect",
            "13_art_regimen_side_effect",
            "14_art_regimen_side_effect",
            "adverse_effect",
        ],
        concept_id: 843,
        name: "Treatment failure",
    },
    {
        categories: [
            "2_art_regimen_side_effect",
            "4_art_regimen_side_effect",
            "8_art_regimen_side_effect",
            "11_art_regimen_side_effect",
            "side_effect",
            "adverse_effect",
        ],
        sortIndex: {
            side_effect: 6,
        },
        concept_id: 1458,
        name: "Lactic acidosis",
    },
    {
        categories: [
            "tb_symptom",
            "0_art_regimen_side_effect",
            "9_art_regimen_side_effect",
            "15_art_regimen_side_effect",
            "16_art_regimen_side_effect",
            "17_art_regimen_side_effect",
            "side_effect",
            "adverse_effect",
        ],
        sortIndex: {
            side_effect: 8,
            tb_symptom: 1,
        },
        concept_id: 107,
        name: "Cough",
    },
    {
        categories: ["11_art_regimen_contraindication", "contraindication", "adverse_effect"],
        sortIndex: {
            contraindication: 1,
        },
        concept_id: 821,
        name: "Peripheral neuropathy",
    },
    {
        categories: [
            "0_art_regimen_contraindication",
            "2_art_regimen_contraindication",
            "6_art_regimen_contraindication",
            "7_art_regimen_contraindication",
            "8_art_regimen_contraindication",
            "contraindication",
            "adverse_effect",
        ],
        sortIndex: {
            contraindication: 3,
        },
        concept_id: 215,
        name: "Jaundice",
    },
    {
        categories: [
            "2_art_regimen_contraindication",
            "4_art_regimen_contraindication",
            "8_art_regimen_contraindication",
            "11_art_regimen_contraindication",
            "contraindication",
            "adverse_effect",
        ],
        sortIndex: {
            contraindication: 5,
        },
        concept_id: 2148,
        name: "Lipodystrophy",
    },
    {
        categories: [
            "5_art_regimen_contraindication",
            "6_art_regimen_contraindication",
            "7_art_regimen_contraindication",
            "10_art_regimen_contraindication",
            "contraindication",
            "adverse_effect",
        ],
        sortIndex: {
            contraindication: 7,
        },
        concept_id: 9242,
        name: "Kidney Failure",
    },
    {
        categories: ["4_art_regimen_contraindication", "5_art_regimen_contraindication", "contraindication", "adverse_effect"],
        sortIndex: {
            contraindication: 2,
        },
        concept_id: 219,
        name: "Psychosis",
    },
    {
        categories: ["4_art_regimen_contraindication", "5_art_regimen_contraindication", "contraindication", "adverse_effect"],
        sortIndex: {
            contraindication: 4,
        },
        concept_id: 9440,
        name: "Gynaecomastia",
    },
    {
        categories: [
            "2_art_regimen_contraindication",
            "4_art_regimen_contraindication",
            "8_art_regimen_contraindication",
            "11_art_regimen_contraindication",
            "contraindication",
            "adverse_effect",
        ],
        sortIndex: {
            contraindication: 6,
        },
        concept_id: 3,
        name: "Anemia",
    },
    {
        categories: ["0_art_regimen_contraindication", "contraindication", "adverse_effect"],
        sortIndex: {
            contraindication: 9,
        },
        concept_id: 512,
        name: "Skin rash",
    },
    {
        categories: ["contraindication", "adverse_effect"],
        sortIndex: {
            contraindication: 8,
        },
        concept_id: 867,
        name: "Insomnia",
    },
    {
        categories: [],
        concept_id: 1588,
        name: "TB treatment history",
    },
    {
        categories: ["fast_track"],
        concept_id: 9533,
        name: "Adult 18 years +",
    },
    {
        categories: ["fast_track"],
        concept_id: 9534,
        name: "On ART for 12 months ",
    },
    {
        categories: ["fast_track"],
        concept_id: 9535,
        name: "On 1st line ART",
    },
    {
        categories: ["fast_track"],
        concept_id: 9537,
        name: "Good current adherence",
    },
    {
        categories: ["fast_track"],
        concept_id: 9536,
        name: "Last VL <1000",
    },
    {
        categories: ["fast_track"],
        concept_id: 9538,
        name: "Pregnant / Breastfeeding",
    },
    {
        categories: ["fast_track"],
        concept_id: 9539,
        name: "Side effects / HIV-rel. diseases",
    },
    {
        categories: ["fast_track"],
        concept_id: 9540,
        name: "Needs BP / diabetes treatment",
    },
    {
        categories: ["fast_track"],
        concept_id: 9527,
        name: "Started IPT <12m ago",
    },
    {
        categories: ["fast_track"],
        concept_id: 2186,
        name: "Any sign for TB",
    },
    {
        categories: ["reasons_for_art", "stage_1_conditions_adults_who_reason"],
        concept_id: 7561,
        name: "WHO STAGE I ADULT",
    },
    {
        categories: ["reasons_for_art", "stage_1_conditions_pedaids_who_reason", "stage_1_conditions_adults_who_reason"],
        concept_id: 7041,
        name: "WHO STAGE I ADULT AND PEDS",
    },
    {
        categories: ["reasons_for_art", "stage_1_conditions_pedaids_who_reason"],
        concept_id: 7049,
        name: "WHO STAGE I PEDS",
    },
    {
        categories: ["reasons_for_art", "stage_2_conditions_adults_who_reason"],
        concept_id: 7046,
        name: "WHO STAGE II ADULT",
    },
    {
        categories: ["reasons_for_art", "stage_2_conditions_pedaids_who_reason", "stage_2_conditions_adults_who_reason"],
        concept_id: 7042,
        name: "WHO STAGE II ADULT AND PEDS",
    },
    {
        categories: ["reasons_for_art", "stage_2_conditions_pedaids_who_reason"],
        concept_id: 7050,
        name: "WHO STAGE II PEDS",
    },
    {
        categories: ["reasons_for_art", "stage_3_conditions_adults_who_reason"],
        concept_id: 7047,
        name: "WHO STAGE III ADULT",
    },
    {
        categories: ["reasons_for_art", "stage_3_conditions_pedaids_who_reason", "stage_3_conditions_adults_who_reason"],
        concept_id: 7043,
        name: "WHO STAGE III ADULT AND PEDS",
    },
    {
        categories: ["reasons_for_art", "stage_3_conditions_pedaids_who_reason"],
        concept_id: 7051,
        name: "WHO STAGE III PEDS",
    },
    {
        categories: ["reasons_for_art", "stage_4_conditions_adults_who_reason"],
        concept_id: 7048,
        name: "WHO STAGE IV ADULT",
    },
    {
        categories: ["reasons_for_art", "stage_4_conditions_pedaids_who_reason", "stage_4_conditions_adults_who_reason"],
        concept_id: 7044,
        name: "WHO STAGE IV ADULT AND PEDS",
    },
    {
        categories: ["reasons_for_art", "stage_4_conditions_pedaids_who_reason"],
        concept_id: 7052,
        name: "WHO STAGE IV PEDS",
    },
    {
        categories: [],
        concept_id: 1805,
        name: "Patient present",
    },
    {
        categories: [],
        concept_id: 2122,
        name: "Guardian present",
    },
    {
        categories: [],
        concept_id: 5092,
        name: "SAO2",
    },
    {
        categories: [],
        concept_id: 5087,
        name: "Pulse",
    },
    {
        categories: [],
        concept_id: 5088,
        name: "Temperature",
    },
    {
        categories: [],
        concept_id: 1484,
        name: "Treatment status",
    },
    {
        categories: [],
        concept_id: 3140,
        name: "Reason for poor treatment adherence",
    },
    {
        categories: [],
        concept_id: 6987,
        name: "Drug adherence",
    },
    {
        categories: [],
        concept_id: 2540,
        name: "Number of tablets brought to clinic",
    },
    {
        categories: [],
        concept_id: 7880,
        name: "Confirmatory hiv test type",
    },
    {
        categories: ["hiv_test_types"],
        concept_id: 1040,
        name: "HIV rapid test",
    },
    {
        categories: ["hiv_test_types"],
        concept_id: 844,
        name: "HIV DNA polymerase chain reaction",
    },
    {
        categories: ["hiv_test_types"],
        concept_id: 1118,
        name: "Not done",
    },
    {
        categories: ["hiv_test_types"],
        concept_id: 1067,
        name: "Unknown",
    },
    {
        categories: ["reason_for_art"],
        concept_id: 8263,
        name: "PRESUMED SEVERE HIV",
    },
    {
        categories: ["reason_for_art"],
        concept_id: 844,
        name: "HIV DNA POLYMERASE CHAIN REACTION",
    },
    {
        categories: ["reason_for_art"],
        concept_id: 1169,
        name: "HIV infected",
    },
    {
        categories: ["reason_for_art"],
        concept_id: 1755,
        name: "PATIENT PREGNANT",
    },
    {
        categories: ["reason_for_art"],
        concept_id: 5632,
        name: "BREASTFEEDING",
    },
    {
        categories: ["reason_for_art"],
        concept_id: 5006,
        name: "Asymptomatic",
    },
    {
        categories: ["reason_for_art"],
        concept_id: 8376,
        name: "LYMPHOCYTE COUNT BELOW THRESHOLD WITH WHO STAGE 1",
    },
    {
        categories: ["reason_for_art"],
        concept_id: 7559,
        name: "LYMPHOCYTE COUNT BELOW THRESHOLD WITH WHO STAGE 2",
    },
    {
        categories: [""],
        concept_id: 2743,
        name: "Who stages criteria present",
    },
    {
        categories: [""],
        concept_id: 7965,
        name: "Is patient breast feeding",
    },
    {
        categories: [""],
        concept_id: 6131,
        name: "Is patient pregnant",
    },
    {
        categories: [""],
        concept_id: 7563,
        name: "Reason for ART eligibility",
    },
    {
        categories: [""],
        concept_id: 7562,
        name: "Who stage",
    },
    {
        categories: [""],
        concept_id: 2540,
        name: "Pills brought",
    },
    {
        categories: [""],
        concept_id: 5089,
        name: "weight",
    },
    {
        categories: [],
        concept_id: 5090,
        name: "Height",
    },
    {
        categories: ["art_medication_order"],
        concept_id: 9974,
        name: "Rifapentine",
    },
    {
        categories: ["art_medication_order", "art_extra_medication_order"],
        concept_id: 916,
        name: "CPT",
    },
    {
        categories: ["art_medication_order", "art_extra_medication_order"],
        concept_id: 656,
        name: "INH",
    },
    {
        categories: [""],
        concept_id: 6784,
        name: "Appointment type",
    },
    {
        categories: [""],
        concept_id: 1779,
        name: "Reason for ARV switch",
    },
    {
        categories: [""],
        concept_id: 9561,
        name: "Assess for fast track",
    },
    {
        categories: [""],
        concept_id: 1282,
        name: "Medication orders",
    },
    {
        categories: [""],
        concept_id: 8471,
        name: "Fast track",
    },
    {
        categories: [""],
        concept_id: 7755,
        name: "Art Side effects",
    },
    {
        categories: ["art_medication_order"],
        concept_id: 1085,
        name: "Antiretroviral drugs",
    },
    {
        categories: [""],
        concept_id: 1066,
        name: "No",
    },
    {
        categories: [""],
        concept_id: 1065,
        name: "Yes",
    },
    {
        categories: [""],
        concept_id: 5096,
        name: "Appointment date",
    },
    {
        categories: [""],
        concept_id: 3289,
        name: "Type of patient",
    },
    {
        categories: ["art_patient_type"],
        concept_id: 9684,
        order: 3,
        name: "External consultation",
    },
    {
        categories: [""],
        concept_id: 6830,
        name: "Cd4 count location",
    },
    {
        categories: [""],
        concept_id: 6831,
        name: "Cd4 count datetime",
    },
    {
        categories: [""],
        concept_id: 5497,
        name: "CD4 count",
    },
    {
        categories: ["cd4_count"],
        concept_id: 8208,
        name: "cd4 less than or equal to 750",
    },
    {
        categories: ["cd4_count"],
        concept_id: 8262,
        name: "cd4 less than or equal to 250",
    },
    {
        categories: ["cd4_count"],
        concept_id: 8207,
        name: "cd4 less than or equal to 350",
    },
    {
        categories: ["cd4_count"],
        concept_id: 9389,
        name: "cd4 less than or equal to 500",
    },
    {
        categories: ["whole_staging_numbers"],
        concept_id: 9145,
        name: "WHO STAGE 1",
    },
    {
        categories: ["whole_staging_numbers"],
        concept_id: 9146,
        name: "WHO STAGE 2",
    },
    {
        categories: ["whole_staging_numbers"],
        concept_id: 2932,
        name: "WHO STAGE 3",
    },
    {
        categories: ["whole_staging_numbers"],
        concept_id: 2933,
        name: "WHO STAGE 4",
    },
    {
        categories: ["whole_staging_numbers"],
        concept_id: 7561,
        name: "WHO STAGE I ADULT",
    },
    {
        categories: ["whole_staging_numbers"],
        concept_id: 7041,
        name: "WHO STAGE I ADULT AND PEDS",
    },
    {
        categories: ["whole_staging_numbers"],
        concept_id: 7049,
        name: "WHO STAGE I PEDS",
    },
    {
        categories: ["whole_staging_numbers"],
        concept_id: 7046,
        name: "WHO STAGE II ADULT",
    },
    {
        categories: ["whole_staging_numbers"],
        concept_id: 7042,
        name: "WHO STAGE II ADULT AND PEDS",
    },
    {
        categories: ["whole_staging_numbers"],
        concept_id: 7050,
        name: "WHO STAGE II PEDS",
    },
    {
        categories: ["whole_staging_numbers"],
        concept_id: 7047,
        name: "WHO STAGE III ADULT",
    },
    {
        categories: ["whole_staging_numbers"],
        concept_id: 7043,
        name: "WHO STAGE III ADULT AND PEDS",
    },
    {
        categories: ["whole_staging_numbers"],
        concept_id: 7051,
        name: "WHO STAGE III PEDS",
    },
    {
        categories: ["whole_staging_numbers"],
        concept_id: 7048,
        name: "WHO STAGE IV ADULT",
    },
    {
        categories: ["whole_staging_numbers"],
        concept_id: 7044,
        name: "WHO STAGE IV ADULT AND PEDS",
    },
    {
        categories: ["whole_staging_numbers"],
        concept_id: 7052,
        name: "WHO STAGE IV PEDS",
    },
    {
        categories: ["pshd_condition", "stage_4_conditions_pedaids", "stage_4_conditions_adults", "who_staging_conditions", "staging_4_conditions"],
        concept_id: 7548,
        name: "Cryptococcal meningitis or other extrapulmonary cryptococcosis",
    },
    {
        categories: ["pshd_condition", "stage_4_conditions_pedaids", "stage_4_conditions_adults", "who_staging_conditions", "staging_4_conditions"],
        concept_id: 5340,
        name: "Candidiasis of oseophagus, trachea and bronchi or lungs",
    },
    {
        categories: ["stage_4_conditions_pedaids", "stage_4_conditions_adults", "who_staging_conditions", "staging_4_conditions"],
        concept_id: 1547,
        name: "Extrapulmonary tuberculosis (EPTB)",
    },
    {
        categories: ["stage_4_conditions_pedaids", "stage_4_conditions_adults", "who_staging_conditions", "staging_4_conditions"],
        concept_id: 507,
        name: "Kaposis sarcoma",
    },
    {
        categories: ["stage_3_conditions_pedaids", "stage_4_conditions_adults", "who_staging_conditions", "staging_4_conditions"],
        concept_id: 1215,
        name: "Bacterial pneumonia, severe recurrent",
    },
    {
        categories: ["stage_4_conditions_adults", "staging_4_conditions", "who_staging_conditions"],
        concept_id: 7959,
        name: "Non-typhoidal Salmonella bacteraemia, recurrent",
    },
    {
        categories: ["stage_4_conditions_adults", "stage_4_conditions_pedaids", "who_staging_conditions", "staging_4_conditions"],
        concept_id: 7957,
        name: "Symptomatic HIV-associated nephropathy or cardiomyopathy",
    },
    {
        categories: ["stage_4_conditions_pedaids", "stage_4_conditions_adults", "who_staging_conditions", "staging_4_conditions"],
        concept_id: 2587,
        name: "Cerebral or B-cell non Hodgkin lymphoma",
    },
    {
        categories: ["pshd_condition", "stage_4_conditions_pedaids", "stage_4_conditions_adults", "who_staging_conditions", "staging_4_conditions"],
        concept_id: 882,
        name: "Pneumocystis pneumonia",
    },
    {
        categories: ["stage_4_conditions_adults", "who_staging_conditions", "staging_4_conditions"],
        concept_id: 5344,
        name: "Chronic herpes simplex infection (orolabial, gential / anorectal >1 month or visceral at any site)",
    },
    {
        categories: ["stage_4_conditions_adults", "who_staging_conditions", "staging_4_conditions"],
        concept_id: 7551,
        name: "Cytomegalovirus infection (retinitis or infection or other organs)",
    },
    {
        categories: ["pshd_condition", "stage_4_conditions_adults", "who_staging_conditions"],
        concept_id: 2583,
        name: "Toxoplasmosis of the brain",
    },
    {
        categories: ["stage_4_conditions_adults", "who_staging_conditions", "staging_4_conditions"],
        concept_id: 2588,
        name: "Invasive cancer of cervix",
    },
    {
        categories: ["stage_4_conditions_adults", "who_staging_conditions", "staging_4_conditions"],
        concept_id: 7040,
        name: "Unspecified stage 4 condition",
    },
    {
        categories: ["stage_4_conditions_adults", "who_staging_conditions", "staging_4_conditions"],
        concept_id: 6408,
        name: "Other",
    },
    {
        categories: ["stage_4_conditions_pedaids", "who_staging_conditions"],
        concept_id: 1362,
        name: "HIV encephalopathy",
    },
    {
        categories: ["stage_4_conditions_pedaids", "who_staging_conditions"],
        concept_id: 2585,
        name: "Disseminated non-tuberculosis mycobacterial infection",
    },
    {
        categories: ["stage_4_conditions_pedaids", "who_staging_conditions"],
        concept_id: 7549,
        name: "Cryptosporidiosis, chronic with diarroea",
    },
    {
        categories: ["stage_4_conditions_pedaids", "who_staging_conditions"],
        concept_id: 7956,
        name: "Isosporiasis >1 month",
    },
    {
        categories: ["stage_4_conditions_pedaids", "who_staging_conditions"],
        concept_id: 7550,
        name: "Disseminated mycosis (coccidiomycosis or histoplasmosis)",
    },
    {
        categories: ["stage_4_conditions_pedaids", "who_staging_conditions"],
        concept_id: 5046,
        name: "Progressive multifocal leukoencephalopathy",
    },
    {
        categories: ["pshd_condition", "stage_4_conditions_pedaids", "who_staging_conditions"],
        concept_id: 3068,
        name: "Severe unexplained wasting or malnutrition not responding to treatment (weight-for-height/ -age <70% or MUAC less than 11cm or oedema)",
    },
    {
        categories: ["who_staging_conditions"],
        concept_id: 2894,
        name: "Bacterial infections, severe recurrent  (empyema, pyomyositis, meningitis, bone/joint infections but EXCLUDING pneumonia)",
    },
    {
        categories: ["stage_4_conditions_pedaids", "who_staging_conditions"],
        concept_id: 7960,
        name: "Chronic herpes simplex infection (orolabial or cutaneous >1 month or visceral at any site)",
    },
    {
        categories: ["stage_4_conditions_pedaids", "who_staging_conditions"],
        concept_id: 7552,
        name: "Cytomegalovirus infection: rentinitis or other organ (from age 1 month)",
    },
    {
        categories: ["pshd_condition", "stage_4_conditions_pedaids", "who_staging_conditions", "staging_4_conditions"],
        concept_id: 5048,
        name: "Toxoplasmosis of the brain (from age 1 month)",
    },
    {
        categories: ["stage_4_conditions_pedaids", "who_staging_conditions"],
        concept_id: 7961,
        name: "Recto-vaginal fistula, HIV-associated",
    },
    {
        categories: ["severe_hiv_wasting_syndrome", "stage_3_conditions_adults", "who_staging_conditions"],
        concept_id: 7540,
        name: "Severe weight loss >10% and/or BMI <18.5kg/m^2, unexplained",
    },
    {
        categories: ["severe_hiv_wasting_syndrome", "stage_3_conditions_adults", "who_staging_conditions"],
        concept_id: 5018,
        name: "Diarrhoea, chronic (>1 month) unexplained",
    },
    {
        categories: ["severe_hiv_wasting_syndrome", "stage_3_conditions_adults", "stage_3_conditions_pedaids", "who_staging_conditions"],
        concept_id: 5027,
        name: "Fever, persistent unexplained, intermittent or constant, >1 month",
    },
    {
        categories: ["stage_3_conditions_pedaids", "stage_3_conditions_adults", "who_staging_conditions"],
        concept_id: 8206,
        name: "Pulmonary tuberculosis (current)",
    },
    {
        categories: ["stage_3_conditions_pedaids", "stage_3_conditions_adults", "who_staging_conditions"],
        concept_id: 7539,
        name: "Tuberculosis (PTB or EPTB) within the last 2 years",
    },
    {
        categories: ["stage_3_conditions_adults", "who_staging_conditions"],
        concept_id: 5334,
        name: "Oral candidiasis",
    },
    {
        categories: ["stage_3_conditions_adults", "stage_3_conditions_pedaids", "who_staging_conditions"],
        concept_id: 7546,
        name: "Acute necrotizing ulcerative gingivitis or periodontitis",
    },
    {
        categories: ["stage_3_conditions_pedaids", "stage_3_conditions_adults", "who_staging_conditions"],
        concept_id: 2582,
        name: "Anaemia, unexplained < 8 g/dl",
    },
    {
        categories: ["stage_3_conditions_pedaids", "stage_3_conditions_adults", "who_staging_conditions"],
        concept_id: 7954,
        name: "Neutropaenia, unexplained < 500 /mm(cubed)",
    },
    {
        categories: ["stage_3_conditions_adults", "who_staging_conditions"],
        concept_id: 7541,
        name: "Severe bacterial infections (pneumonia, empyema, pyomyositis, bone/joint, meningitis, bacteraemia)",
    },
    {
        categories: ["stage_3_conditions_adults", "stage_3_conditions_pedaids", "who_staging_conditions"],
        concept_id: 7955,
        name: "Thrombocytopaenia, chronic < 50,000 /mm(cubed)",
    },
    {
        categories: ["stage_3_conditions_adults", "who_staging_conditions"],
        concept_id: 8205,
        name: "Hepatitis B or C infection",
    },
    {
        categories: ["stage_3_conditions_pedaids", "stage_3_conditions_adults", "who_staging_conditions"],
        concept_id: 5337,
        name: "Oral hairy leukoplakia",
    },
    {
        categories: ["stage_3_conditions_adults", "who_staging_conditions"],
        concept_id: 7039,
        name: "Unspecified stage 3 condition",
    },
    {
        categories: ["stage_3_conditions_pedaids", "who_staging_conditions"],
        concept_id: 7543,
        name: "Moderate unexplained wasting/malnutrition not responding to treatment (weight-for-height/ -age 70-79% or muac 11-12 cm)",
    },
    {
        categories: ["stage_3_conditions_pedaids", "who_staging_conditions"],
        concept_id: 7544,
        name: "Diarrhoea, persistent unexplained (14 days or more)",
    },
    {
        categories: ["pshd_condition", "stage_3_conditions_pedaids", "who_staging_conditions"],
        concept_id: 7545,
        name: "Oral candidiasis (from age 2 months)",
    },
    {
        categories: ["stage_3_conditions_pedaids", "who_staging_conditions"],
        concept_id: 7547,
        name: "Lymph node tuberculosis",
    },
    {
        categories: ["stage_3_conditions_pedaids", "who_staging_conditions"],
        concept_id: 5024,
        name: "Symptomatic lymphoid interstitial pneumonia",
    },
    {
        categories: ["stage_3_conditions_pedaids", "who_staging_conditions"],
        concept_id: 2889,
        name: "Chronic HIV-associated lung disease, including bronchiectasis",
    },
    {
        categories: ["stage_2_conditions_adults", "who_staging_conditions"],
        concept_id: 5332,
        name: "Moderate weight loss less than or equal to 10 percent, unexplained",
    },
    {
        categories: ["stage_2_conditions_pedaids", "stage_2_conditions_adults", "who_staging_conditions"],
        concept_id: 5012,
        name: "Respiratory tract infections, recurrent (sinusitis, tonsilitus, otitis media, pharyngitis)",
    },
    {
        categories: ["stage_2_conditions_adults", "who_staging_conditions"],
        concept_id: 2578,
        name: "Seborrhoeic dermatitis",
    },
    {
        categories: ["stage_2_conditions_adults", "stage_2_conditions_pedaids", "who_staging_conditions"],
        concept_id: 7536,
        name: "Papular pruritic eruptions / Fungal nail infections",
    },
    {
        categories: ["stage_2_conditions_pedaids", "stage_2_conditions_adults", "who_staging_conditions"],
        concept_id: 836,
        name: "Herpes zoster",
    },
    {
        categories: ["stage_2_conditions_pedaids", "stage_2_conditions_adults", "who_staging_conditions"],
        concept_id: 2575,
        name: "Angular cheilitis",
    },
    {
        categories: ["stage_2_conditions_pedaids", "stage_2_conditions_adults", "who_staging_conditions"],
        concept_id: 2576,
        name: "Oral ulcerations, recurrent",
    },
    {
        categories: ["stage_2_conditions_adults", "who_staging_conditions"],
        concept_id: 7038,
        name: "Unspecified stage 2 condition",
    },
    {
        categories: ["stage_2_conditions_pedaids", "who_staging_conditions"],
        concept_id: 7537,
        name: "Hepatosplenomegaly, persistent unexplained",
    },
    {
        categories: ["stage_2_conditions_pedaids", "who_staging_conditions"],
        concept_id: 2891,
        name: "Lineal gingival erythema",
    },
    {
        categories: ["anc_diagnosis", "stage_2_conditions_pedaids", "who_staging_conditions"],
        concept_id: 6775,
        name: "Wart virus infection, extensive",
    },
    {
        categories: ["stage_2_conditions_pedaids", "who_staging_conditions"],
        concept_id: 6776,
        name: "Molluscum contagiosum, extensive",
    },
    {
        categories: ["stage_2_conditions_pedaids", "who_staging_conditions"],
        concept_id: 1210,
        name: "Parotid enlargement, persistent unexplained",
    },
    {
        categories: ["stage_1_conditions_pedaids", "stage_1_conditions_adults", "who_staging_conditions"],
        concept_id: 5327,
        name: "Asymptomatic HIV infection",
    },
    {
        categories: ["stage_1_conditions_pedaids", "stage_1_conditions_adults", "who_staging_conditions"],
        concept_id: 5328,
        name: "Persistent generalized lymphadenopathy",
    },
    {
        categories: [],
        concept_id: 8426,
        name: "Radiology Orders",
    },
    {
        name: "Hand x-ray",
        concept_id: 10337,
    },
    {
        name: "Foot x-ray",
        concept_id: 10334,
    },
    {
        name: "Leg x-ray",
        concept_id: 10342,
    },
    {
        name: "Pelvis x-ray",
        concept_id: 10343,
    },
    {
        name: "Shoulder x-ray",
        concept_id: 10344,
    },
    {
        name: "History of COVID-19 contact",
        concept_id: 10539,
    },
    {
        name: "HIV viral load",
        concept_id: 856,
    },
    {
        name: "Plasma",
        concept_id: 1002,
    },
    {
        categories: [],
        concept_id: 11764,
        name: "How many doses of Tdv did the mother receive?",
    },
    {
        categories: [],
        concept_id: 11759,
        name: "Protected at birth",
    },
    {
        categories: [],
        concept_id: 5090,
        name: "height",
    },
    {
        categories: [],
        concept_id: 5089,
        name: "weight",
    },
    {
        categories: [],
        concept_id: 5085,
        name: "Systolic",
    },
    {
        categories: [],
        concept_id: 5086,
        name: "Diastolic",
    },
    {
        categories: [],
        concept_id: 5088,
        name: "Temperature",
    },
    {
        categories: [],
        concept_id: 5087,
        name: "Pulse",
    },
    {
        categories: [],
        concept_id: 5242,
        name: "Respiratory rate",
    },
    {
        categories: [],
        concept_id: 5092,
        name: "SAO2",
    },
];
