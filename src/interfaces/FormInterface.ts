export interface FormElement {
    componentType?:
        | "Heading"
        | "inputField"
        | "checkboxField"
        | "radioButtonField"
        | "Alert"
        | "multiSelectInputField"
        | "Dashes"
        | "phoneInputField"
        | "dateInputField";
    size?: string;
    name?: string;
    header?: string;
    value?: any;
    initialValue?: any;
    condition?:
        | ((allValues: Record<string, any>) => boolean)
        | ((allValues: Record<string, any>) => Promise<AlertResult | null>)
        | ((allValues: Record<string, any>) => AlertResult | null);
    [key: string]: any;
}

export interface AlertResult {
    value?: string;
    index?: string;
    backgroundColor?: string;
    textColor?: string;
    icon?: string;
    message?: string;
    bmi?: number;
    category?: string;
    [key: string]: any;
}
