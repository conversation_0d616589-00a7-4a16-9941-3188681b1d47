<template>
  <ion-modal :is-open="props.isOpen" :show-backdrop="true" @didDismiss="closeModal">
    <ion-header>
      <ion-toolbar>
        <ion-title>{{ props.title }}</ion-title>
        <ion-buttons slot="end">
          <ion-button @click="closeModal">
            <ion-icon :icon="closeCircleOutline"></ion-icon>
          </ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content class="ion-padding">
      <ion-text color="danger" v-if="!isDataValid">
        <p>Invalid data provided to table</p>
      </ion-text>

      <ion-text v-if="isDataValid && tableRows.length === 0">
        <p>No data available</p>
      </ion-text>

      <report-table
        v-if="isDataValid && tableRows.length > 0"
        :title="props.title"
        :columns="columns"
        :rows="tableRows"
        :period="props.period"
        v-bind="tableConfig"
      />
    </ion-content>

    <ion-footer collapse="fade" class="ion-no-border">
      <ion-row>
        <ion-col>
          <ion-button id="cbtn" class="btnText cbtn" fill="solid" style="width: 130px" @click="closeModal">
            Close
          </ion-button>
        </ion-col>
        <ion-col v-if="props.allowVerification">
          <dynamic-button
            name="Verify"
            @click="onVerify"
            fill="solid"
            style="float: right; margin: 2%; width: 130px"
          />
        </ion-col>
      </ion-row>
    </ion-footer>
  </ion-modal>
</template>

<script setup lang="ts">
import { defineProps, ref, computed, watch } from 'vue'
import {
  IonModal,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonButtons,
  IonButton,
  IonContent,
  IonIcon,
  IonFooter,
  IonRow,
  IonCol,
  IonText
} from '@ionic/vue'
import { closeCircleOutline } from 'ionicons/icons'
import DynamicButton from '@/components/DynamicButton.vue'
import ReportTable from '@/components/ReportTable.vue'
import { TableColumnInterface } from '@uniquedj95/vtable'

interface Props {
  isOpen: boolean
  title: string
  data: Array<any>
  indicator: string
  period?: string 
  allowVerification?: boolean
  onClose: () => void
  onVerify?: () => void
}

const props = defineProps<Props>()
const tableRows = ref<Array<any>>([])
const isDataValid = ref(true)

// Define columns based on the data structure
const columns = computed<TableColumnInterface[]>(() => {
  const baseColumns = [
    { path: 'arv_number', label: 'ARV Number', sortable: true },
    { path: 'name', label: 'Name', sortable: true },
    { path: 'gender', label: 'Gender', sortable: true }
  ];

  if (!props.data?.length) {
    console.log('No data available for columns');
    return baseColumns;
  }

  // Add additional columns based on the indicator type
  const firstRecord = props.data[0];

  if (firstRecord.dob) {
    baseColumns.push({ path: 'dob', label: 'Date of Birth', sortable: true });
  }

  if (firstRecord.enrollment_date) {
    baseColumns.push({ path: 'enrollment_date', label: 'Enrollment Date', sortable: true });
  }

  if (firstRecord.outcome_date) {
    baseColumns.push({ path: 'outcome_date', label: 'Outcome Date', sortable: true });
  }

  console.log('Final columns:', baseColumns);
  return baseColumns;
});

const formatRows = () => {
  try {
    if (!props.data || !Array.isArray(props.data)) {
      console.warn('DataCleaningResultsModal: Invalid or missing data prop');
      isDataValid.value = false;
      tableRows.value = [];
      return;
    }

    if (props.data.length === 0) {
      console.info('DataCleaningResultsModal: Empty data array provided');
      isDataValid.value = true;
      tableRows.value = [];
      return;
    }

    isDataValid.value = true;
    tableRows.value = props.data.map(patient => {
      try {
        const row: Record<string, any> = {
          arv_number: patient.arv_number || patient.art_number || '',
          name: patient.name || '',
          gender: patient.gender || ''
        };

        // Add dynamic fields if they exist in the data
        if (patient.dob) {
          row.dob = patient.dob;
        }
        if (patient.enrollment_date) {
          row.enrollment_date = patient.enrollment_date;
        }
        if (patient.outcome_date) {
          row.outcome_date = patient.outcome_date;
        }

        // Add any custom fields that might be required for specific indicators
        if (props.indicator && patient[props.indicator]) {
          row[props.indicator] = patient[props.indicator];
        }

        return row;
      } catch (err) {
        console.error('Error formatting row:', err, { patient });
        return null;
      }
    }).filter(row => row !== null);

    console.log('DataCleaningResultsModal: Rows formatted successfully', { 
      rowCount: tableRows.value.length 
    });
  } catch (err) {
    console.error('Error in formatRows:', err);
    isDataValid.value = false;
    tableRows.value = [];
  }
};

const closeModal = () => {
  props.onClose()
}

const onVerify = () => {
  if (props.onVerify) {
    props.onVerify()
  }
}

watch(() => props.data, formatRows, { immediate: true })

const tableConfig = computed(() => ({
  showIndices: true,
  tableCssTheme: 'report-theme'
}));
</script>

<style scoped>
ion-header {
  --background: var(--ion-color-primary);
}

ion-title {
  color: var(--ion-color-light);
}

ion-footer {
  --ion-toolbar-background: #fff;
}

ion-modal {
  --height: 85%;
  --border-radius: 8px;
}
</style>
