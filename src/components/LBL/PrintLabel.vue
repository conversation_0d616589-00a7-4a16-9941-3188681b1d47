<template>
    <div>
        <div :id="_id" class="label-canvas">
            <slot></slot>
        </div>
        <div id="img-canvas">
            <div v-if="!showImage" class='print-section ion-text-center vertically-align'> 
                <svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 1000 1000" enable-background="new 0 0 1000 1000" xml:space="preserve">
                <metadata> Svg Vector Icons : http://www.onlinewebfonts.com/icon </metadata>
                <g><g transform="translate(0.000000,511.000000) scale(0.100000,-0.100000)"><path d="M2918.3,4119.3c-303-51.3-610.8-284.4-753-568.8c-102.6-212.1-125.9-359-125.9-822.9v-380h-533.8c-575.8,0-720.3-16.3-899.8-104.9c-237.8-116.6-454.6-410.3-491.9-669l-14-109.6l258.8-128.2l258.8-130.5H5000h4382.5l258.7,130.5l258.8,128.2l-14,109.6c-37.3,261.1-247.1,550.1-487.2,666.7c-184.2,90.9-324,107.2-904.5,107.2h-533.8v380c0,463.9-23.3,610.8-125.9,822.9c-142.2,286.7-435.9,508.2-757.6,568.8C6927.8,4147.2,3088.5,4147.2,2918.3,4119.3z M7158.6,3776.6c79.3-25.6,156.2-81.6,256.4-179.5c216.8-216.8,219.1-230.8,219.1-1102.6v-729.6H5000H2365.8v729.6c0,871.8,2.3,885.8,219.1,1102.6c233.1,230.8,51.3,214.5,2405.7,216.8C6850.9,3816.2,7053.7,3811.5,7158.6,3776.6z"/><path d="M109.3-267.9l7-1382.3l74.6-153.8c90.9-184.2,258.7-349.7,445.2-435.9c130.5-60.6,146.9-62.9,692.3-74.6l559.5-11.7l14-338c11.7-275.1,23.3-361.3,65.3-466.2c163.2-401,484.9-671.4,909.1-757.6c146.9-32.6,452.2-35,2202.9-30.3c2030.4,7,2030.4,7,2184.3,58.3c363.7,123.5,643.4,396.3,773.9,752.9c37.3,95.6,51.3,209.8,60.6,454.6l14,326.4l559.5,11.7l561.8,11.7l153.9,76.9c191.2,93.2,366,277.4,442.9,466.2l53.6,132.9l7,1370.7l7,1370.7l-209.8-107.2l-212.1-104.9H5000H524.3l-212.1,104.9l-209.8,107.2L109.3-267.9z M1107,121.4c207.5-107.2,167.8-435.9-60.6-498.9c-104.9-28-186.5-7-263.4,69.9C701.4-225.9,678.1-121,720.1-20.8C785.4,137.7,955.5,200.7,1107,121.4z M7487.3-368.1c139.9-42,223.8-128.2,268.1-275.1c23.3-72.3,30.3-370.7,30.3-1181.9c0-1079.3,0-1084-53.6-1216.8c-76.9-188.8-251.8-373-442.9-466.2l-156.2-76.9H5000H2867l-156.2,76.9c-191.1,93.2-366,277.4-442.9,466.2c-53.6,130.6-53.6,149.2-60.6,1135.3c-7,1046.7,4.7,1244.8,88.6,1382.3c51.3,86.3,135.2,144.5,240.1,167.8c39.6,11.7,1144.6,18.7,2454.7,21C7053.7-333.2,7384.7-337.8,7487.3-368.1z M1125.7-766.7c65.3-44.3,121.2-151.5,121.2-230.8c0-79.3-55.9-186.5-121.2-230.8c-67.6-48.9-216.8-46.6-298.4,2.4c-172.5,100.2-167.8,368.3,4.7,466.2C906.6-720.1,1062.8-722.5,1125.7-766.7z"/><path d="M3149.1-927.6c-60.6-62.9-67.6-130.5-18.7-195.8l44.3-60.6h1708.7h1708.7l44.3,60.6c48.9,65.3,42,132.9-18.7,195.8c-32.6,30.3-251.8,35-1734.4,35C3400.9-892.6,3181.7-897.3,3149.1-927.6z"/><path d="M3149.1-1860c-60.6-62.9-67.6-130.5-18.7-195.8l44.3-60.6h1708.7h1708.7l44.3,60.6c48.9,65.3,42,132.9-18.7,195.8c-32.6,30.3-251.8,35-1734.4,35C3400.9-1825.1,3181.7-1829.7,3149.1-1860z"/><path d="M3149.1-2792.5c-60.6-62.9-67.6-130.5-18.7-195.8l44.3-60.6h1708.7h1708.7l44.3,60.6c48.9,65.3,42,132.9-18.7,195.8c-32.6,30.3-251.8,35-1734.4,35C3400.9-2757.5,3181.7-2762.2,3149.1-2792.5z"/></g></g>
                </svg>
            </div>
            <img src="" :id="`${_id}-image`"/>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { onMounted, computed, ref } from 'vue';
import html2canvas from "html2canvas"
import { modalController } from '@ionic/core';
import { delayPromise } from '@/utils/Timers';

const MODAL_TIMEOUT = 50

const emits = defineEmits(['labelOutput'])
const props = defineProps({ id: String, width: Number, height: Number })

const showImage = ref(false)
const _id = computed(() => `${props.id}${new Date().toISOString()}`)

function generateImage() {
    const label: any = document.getElementById(_id.value as any);
    if (!label) return
    html2canvas(label, { scale: 7, backgroundColor: "#ffffff"}).then(function(canvas: any) {
        try {
            // Convert canvas to Data URL (base64 image string)
            const imageData = canvas.toDataURL("image/png");
            const imgSrc: any = document.getElementById(`${[_id.value]}-image`)
            imgSrc.src = imageData
            showImage.value = true
            emits("labelOutput", imageData)
            delayPromise(MODAL_TIMEOUT).then(() => modalController.dismiss({  generatedLabelImage: imageData }, undefined, "printout"))
        } catch (error) {
            console.error(error)
        }
    });
}
onMounted(() => setTimeout(() => generateImage(), 80))
</script>
<style scoped>
    .label-canvas {
        position: absolute;
        left: -9999px; /* Off-screen */
        top: -9999px; /* Off-screen */
        font-family:Verdana, Geneva, Tahoma, sans-serif!important;
        overflow: hidden;
        max-width: 4496px;
    }
    #img-canvas {
        padding: 0;
        margin: 0 auto;
        overflow: hidden;
    }
    svg {
        width: 50%;
    }
</style>
