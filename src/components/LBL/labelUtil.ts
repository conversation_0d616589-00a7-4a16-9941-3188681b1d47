import { PrintoutService } from "@/services/printout_service";
import { toastWarning } from "@/utils/Alerts";
import { modalController } from "@ionic/vue";
import { isEmpty } from "lodash";
import { ref } from "vue";
import EventBus from "@/utils/EventBus";
import { EventChannels } from "@/utils/EventBus";
import { Service } from "@/services/service";
import { LabelPrinter } from "cap-label-printer-plugin";
import { delayPromise } from "../../utils/Timers";
import Url from "../../utils/Url";
import ApiClient from "../../services/api_client";
import { NCDVisitSummary } from "@/services/print/NCD_visit_summary";
import { useDemographicsStore } from "@/stores/DemographicStore";
import { SelectPrinter, sendZPLCommand, canvasToZPL } from "@/services/print/ip_address_printers";

interface PrintLabelParams {
    scaleWidth?: number;
    scaleHeight?: number;
    useImage?: boolean;
    lblUrl?: string;
    copies?: number;
    onGenerateProps?: () => Promise<Record<string, any>>;
}

const isLoading = ref(false);

/**
 * Configure and retrieve default printer
 * @returns
 */
async function configurePrinter() {
    const service = new PrintoutService();
    let printer = await service.getDefaultPrinter();
    if (isEmpty(printer)) printer = await service.selectDefaultPrinter();
    if (isEmpty(printer)) throw new Error("No printer device found");
    return printer;
}

/**
 * Print LBL using backend url
 * @param printer
 * @param url
 */
async function printLbl(printer: any, url: string) {
    const finalUrl = Url.updateUrlParamString(url, { raw: true });
    EventBus.emit(EventChannels.SHOW_MODAL, "zebra-modal");

    console.time(url);
    const eplCommands = await Service.getText(finalUrl);

    if (!eplCommands) throw "Unable to print Label. Try again later";
    if (printer?.port === "BT") {
        const service = new PrintoutService();
        return service.printToBluetoothDevice(printer!, eplCommands);
    }
    LabelPrinter.printLabel({
        eplCommands,
        name: printer.name,
        address: printer.address,
        url: await ApiClient.expandPath(finalUrl),
    });
    console.timeEnd(url);
    await delayPromise(2000);
}

/**
 * Generate label image for the printer
 * @param component
 * @param componentProps
 * @param width
 * @param height
 */
async function printLabelImage(printer: any, component: any, params: PrintLabelParams) {
    try {
        const patient = await NCDVisitSummary(useDemographicsStore().patient);
        const ip_address = await SelectPrinter();
        if (!ip_address) return;
        const modal = await modalController.create({
            id: "printout",
            component,
            backdropDismiss: false,
            cssClass: "visit-modal",
            componentProps: {
                patient,
                imageOut: async (images: string[]) => {
                    const copies = params.copies ?? 1;

                    for (let i = 0; i < copies; ++i) {
                        const res = images.map(async (image: any) => {
                            if (Service.getIsIpPrintersStatus()) {
                                const gfa = canvasToZPL(image);
                                const zpl = `^XA ^PW900 ^FO20,20 ${gfa} ^FS ^XZ`;
                                await sendZPLCommand({ ip_address, zpl });
                            } else {
                                LabelPrinter.printLabel({
                                    address: printer?.address,
                                    name: printer?.name,
                                    // eplCommands: componentProps.zpl ?? "",
                                    height: 302,
                                    width: 582,
                                    // url: url ?? '',
                                    image: image,
                                });
                            }
                        });
                        await Promise.all(res);
                    }
                    setTimeout(() => modalController.getTop().then((e) => e && modal.dismiss()), 1500);
                    isLoading.value = false;
                },
            },
        });
        await modal.present();
    } catch (e) {
        console.error(e);
        isLoading.value = false;
        modalController.getTop().then((v) => (v ? modalController.dismiss() : null));
        toastWarning("An error occurred while generating label");
    }
}

/**
 * Print a label component
 * @param labelComponent
 * @param params
 * @returns
 */
export async function printLabel(labelComponent: any, params: PrintLabelParams) {
    let printer = {};
    if (!Service.getIsIpPrintersStatus()) printer = await configurePrinter();
    // if (/zebra/i.test(printer.name) || /Browser/i.test(printer.address) || printer.port === "BT") {
    //     return printLbl(printer, params.lblUrl);
    // }
    return await printLabelImage(printer, labelComponent, params);
}
