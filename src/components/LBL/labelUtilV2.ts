import { PrintoutService } from "@/services/printout_service";
import { toastWarning } from "@/utils/Alerts";
import { modalController, loadingController } from "@ionic/vue";
import { isEmpty } from "lodash";
import { ref } from "vue";
import EventBus from "@/utils/EventBus";
import { EventChannels } from "@/utils/EventBus";
import { Service } from "@/services/service";
import { LabelPrinter } from "cap-label-printer-plugin";
import { delayPromise } from "../../utils/Timers";
import Url from "../../utils/Url";
import ApiClient from "../../services/api_client";
import url from "@/utils/Url";

interface PrintLabelParams {
    scaleWidth?: number;
    scaleHeight?: number;
    useImage?: boolean;
    lblUrl?: string;
    copies?: number;
    onGenerateProps?: () => Promise<Record<string, any>>;
}

const isLoading = ref(false);

function dismissLoader() {
    loadingController.getTop().then((v) => (v ? loadingController.dismiss() : null));
}

/**
 * Configure and retrieve default printer
 * @returns
 */
async function configurePrinter() {
    const service = new PrintoutService();
    let printer = await service.getDefaultPrinter();
    if (isEmpty(printer)) printer = await service.selectDefaultPrinter();
    if (isEmpty(printer)) throw new Error("No printer device found");
    return printer;
}

/**
 * Print LBL using backend url
 * @param printer
 * @param url
 */
async function printLbl(printer: any, url: string) {
    const finalUrl = Url.updateUrlParamString(url, { raw: true });
    EventBus.emit(EventChannels.SHOW_MODAL, "zebra-modal");
    const eplCommands = await Service.getText(finalUrl);
    if (!eplCommands) throw "Unable to print Label. Try again later";
    if (printer?.port === "BT") {
        const service = new PrintoutService();
        return service.printToBluetoothDevice(printer!, eplCommands);
    }
    LabelPrinter.printLabel({
        eplCommands,
        name: printer.name,
        address: printer.address,
        url: await ApiClient.expandPath(finalUrl),
    });
    await delayPromise(2000);
}

/**
 * Generate label image for the printer
 * @param component
 * @param componentProps
 * @param width
 * @param height
 */
async function printLabelImage(printer: any, component: any, params: PrintLabelParams) {
    if (isLoading.value) return;
    isLoading.value = true;
    let componentProps: Record<string, any> = {};
    const loader = await loadingController.create({ message: "Processing label...", backdropDismiss: false });
    await loader.present();
    try {
        // Retrieve Label data either by a generation function or url specified
        if (typeof params.onGenerateProps === "function") {
            componentProps = await params.onGenerateProps();
        } else if (typeof params.lblUrl === "string") {
            componentProps = await PrintoutService.getJson(url.removeParams(params.lblUrl), url.extractParams(params.lblUrl));
        } else {
            isLoading.value = false;
            console.error("No generation parameters provided");
            toastWarning("No label generation parameters specified!");
            dismissLoader();
            return;
        }
        dismissLoader();
        // Show printing generation modal
        const modal = await modalController.create({
            id: "printout",
            component,
            backdropDismiss: false,
            cssClass: "small-modal",
            componentProps: { ...(componentProps?.data ?? componentProps) },
        });

        await modal.present();
        const { data } = await modal.onWillDismiss();
        const copies = params.copies ?? 1;
        for (let i = 0; i < copies; ++i) {
            await LabelPrinter.printLabel({
                address: printer.address,
                name: printer.name,
                eplCommands: componentProps.zpl ?? "",
                height: params.scaleHeight ?? 0,
                width: params.scaleWidth ?? 562,
                image: data.generatedLabelImage && params.useImage !== false ? data.generatedLabelImage : undefined,
            });
        }
        isLoading.value = false;
    } catch (e) {
        isLoading.value = false;
        console.error(e);
        dismissLoader();
        modalController.getTop().then((v) => (v ? modalController.dismiss() : null));
        toastWarning("An error occurred while generating label");
    }
}

/**
 * Print a label component
 * @param labelComponent
 * @param params
 * @returns
 */
export async function printLabel(labelComponent: any, params: PrintLabelParams) {
    const printer = await configurePrinter();
    if (/Browser/i.test(printer.address) || printer.port === "BT") {
        return printLbl(printer, params.lblUrl);
    }
    return printLabelImage(printer, labelComponent, params);
}
