<template>
    <ion-header style="display: flex; justify-content: space-between">
        <ion-title class="modalTitle">Add Lab Test</ion-title>
        <ion-icon @click="dismiss()" style="padding-top: 10px; padding-right: 10px" :icon="iconsContent.cancel"></ion-icon>
    </ion-header>
    <ion-content :fullscreen="true" class="ion-padding" style="--background: #fff">
        <StandardForm :formData="addTestForm" ref="formRef" />
    </ion-content>
    <ion-footer collapse="fade" class="ion-no-border">
        <ion-row>
            <ion-col>
                <DynamicButton @click="saveTest()" name="Save" fill="solid" style="float: right; margin: 2%; width: 130px" />
            </ion-col>
        </ion-row>
    </ion-footer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from "vue";
import { useRoute } from "vue-router";
import { storeToRefs } from "pinia";
import { IonContent, IonHeader, IonTitle, modalController } from "@ionic/vue";
import { icons } from "@/utils/svg";
import { OrderService } from "@/services/order_service";
import { useInvestigationStore } from "@/apps/NCD/stores/InvestigationStore";
import BasicForm from "@/components/BasicForm.vue";
import DynamicButton from "@/components/DynamicButton.vue";
import { useDemographicsStore } from "@/stores/DemographicStore";
import HisDate from "@/utils/Date";
import { validateInputFiledData } from "@/services/group_validation";
import { Service } from "@/services/service";
import { savePatientRecord } from "@/services/offline_service";

import { modifyFieldValue } from "@/services/data_helpers";
import { ConceptService } from "@/services/concept_service";
import { getOfflineRecords } from "@/services/offline_service";
import StandardForm from "@/components/Forms/StandardForm.vue";
import { FormElement } from "@/components/Forms/interfaces/FormElement";
import StandardValidations from "@/validations/StandardValidations";
import { toastWarning } from "@/utils/Alerts";

// Store setup
const demographicsStore = useDemographicsStore();
const { patient } = storeToRefs(demographicsStore);

// Route setup
const route = useRoute();

// Reactive data
const iconsContent = icons;
const selectedTest = ref<any>("");
const labOrders = ref<any>("");
const specimen = ref<any>("");
const testList = ref<any>("");
const formRef = ref<InstanceType<typeof StandardForm> | null>(null);

// Computed properties

const addTestForm = computed(() => {
    return [
        {
            componentType: "multiSelectInputField",
            name: "test",
            header: "Test",
            options: testList.value,
            grid: { s: "6" },
            icon: icons.search,
            validation: StandardValidations.required,
            onChange: (value: any) => {
                selectedTest.value = value.name;
            },
        },
        {
            componentType: "multiSelectInputField",
            name: "specimen",
            header: "Specimen",
            grid: { s: "6" },
            icon: icons.search,
            validation: StandardValidations.required,
            value: specimen.value.length == 1 ? specimen.value[0] : "",
            options: specimen.value.length > 1 ? specimen.value : [],
        },
    ] satisfies FormElement[];
});

watch(selectedTest, async (newValue) => {
    if (newValue) {
        try {
            specimen.value = await getSpecimen(newValue);
        } catch (error) {
            specimen.value = [];
        }
    } else {
        specimen.value = [];
    }
});

const getSpecimen = async (name: any) => {
    let specimens: any;
    if (Service.getUseIndexDBStatus() || Service.getModsStatus()) {
        specimens = await getOfflineRecords("specimens");
    } else {
        specimens = await OrderService.getSpecimens("", 1000);
    }

    const data = await ConceptService.getConceptSet(name, "", { names: specimens.map((item: any) => item.name) });
    return data;
};

watch(
    patient,
    async () => {
        labOrders.value = [...patient.value.labOrders.saved, ...patient.value.labOrders.unsaved];
    },
    { deep: true }
);

// Methods
const dismiss = () => {
    modalController.dismiss();
};

const getTests = async () => {
    if (Service.getUseIndexDBStatus() || Service.getModsStatus()) {
        return await getOfflineRecords("testTypes");
    } else {
        return await OrderService.getTestTypes();
    }
};

const saveTest = async () => {
    if (formRef.value.validateForm()) {
        toastWarning("Test not saved");
        return;
    }
    if (!formRef.value) {
        console.error("Form reference is not available");
        return;
    }
    const formData = formRef.value.getFormValues();
    const investigation = [
        {
            concept_id: formData["test"].concept_id,
            name: formData["test"].name,
            specimen: formData["specimen"].name,
            reason: "Routine",
            specimenConcept: await ConceptService.getConceptID(formData["specimen"].name, true),
        },
    ];
    const data = await OrderService.buildLabOrders("", investigation);
    let order = data[0];
    order.order_date = order.date;
    order.specimen.name = formData["specimen"].name;
    order.tests[0].name = formData["test"].name;
    const patientData = JSON.parse(JSON.stringify(patient.value));
    (patientData.labOrders ??= {}).unsaved ??= [];
    (patientData.labOrders ??= {}).saved ??= [];
    patientData.labOrders.unsaved.push(order);
    await savePatientRecord(patientData);
    labOrders.value = [...patientData.labOrders.saved, ...patientData.labOrders.unsaved];
    dismiss();
};

// Lifecycle hooks
onMounted(async () => {
    testList.value = await getTests();
});
</script>

<style scoped>
.ion-padding {
    --padding-start: var(--ion-padding, 0px);
    --padding-end: var(--ion-padding, 0px);
    --padding-top: var(--ion-padding, 0px);
    --padding-bottom: var(--ion-padding, 16px);
}
.background {
    /* background-color: #fff; */
}
#container {
    text-align: center;

    position: absolute;
    left: 0;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
}

#container strong {
    font-size: 20px;
    line-height: 26px;
}

#container p {
    font-size: 16px;
    line-height: 22px;

    color: #8c8c8c;

    margin: 0;
}

#container a {
    text-decoration: none;
}

.action_buttons {
    color: var(--ion-color-primary);
    display: flex;
    align-items: center;
    float: right;
    max-width: 70px;
}

.modify_buttons {
    padding-left: 20px;
}

.item_no_border {
    --border-color: transparent;
}

.search_result {
    padding: 10px;
}
.action_buttons {
    opacity: 0; /* Initially hide the action buttons */
    transition: opacity 0.3s; /* Add a smooth transition effect */
}

.dashed_bottom_border:hover .action_buttons {
    opacity: 1; /* Show the action buttons when the row is hovered over */
}
.dashed_bottom_border {
    font-weight: 700;
}
.sub_item_body {
    margin-left: 45px;
}
ion-segment-button {
    background: #fff;
    margin-right: 1px;
    font-size: 12px;
    text-transform: unset;
}
.bottomSummary {
    margin-top: 20px;
    max-width: 600px;
}
.bottomSummary .segment-button-checked {
    background: #fff !important;
    --indicator-color: none;
}
.bottomSummary ion-segment-button {
    background: #e6e6e6;
    margin-right: 5px;
    border-top-right-radius: 10px;
    border-top-left-radius: 10px;
    text-transform: unset;
    font-style: normal;
    font-weight: 700;
    font-size: 12px;
}
</style>
