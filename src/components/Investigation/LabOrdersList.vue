<template>
    <div class="container">
        <div class="table-responsive">
            <DataTable ref="dataTable" :options="options" :data="tableData" class="display nowrap modern-table" width="100%">
                <thead>
                    <tr>
                        <th v-for="head in header" :key="head">{{ head }}</th>
                    </tr>
                </thead>
            </DataTable>
        </div>
        <LabViewResultsModal :popoverOpen="openResultsModal" :content="labResultsContent" @closeModal="openResultsModal = false" />
        <LabModal :popoverOpen="openModal" @saved="closeModal" @closeModal="openModal = false" />
        <SendToLabConfirmationModal
            :isOpen="sendToLabModalOpen"
            :title="'Send Patient to Lab'"
            :closeModalFunc="dismiss"
            :onYes="handleSendToLab"
            :onNo="dismiss"
        />
    </div>
</template>

<script setup lang="ts">
import { IonContent, IonHeader, IonItem, IonList, IonTitle, IonToolbar, IonMenu } from "@ionic/vue";
import { checkmark, pulseOutline } from "ionicons/icons";
import { ref, computed, onMounted, watch, nextTick } from "vue";
import { useRouter, useRoute } from "vue-router";
import { storeToRefs } from "pinia";
import { icons } from "@/utils/svg";
import { ObservationService } from "@/services/observation_service";
import { useDemographicsStore } from "@/stores/DemographicStore";
import { useLabResultsStore } from "@/stores/LabResults";
import { useInvestigationStore } from "@/stores/InvestigationStore";
import { usePatientList } from "@/apps/OPD/stores/patientListStore";
import HisDate from "@/utils/Date";
import { iconGraph, iconList } from "@/utils/SvgDynamicColor";
import { OrderService } from "@/services/order_service";
import DynamicButton from "@/components/DynamicButton.vue";
import table from "@/components/DataViews/tables/ReportDataTable";
import DashBox from "@/components/DashBox.vue";
import { PatientLabService } from "@/services/lab/patient_lab_service";
import { createModal, toastDanger, toastSuccess } from "@/utils/Alerts";
import { PatientLabResultService } from "@/services/patient_lab_result_service";
import LabModal from "@/components/Investigation/EnterLabResultsModal.vue";
import LabViewResultsModal from "@/components/Investigation/LabViewResultsModal.vue";
import AddLabTestModal from "@/components/Investigation/AddLabTestModal.vue";
import CheckInConfirmationModal from "@/components/Modal/CheckInConfirmationModal.vue";
import { Service } from "@/services/service";
import { getUserLocation } from "@/services/userService";
import { PatientOpdList } from "@/services/patient_opd_list";
import dates from "@/utils/Date";
import SetUserRole from "@/views/Mixin/SetUserRole.vue";
import DataTable from "datatables.net-vue3";
import DataTablesCore from "datatables.net";
import DataTablesResponsive from "datatables.net-responsive";
import "datatables.net-buttons";
import "datatables.net-buttons/js/buttons.html5";
import "datatables.net-buttons-dt";
import "datatables.net-responsive";
import "datatables.net-select";
import "datatables.net-buttons";
import { toastWarning, popoverConfirmation } from "@/utils/Alerts";
import { LabOrder } from "@/services/lab_order";
import { ConceptService } from "@/services/concept_service";
import { getOfflineRecords, savePatientRecord } from "@/services/offline_service";
import SendToLabConfirmationModal from "@/components/Investigation/SendToLabConfirmationModal.vue";
import { UserService } from "@/services/user_service";
import { StagesService } from "@/apps/OPD/services/stages_service";

// Props
interface Props {
    propOrders?: any[];
}

const props = withDefaults(defineProps<Props>(), {
    propOrders: () => [],
});

// Router
const router = useRouter();
const route = useRoute();

// Stores
const demographicsStore = useDemographicsStore();
const labResultsStore = useLabResultsStore();
const investigationStore = useInvestigationStore();
const patientListStore = usePatientList();

const { patient } = storeToRefs(demographicsStore);
const { labResults } = storeToRefs(labResultsStore);
const { investigations } = storeToRefs(investigationStore);
const { patientsWaitingForVitals, patientsWaitingForConsultation, patientsWaitingForLab, patientsWaitingForDispensation } =
    storeToRefs(patientListStore);

// Reactive data
const dataTable = ref();
const tableData = ref<any[]>([]);
const header = ref(["Lab Test", "Specimen", "Accession Number", "Order Date", "Result", "Action"]);
const displayGraph = ref(true);
const displaySendToLab = ref(false);
const sendToLabModalOpen = ref(false);
const orders = ref<any[]>([]);
const userRoles = ref<any[]>([]);
const listOrders = ref<any[]>([]);
const listResults = ref<any[]>([]);
const hasPatientsWaitingForLab = ref(false);
const labResultsContent = ref<any>("");
const openModal = ref(false);
const openResultsModal = ref(false);
const service = ref<any>("");

// Icons and UI data
const iconsContent = icons;
const graphIcon = iconGraph(["#006401"]);
const listIcon = iconList(["#636363"]);

// DataTable options
const options = ref({
    responsive: true,
    select: false,
    layout: {
        topStart: "buttons",
        topEnd: "search",
        bottomStart: "info",
        bottomEnd: "paging",
    },
    ordering: false,
    buttons: [
        {
            text: " <b>+ Add other tests </b>",
            className: "add-test text-white",
            action: async () => {
                await openEnterResultModal();
            },
        },
        {
            text: " <b>Send to Lab </b>",
            className: "send-lab text-white",
            attr: {
                id: "send-to-lab-btn",
            },

            action: async () => {
                await openSendToLabModal();
            },
        },
    ],
});

// Computed properties
const hasEnterResults = computed(() => {
    return listOrders.value.some((item: any) => item.btn && item.btn.includes("enter_results"));
});

// Methods
const closeModal = async () => {
    openModal.value = false;
    await setListData();
};

const hideSendToLabButton = () => {
    const button = document.getElementById("send-to-lab-btn");
    if (button) {
        button.style.display = "none";
    }
};

const showSendToLabButton = () => {
    const button = document.getElementById("send-to-lab-btn");
    if (button) {
        button.style.display = "inline";
    }
};

const sendToLabButtonCheck = () => {
    const isSendToLab = UserService.getProgramID() == 14 && !UserService.isLabTechnician() && displaySendToLab.value;
    if (isSendToLab) {
        showSendToLabButton();
    } else {
        hideSendToLabButton();
    }
};

const saveTest = async (data: any) => {
    const investigation = [
        {
            concept_id: await ConceptService.getConceptID(data.name, true),
            name: data.name,
            specimen: data.specimen,
            reason: "Routine",
            specimenConcept: await ConceptService.getConceptID(data.specimen, true),
        },
    ];
    const labOrder = await OrderService.buildLabOrders("", investigation);
    let order = labOrder[0];
    order.order_date = order.date;
    order.specimen.name = await ConceptService.getConceptName(order.specimen.concept_id);
    order.tests[0].name = await ConceptService.getConceptName(order.tests[0].concept_id);
    patient.value.labOrders.unsaved.push(order);
    await savePatientRecord(patient.value);
};

const openEnterResultModal = async () => {
    const dataToPass = { title: "name" };
    await createModal(AddLabTestModal, { class: "lab-results-modal" }, true, dataToPass);
    await setListData();
};

const openSendToLabModal = async () => {
    sendToLabModalOpen.value = true;
};

const handleSendToLab = async () => {
    try {
        const locationId = String(localStorage.getItem("locationID"));
        if (!locationId) {
            toastDanger("Location not found");
            return;
        }

        await StagesService.addPatientToStage(patient.value, "LAB");

        await patientListStore.refresh(locationId);
        sendToLabModalOpen.value = false;
        await router.push("home");
        toastSuccess("The patient successfully sent to the lab. You may now attend to other patients.");
    } catch (error) {
        console.error("Error sending to lab:", error);
        toastDanger("Failed to send patient to lab");
    } finally {
        sendToLabModalOpen.value = false;
    }
};

const dismiss = () => {
    sendToLabModalOpen.value = false;
};

const fetchPatientLabStageData = async () => {
    const location = await getUserLocation();
    const locationId = location ? location.code : null;

    if (locationId) {
        const LabPatients = await PatientOpdList.getPatientList("LAB", locationId);
        await patientListStore.refresh(locationId);
        if (patient.value.patientID) {
            hasPatientsWaitingForLab.value = LabPatients.some((p: any) => p.patient_id === patient.value.patientID);
        }
    }
};

const updateInvestigationWizard = async () => {
    if (!patient.value?.labOrders) return;
    orders.value = [...patient.value?.labOrders?.saved, ...patient.value?.labOrders?.unsaved];
    if (!orders.value) return;

    const filteredArray = await orders.value.filter((obj: any) => {
        return HisDate.toStandardHisFormat(HisDate.sessionDate()) === HisDate.toStandardHisFormat(obj.order_date);
    });

    if (filteredArray.length > 0) {
        investigations.value[0].selectedData = filteredArray;
    } else {
        investigations.value[0].selectedData = "";
    }
};

const voidLabOrder = async (data: any, event: any) => {
    const deleteConfirmed = await popoverConfirmation(`Do you want to delete ${data.tests[0].name} ?`, event);
    if (deleteConfirmed) {
        const patientData = JSON.parse(JSON.stringify(patient.value));
        const savedOrders = patientData.labOrders.saved;
        const orderExists = savedOrders.some((order: any) => order.order_date === data.order_date && order?.tests[0]?.name == data?.tests[0]?.name);

        if (orderExists) {
            patientData.labOrders.saved = removeTestByNameAndDate(patientData.labOrders.saved, data.tests[0].name, data.order_date);
            (patientData.labOrders ??= {}).voided ??= [];
            patientData.labOrders.voided.push({
                orderId: data.id,
                reason: "Mistake entry",
            });
        } else {
            patientData.labOrders.unsaved = removeTestByNameAndDate(patientData.labOrders.unsaved, data.tests[0].name, data.date);
        }
        await savePatientRecord(patientData);
    }
    await setListData();
};

const removeTestByNameAndDate = (labOrders: any, testName: any, orderDate: any) => {
    return labOrders.filter((order: any) => {
        if (order.order_date === orderDate) {
            order.tests = order.tests.filter((test: any) => test.name !== testName);
        }
        return order.tests.length > 0;
    });
};

const openResultsForm = async (test: any) => {
    let testIndicators;
    if (Service.getUseIndexDBStatus() || Service.getModsStatus())
        testIndicators = await getOfflineRecords("testResultIndicators", { whereClause: { test_type_id: test.concept_id } });
    else testIndicators = await PatientLabResultService.getTestIndicatorsWithID(test.concept_id);

    const indicators = [
        test,
        {
            validationStatus: "",
            data: {
                rowData: [
                    {
                        colData: [],
                    },
                ],
            },
        },
    ] as any;

    testIndicators.forEach((item: any) => {
        let data = {
            inputHeader: item.name,
            value: "",
            colSize: 3,
            id: item.concept_id,
            name: item.name,
            required: true,
            eventType: "input",
            alertsErrorMassage: "",
        } as any;

        if (item.name == "RBS") {
            data = {
                inputHeader: item.name,
                value: "",
                colSize: 3,
                id: item.concept_id,
                name: item.name,
                required: true,
                eventType: "input",
                alertsErrorMassage: "",
                unit: "mg/dL",
            };
        }
        if (item.name == "FBS") {
            data = {
                inputHeader: item.name,
                value: "",
                colSize: 3,
                id: item.concept_id,
                name: item.name,
                required: true,
                eventType: "input",
                alertsErrorMassage: "",
                unit: "mg/dL",
            };
        }
        if (
            item.name == "MRDT" ||
            item.name == "Tuberculosis program" ||
            item.name == "Vdrl" ||
            item.name == "Hepatitis B" ||
            item.name == "Lam" ||
            item.name == "CrAg" ||
            item.name == "CD4 count" ||
            //dip Stick
            item.name == "Leukocytes" ||
            item.name == "Protein" ||
            item.name == "Nitrite" ||
            item.name == "Urine Ketones" ||
            //hiv
            item.name == "HIV test"
        ) {
            let multiData = [] as any;
            if (item.name == "MRDT" || item.name == "Vdrl" || item.name == "Hepatitis B" || item.name == "CrAg" || item.name == "Lam") {
                multiData = [
                    { id: "1", name: "Positive" },
                    { id: "2", name: "Negative" },
                    { id: "3", name: "Invalid" },
                ];
            }

            if (item.name == "Tuberculosis program") {
                multiData = [
                    { id: "1", name: "Scanty" },
                    { id: "2", name: "Negative" },
                    { id: "3", name: "1+" },
                    { id: "4", name: "2+" },
                    { id: "5", name: "3+" },
                ];
            }
            if (item.name == "CD4 count") {
                multiData = [
                    { id: "1", name: "below reference line" },
                    { id: "2", name: "above reference line" },
                ];
            }
            if (
                item.name == "Leukocytes" ||
                item.name == "Protein" ||
                item.name == "Nitrite" ||
                item.name == "Ketones" ||
                item.name == "Urine Ketones"
            ) {
                multiData = [
                    { id: "2", name: "Negative" },
                    { id: "1", name: "Trace" },
                    { id: "3", name: "1+" },
                    { id: "4", name: "2+" },
                    { id: "5", name: "3+" },
                    { id: "6", name: "4+" },
                ];
            }

            if (item.name == "HIV test") {
                multiData = [
                    { id: "2", name: "Positive" },
                    { id: "1", name: "Negative" },
                    { id: "3", name: "Invalid" },
                ];
            }

            data = {
                inputHeader: item.name,
                icon: icons.search,
                value: "",
                name: "units",
                eventType: "input",
                alertsErrorMassage: "",
                isSingleSelect: true,
                trackBy: "id",
                multiSelectData: multiData,
                id: item.concept_id,
                idName: "district_id",
            } as any;
        }

        indicators[1].data.rowData[0].colData.push(data);
    });

    labResultsStore.setLabResults(indicators);
    openModal.value = true;
    orders.value = [...patient.value.labOrders.saved, ...patient.value.labOrders.unsaved];
};

const viewLabOrder = async (labResults: any) => {
    labResultsContent.value = labResults;
    openResultsModal.value = true;
};

const setListData = async () => {
    if (!patient.value.labOrders) return;
    orders.value = [...patient.value.labOrders.saved, ...patient.value.labOrders.unsaved];

    // After updating table data and displaySendToLab, re-render DataTables buttons

    const generatedTableData: any = generateListItems(orders.value, "order");

    const predefineTests =
        Service.getProgramID() == 32
            ? [
                  [
                      "FBS",
                      "Blood",
                      "",
                      "",
                      "",
                      `<button class="btn btn-outline-success btn-sm order-btn" data-id='${JSON.stringify({
                          name: "FBS",
                          specimen: "Blood",
                      })}'>Order Test</button> `,
                  ],
                  [
                      "HbA1c",
                      "Blood",
                      "",
                      "",
                      "",
                      `<button class="btn btn-outline-success btn-sm order-btn" data-id='${JSON.stringify({
                          name: "HbA1c",
                          specimen: "Blood",
                      })}'>Order Test</button> `,
                  ],
                  [
                      "RBS",
                      "Blood",
                      "",
                      "",
                      "",
                      `<button class="btn btn-outline-success btn-sm order-btn" data-id='${JSON.stringify({
                          name: "RBS",
                          specimen: "Blood",
                      })}'>Order Test</button> `,
                  ],
              ]
            : [];

    const uniquePredefineTests = predefineTests.filter((predefTest) => {
        return !generatedTableData.some((tableRow: any) => tableRow[0] === predefTest[0]);
    });

    const duplicateTests = generatedTableData.filter((tableRow: any) => {
        return predefineTests.some((predefTest) => predefTest[0] === tableRow[0]);
    });

    const uniqueTableDataTests = generatedTableData.filter((predefTest: any) => {
        return !duplicateTests.some((tableRow: any) => tableRow[0] === predefTest[0]);
    });

    tableData.value = [...duplicateTests, ...uniquePredefineTests, ...uniqueTableDataTests];
    sendToLabButtonCheck();
    await updateInvestigationWizard();
    DataTable.use(DataTablesCore);
};

const generateListItems = (data: any, type: any) => {
    let result = null;
    displaySendToLab.value = false;
    if (data.length > 0) {
        return data.flatMap((item: any) => {
            return item.tests.flatMap((test: any) => {
                const enter_results = `<button class="btn btn-outline-success btn-sm result-btn" data-id='${JSON.stringify(
                    test
                )}'>Enter Result</button> `;
                const attach = `<button class="btn btn-outline-secondary btn-sm attach-btn" data-id='${JSON.stringify(test)}'>${
                    iconsContent.attach2
                }</button>`;
                const view = `<button class="btn btn-outline-secondary btn-sm view-btn" data-id='${JSON.stringify(test)}'>${
                    iconsContent.view2
                }</button> `;
                let resultDisplay = enter_results + attach;

                if (test?.result?.length == 1) {
                    resultDisplay = test?.result != null ? test?.result[0]?.value_modifier + test?.result[0]?.value : null;
                } else if (test?.result?.length > 1) {
                    result = test?.result;
                    resultDisplay = view;
                } else if (item?.accession_number) {
                    displaySendToLab.value = true;
                }

                let print = "";
                if (!item?.accession_number) {
                    resultDisplay = "";
                } else {
                    print = `<button class="btn btn-outline-secondary btn-sm" data-id='${JSON.stringify(item)}'>${iconsContent.print2}</button>`;
                }

                return [
                    [
                        test?.name,
                        item?.specimen.name,
                        item?.accession_number || "",
                        HisDate.toStandardHisFormat(item?.order_date),
                        resultDisplay,
                        print +
                            `
                        <button class="btn btn-outline-danger btn-sm delete-btn" data-id='${JSON.stringify(item)}'>${iconsContent.delete2}</button>
                        `,
                    ],
                ];
            });
        });
    } else {
        return [];
    }
};

// Lifecycle hooks
onMounted(async () => {
    if (Service.getProgramID() == 14) {
        options.value.buttons.push();
    }
    await updateInvestigationWizard();
    await setListData();

    nextTick(() => {
        const table = (dataTable.value as any).dt;
        table.columns.adjust().draw();

        table.on("click", ".result-btn", (e: Event) => {
            const data: any = (e.target as HTMLElement).getAttribute("data-id");
            openResultsForm(JSON.parse(data));
        });

        table.on("click", ".view-btn", (e: Event) => {
            const data: any = (e.target as HTMLElement).getAttribute("data-id");
            viewLabOrder(JSON.parse(data));
        });

        table.on("click", ".delete-btn", (e: Event) => {
            const data: any = (e.target as HTMLElement).getAttribute("data-id");
            voidLabOrder(JSON.parse(data), e);
        });

        table.on("click", ".order-btn", (e: Event) => {
            const data: any = (e.target as HTMLElement).getAttribute("data-id");
            saveTest(JSON.parse(data));
        });
    });

    orders.value = props.propOrders;
    service.value = new PatientLabService(patient.value.patientID);
    userRoles.value = await Service.getUserRoles();
});

// Watchers
watch(
    () => route,
    async () => {
        await setListData();
    },
    { deep: true }
);

watch(
    () => patient.value,
    async () => {
        await setListData();
    },
    { deep: true }
);
</script>

<style>
@import "datatables.net-dt";
@import "datatables.net-buttons-dt";
@import "datatables.net-responsive-dt";
@import "datatables.net-select-dt";

.table-responsive {
    width: 100%;
    overflow-x: auto;
}
div.dt-buttons > .dt-button:first-child {
    border: 1px solid #fff;
    background: #046c04;
    border-radius: 5px;
}
div.dt-buttons > .dt-button:hover:not(.disabled) {
    background: #188907 !important;
    border: 1px solid #fff !important;
}
.dt-button.send-lab.text-white {
    background: #046c04;
    border: none;
    border-radius: 4px;
}
.dt-button.display-none {
    display: none !important;
}
</style>
<style scoped>
.bmi_blood {
    font-size: 14px;
    font-weight: 500;
    border: 1px solid #ddeedd;
    border-radius: 4px;
    padding: 4px;
    min-width: 190px;
}
.graphBtn {
    font-size: 14px;
    font-weight: 500;
    border: 1px solid #ddeedd;
    border-radius: 4px;
    padding: 4px;
    min-width: 90px;
}
._active {
    background-color: #ddeedd;
    color: #006401;
    padding: 5px;
    border-radius: 4px;
}
.iconBg {
    background: #ddeedd;
    padding: 2px;
    border-radius: 5px;
}
.show_more {
    color: #006401;
    padding: 10px;
}
.no-margin-left {
    margin: 0;
    display: flex;
    justify-content: flex-start;
    background: lightcyan;
}
.scrollable-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    width: 100%;
}
</style>
