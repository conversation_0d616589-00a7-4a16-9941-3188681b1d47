<template>
  <ion-modal :is-open="isOpen" :show-backdrop="true" @didDismiss="closeModal">
    <ion-content class="modal-wrapper">
      <div class="modal-header">
        <ion-title class="modal-title">{{ title }}</ion-title>
        <ion-button fill="clear" class="close-btn" @click="closeModal">
          <ion-icon :icon="closeCircleOutline()" />
        </ion-button>
      </div>

      <div class="modal-body">
        <slot>
          Sending the patient to the lab will pause this consultation.
          You'll return to the waiting list while waiting for lab results this patient.
        </slot>
      </div>

      <div class="modal-footer">
        <ion-button fill="outline" color="danger" class="action-btn cancel-btn" @click="onNo">
          Cancel
        </ion-button>
        <DynamicButton
            name="Confirm"
            color="success"
            class="action-btn confirm-btn"
            fill="solid"
            @click="onYes"
        />
      </div>
    </ion-content>
  </ion-modal>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';
import {
  IonModal,
  IonTitle,
  IonButton,
  IonContent,
  IonIcon,
} from '@ionic/vue';
import { closeCircleOutline } from 'ionicons/icons';
import DynamicButton from "@/components/DynamicButton.vue";

export default defineComponent({
  name: 'CheckInConfirmationModal',
  components: {
    IonModal,
    IonTitle,
    IonButton,
    IonContent,
    IonIcon,
    DynamicButton,
  },
  props: {
    isOpen: { type: Boolean, required: true, default: false },
    closeModalFunc: { type: Function as PropType<() => void>, required: true },
    onYes: { type: Function as PropType<() => void>, required: true },
    onNo: { type: Function as PropType<() => void>, required: true },
    title: { type: String, required: true },
  },
  methods: {
    closeCircleOutline() {
      return closeCircleOutline;
    },
    closeModal() {
      this.closeModalFunc();
    },
  },
});
</script>

<style scoped>
ion-modal {
  --background: rgba(0, 0, 0, 0.9);
  --width: 100%;
  --height: 200px;
  --max-width: 700px;
  --max-height: 100%;
  border-radius: 16px;
  overflow: hidden;
}

.modal-wrapper {
  display: flex;
  flex-direction: column;
  padding: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.98) 100%);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.modal-header {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.05) 100%);
  backdrop-filter: blur(8px);
  border-bottom: 1px solid rgba(226, 232, 240, 0.6);
  padding: 2px 14px 4px;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
  position: relative;
}

.modal-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.6) 0%, rgba(147, 51, 234, 0.4) 100%);
}

.modal-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: #1e293b;
  line-height: 1.4;
  max-width: 90%;
  white-space: normal;
  word-break: break-word;
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.close-btn {
  font-size: 1.4rem;
  color: #64748b;
  --padding-start: 8px;
  --padding-end: 8px;
  margin-left: auto;
  border-radius: 50%;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.7);
}

.close-btn:hover {
  color: #ef4444;
  background: rgba(255, 255, 255, 0.9);
  transform: scale(1.05);
}

.modal-body {
  flex: 1;
  font-size: 1rem;
  color: #475569;
  line-height: 1.6;
  padding: 24px;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(5px);
}



.action-btn {
  min-width: 100px;
  height: 40px;
  font-size: 0.9rem;
  border-radius: 8px;
  text-transform: none;
  padding: 0 20px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.cancel-btn {
  --border-color: #ef4444;
  --color: #ef4444;
  backdrop-filter: blur(4px);
}
</style>