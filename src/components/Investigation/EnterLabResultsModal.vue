<template>
    <ion-modal :is-open="popoverOpen" :show-backdrop="true" @didDismiss="$emit('closePopoover', false)" :keyboard-close="keyboardClose">
        <ion-header>
            <ion-toolbar>
                <ion-title
                    ><b>Enter lab results for ({{ labResults[0].name }}) test</b></ion-title
                >
                <ion-buttons slot="end">
                    <ion-button @click="$emit('closeModal')">Close</ion-button>
                </ion-buttons>
            </ion-toolbar>
        </ion-header>
        <ion-content class="ion-padding">
            <div class="modal_wrapper">
                <div class="center text_12">
                    <ion-row>
                        <basic-form :contentData="labResults"> </basic-form>
                    </ion-row>
                </div>
            </div>
        </ion-content>
        <ion-footer :translucent="true">
            <ion-toolbar>
                <DynamicButton @click="saveResults()" name="Save" fill="clear" iconSlot="icon-only" style="float: right" />
            </ion-toolbar>
        </ion-footer>
    </ion-modal>
</template>

<script lang="ts">
import { IonButtons, IonButton, IonModal, IonAvatar, IonImg, IonLabel, IonPage, IonFooter } from "@ionic/vue";
import { IonContent, IonHeader, IonItem, IonList, IonTitle, IonToolbar, IonMenu, modalController } from "@ionic/vue";
import { defineComponent } from "vue";
import { checkmark, pulseOutline } from "ionicons/icons";
import { ref } from "vue";
import { icons } from "@/utils/svg";
import { resetPatientData } from "@/services/reset_data";
import { useLabResultsStore } from "@/stores/LabResults";
import { mapState } from "pinia";
import BasicForm from "@/components/BasicForm.vue";
import { PatientLabResultService } from "@/services/patient_lab_result_service";
import HisDate from "@/utils/Date";
import { useDemographicsStore } from "@/stores/DemographicStore";
import {
    modifyCheckboxInputField,
    getCheckboxSelectedValue,
    getRadioSelectedValue,
    getFieldValue,
    modifyRadioValue,
    modifyFieldValue,
} from "@/services/data_helpers";
import DynamicButton from "@/components/DynamicButton.vue";
import { toastSuccess } from "@/utils/Alerts";
import { getOfflineRecords, savePatientRecord } from "@/services/offline_service";
import { Service } from "@/services/service";

export default defineComponent({
    components: {
        IonButtons,
        IonButton,
        IonModal,
        IonHeader,
        IonContent,
        IonToolbar,
        IonTitle,
        IonItem,
        IonList,
        IonAvatar,
        IonImg,
        IonLabel,
        IonPage,
        IonMenu,
        BasicForm,
        IonFooter,
        DynamicButton,
    },
    data() {
        return {
            popoverStatus: null,
        };
    },
    props: {
        keyboardClose: {
            type: Boolean,
            default: false,
        },
        keepContentsMounted: {
            type: Boolean,
            default: false,
        },
        content: {
            type: Object,
            default: {},
        },
        popoverOpen: {
            type: Boolean,
            default: false,
        },
        event: {
            type: Event,
            default: "",
        },
        title: {
            type: String,
            default: "",
        },
    },
    computed: {
        ...mapState(useDemographicsStore, ["patient"]),
        ...mapState(useLabResultsStore, ["labResults"]),
    },
    mounted() {},
    methods: {
        dismiss() {
            modalController.dismiss();
        },
        async saveResults() {
            const results = await this.buildResults();
            this.patient.labOrders.results = [
                {
                    encounter_id: "",
                    date: HisDate.sessionDate(),
                    measures: results,
                    test_id: this.labResults[0].id,
                },
            ];

            // Check unsaved orders first
            let matchingOrderFound = false;
            for (let i = 0; i < this.patient.labOrders.unsaved.length; i++) {
                if (this.patient.labOrders.unsaved[i].tests[0].id === this.labResults[0].id) {
                    // Update the test result in the unsaved array
                    if (this.patient.labOrders.unsaved[i].tests && this.patient.labOrders.unsaved[i].tests.length > 0) {
                        this.patient.labOrders.unsaved[i].tests[0].result = results;
                        matchingOrderFound = true;
                        break;
                    }
                }
            }

            // If not found in unsaved, check saved orders
            if (!matchingOrderFound) {
                for (let i = 0; i < this.patient.labOrders.saved.length; i++) {
                    if (this.patient.labOrders.saved[i].tests[0].id === this.labResults[0].id) {
                        // Update the test result in the saved array
                        if (this.patient.labOrders.saved[i].tests && this.patient.labOrders.saved[i].tests.length > 0) {
                            this.patient.labOrders.saved[i].tests[0].result = results;
                            break;
                        }
                    }
                }
            }

            await savePatientRecord(this.patient);
            toastSuccess("Saved successfully");
            this.$emit("saved");
        },

        async buildResults() {
            let measures = [] as any;
            await Promise.all(
                this.labResults[1].data.rowData[0].colData.map(async (item: any) => {
                    let testIndicators;
                    if (Service.getUseIndexDBStatus() || Service.getModsStatus())
                        testIndicators = await getOfflineRecords("testResultIndicators", { whereClause: { test_type_id: item.id } });
                    else testIndicators = await PatientLabResultService.getTestIndicatorsWithID(item.id);

                    measures.push({
                        indicator: {
                            name: testIndicators[0]?.name,
                            concept_id: item.id,
                        },
                        value: item.value.name || item.value || "not done",
                        value_modifier: "",
                        value_type: "text",
                    });
                })
            );

            return measures;
        },
        async nav(url: any, action: any) {
            const demographicsStore = useLabResultsStore();
            if (action == "not_save") {
                await resetPatientData();
                demographicsStore.setLabResults(false);
            } else {
                demographicsStore.setLabResults(true);
            }
            this.dismiss();
            this.$router.push(url);
        },
    },
});
</script>
<style scoped>
ion-footer {
    --ion-toolbar-background: #f4f4f4f4;
}
ion-modal {
    --width: 90%;
    --height: 94%;
}
</style>
