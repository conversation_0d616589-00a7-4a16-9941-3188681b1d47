<template>
    <div style="border: 1px dotted #ccc;border-radius: 15px;" class="ion-padding">
        <ion-label>
            <b>{{ label }} </b>
            <ion-text v-if="required" style="color: red">*</ion-text>
            <br/>
            <ion-note v-if="errorMessage">
                {{ errorMessage }}
            </ion-note>
        </ion-label>
        <SelectFacility :show_error="false" :selected_district_ids="[]" :selected_location="null"
            @facilitySelected="(value: any) => { onChange(value.selected_location.name); }" />
    </div>
</template>
<script lang="ts" setup>
import { PropType } from 'vue';
import {
    IonLabel,
    IonText,
    IonNote
} from "@ionic/vue"
import SelectFacility from "@/apps/OPD/components/SelectFacility.vue";

defineProps({
    label: String,
    modelValue: String, // Use modelValue for v-model compatibility
    required: <PERSON><PERSON>an,
    disabled: <PERSON>olean,
    errorMessage: String,
    onChange: Object as PropType<(value: string | number) => void>
})

const emit = defineEmits(['update:modelValue', 'onChange']); // Define the emit event

function onChange(facility: string) {
    emit('update:modelValue', facility); // Emit the updated value
    emit('onChange', facility)
}
</script>
<style scoped>
ion-note {
    color: red;
    font-weight: bold;
    font-style: italic;
}
</style>