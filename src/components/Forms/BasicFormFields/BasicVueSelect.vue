<template>
    <div style="border: 1px dotted #ccc;border-radius: 15px;" class="ion-padding">
        <ion-label>
            <b>{{ label }}</b>
            <span v-if="required" style="color: red">*</span>
        </ion-label>
        <VueMultiselect v-model="val"  teleport @update:model-value="handleTestChange" :multiple="false"
            :hide-selected="false" :close-on-select="true" openDirection="auto" tag-placeholder=""
            :placeholder="placeholder" selectLabel="" label="label" :searchable="true" track-by="label"
            :options="options" :disabled="disabled??false" />
        <ion-note v-if="errorMessage" color="danger">
            {{ errorMessage }}
        </ion-note>
    </div>
</template>
<script lang="ts" setup>
import VueMultiselect from "vue-multiselect";
import {
    IonLabel,
    IonNote
} from "@ionic/vue"
import { PropType, ref } from "vue";

interface Option {
    label: string;
    value: any;
}

const val = ref<any>(null)

defineProps({
    label: String,
    required: Boolean,
    disabled: Boolean,
    placeholder: String,
    errorMessage: String,
    options: Object as PropType<Option[]>,
    modelValue: Object as PropType<any>, // Use modelValue for v-model compatibility
})

const emit = defineEmits(['update:modelValue', 'onChange']); // Define the emit event

function handleTestChange(newValue: any) {
    emit('update:modelValue', newValue); // Emit the updated value
    emit('onChange', newValue); // Emit the updated value
}
</script>