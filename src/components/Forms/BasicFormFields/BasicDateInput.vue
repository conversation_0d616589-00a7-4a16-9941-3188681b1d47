<template>
    <div style="border: 1px dotted #ccc;border-radius: 15px;" class="ion-padding">
        <ion-label class="form-label">
            {{ label }}
            <span v-if="required" style="color: red">*</span>
        </ion-label>
        <DatePicker place_holder="" :date_prop="''" @date-up-dated="(val: any) => { onChange(val.value.standardDate); }"
            :error="false" />
        <ion-note v-if="errorMessage" color="danger">
            {{ errorMessage }}
        </ion-note>
    </div>
</template>
<script lang="ts" setup>
import {
    IonLabel,
    IonNote,
} from "@ionic/vue"
import DatePicker from "@/components/DatePicker.vue";

const emit = defineEmits(['update:modelValue', 'onChange']); // Define the emit event

defineProps({
    label: String,
    modelValue: String,
    required: Boolean,
    errorMessage: String,
})

function onChange(value: any) {
    emit('update:modelValue', value); // Emit the updated value
    emit('onChange', value)
}
</script>
<style scoped>
ion-note {
    color: red;
    font-weight: bold;
    font-style: italic;
}
</style>