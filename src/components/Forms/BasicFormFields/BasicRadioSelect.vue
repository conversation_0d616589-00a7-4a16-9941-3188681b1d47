<template>
    <div style="border: 1px dotted #ccc;border-radius: 15px;" class="ion-padding">
        <ion-label>
            <b>{{ label }}</b>
            <ion-text v-if="required" style="color: red">*</ion-text>
            <p>
                <ion-note v-if="errorMessage" color="danger">{{ errorMessage }}</ion-note>
            </p>
        </ion-label>
        <ion-radio-group style="margin-left: 30px;" @ion-change="onChange" :value="modelValue">
            <ion-item lines="none" v-for="(option, index) in options ?? []" :key="index">
                <ion-radio 
                    :disabled="disableValue ? option.value === disableValue : false"
                    :value="option.value ?? option.label" slot="start"></ion-radio>
                <ion-label style="margin-left: 10px;">{{ option.label }}</ion-label>
            </ion-item>
        </ion-radio-group>
    </div>
</template>
<script lang="ts" setup>
import {
    IonRadioGroup,
    IonRadio,
    IonItem,
    IonLabel,
    IonText,
    IonNote
} from "@ionic/vue"
import { PropType } from "vue";

interface Option {
    label: string;
    value: any;
}

defineProps({
    label: String,
    modelValue: Object as PropType<any>, // Use modelValue for v-model compatibility
    required: Boolean,
    errorMessage: String,
    disableValue: String,
    options: Object as PropType<Option[]>,
})

const emit = defineEmits(['update:modelValue', 'onChange']); // Define the emit event

function onChange(event: any) {
    const newValue = event.target.value;
    emit('update:modelValue', newValue); // Emit the updated value
    emit('onChange', newValue); // Emit the updated value
}
</script>
<style scoped>
ion-note {
    color: red;
    font-weight: bold;
    font-style: italic;
}
</style>