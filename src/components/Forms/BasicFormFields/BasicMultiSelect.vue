<template>
    <div style="border: 1px dotted #ccc;border-radius: 15px;" class="ion-padding">
        <ion-label>
            <b>{{ label }}</b>
            <ion-text v-if="required" color="danger">*</ion-text>
        </ion-label>
        <br />
        <ion-note v-if="errorMessage" color="danger">{{ errorMessage }}</ion-note>
        <p></p>
        <ion-list>
            <ion-item v-for="(option, index) in modelValue" :key="index">
                <ion-checkbox :disabled="disabled || option.disabled" :checked="option.checked" slot="start"
                    @ionChange="(e: any) => onChange(option, e.detail.checked)">
                </ion-checkbox>
                <ion-label style="margin-left:10px;">
                    {{ option.label }}
                    <p v-if="option.status" style="color:green;font-style: italic;">
                        {{ option.status }}
                    </p>
                </ion-label>

            </ion-item>
        </ion-list>
        <p></p>
        <ion-note v-if="errorMessage" color="danger">{{ errorMessage }}</ion-note>
    </div>
</template>
<script lang="ts" setup>
import {
    IonList,
    IonCheckbox,
    IonItem,
    IonLabel,
    IonText,
    IonNote
} from "@ionic/vue"
import { PropType } from "vue";

interface Option {
    label: string;
    value: any;
    status?: string;
    disabled?: boolean;
    checked: boolean;
}

const props = defineProps({
    label: String,
    required: Boolean,
    errorMessage: String,
    disabled: Boolean,
    singleSelect: Boolean,
    modelValue: Object as PropType<Option[]>, // Use modelValue for v-model compatibility
})

const emit = defineEmits(['update:modelValue', 'onChange']); // Define the emit event

function onChange(option: Option, status: any) {
    const options = [...props.modelValue ?? []].map((d: any) => {
        if (props.singleSelect) {
            if (status) {
                return { ...d, checked: d.value === option.value }
            }
        }
        if (d.value === option.value) {
            return { ...d, checked: status }
        }
        return d
    })
    const newOption = { ...option, checked: status }
    emit('update:modelValue', options); // Emit the updated value
    emit('onChange', newOption); // Emit the updated value
}
</script>
<style scoped>
ion-note {
    color: red;
    font-style: italic;
    font-weight: bold;
}
</style>