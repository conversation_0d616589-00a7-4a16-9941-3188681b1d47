<template>
    <div style="border: 1px dotted #ccc;border-radius: 15px;" class="ion-padding">
        <ion-label>
            <b>{{ label }}</b>
            <span v-if="required" style="color: red">*</span>
        </ion-label>
        <ion-select :multiple="multiple" fill="outline" @ionChange="onChange($event.detail.value)" interface="popover"
            :placeholder="placeholder ?? 'Select an option'" :value="modelValue" :compareWith="compareWith">
            <ion-select-option v-for="option in options" :key="option.value" :value="option">
                {{ option.label }}
            </ion-select-option>
        </ion-select>
        <ion-note v-if="errorMessage" color="danger">
            {{ errorMessage }}
        </ion-note>
    </div>
</template>
<script lang="ts" setup>
import {
    IonLabel,
    IonSelect,
    IonSelectOption,
    IonNote
} from "@ionic/vue"
import { PropType, ref } from "vue";

interface Option {
    label: string;
    value: any;
    disabled?: boolean;
    checked?: boolean;
}

defineProps({
    label: String,
    required: Boolean,
    multiple: Boolean,
    errorMessage: String,
    placeholder: String,
    options: Object as PropType<Option[]>,
    modelValue: Object as PropType<any>, // Use modelValue for v-model compatibility
})

const val = ref(null)
const emit = defineEmits(['update:modelValue', 'onChange']); // Define the emit event

// Compare function to help ion-select identify selected option
function compareWith(o1: Option | null, o2: Option | null): boolean {
    if (!o1 || !o2) return false;
    return o1.value === o2.value;
}

function onChange(newValue: any) {
    val.value = newValue.label
    emit('update:modelValue', newValue); // Emit the updated value
    emit('onChange', newValue); // Emit the updated value
}
</script>
<style scoped>
ion-note {
    color: red;
    font-style: italic;
    font-weight: bold;
}
</style>