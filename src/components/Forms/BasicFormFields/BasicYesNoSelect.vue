<template>
    <div style="border: 1px dotted #ccc;border-radius: 15px;" class="ion-padding">
        <ion-label>
            <b>{{ label }} <ion-badge v-if="badgeText">{{ badgeText }}</ion-badge> </b>
            <ion-text v-if="required" style="color: red">*</ion-text>
            <p>
                <ion-note v-if="errorMessage" color="danger">{{ errorMessage }}</ion-note>
            </p>
        </ion-label>
        <ion-radio-group style="margin-left: 30px;" @ion-change="onChange" :value="modelValue">
            <ion-item lines="none">
                <ion-radio value="Yes" slot="start"></ion-radio>
                <ion-label style="margin-left: 10px;">Yes</ion-label>
            </ion-item>
            <ion-item lines="none">
                <ion-radio value="No" slot="start"></ion-radio>
                <ion-label style="margin-left: 10px;">No</ion-label>
            </ion-item>
        </ion-radio-group>
    </div>
</template>
<script lang="ts" setup>
import {
    IonBadge,
    IonRadioGroup,
    IonRadio,
    IonItem,
    IonLabel,
    IonText,
    IonNote
} from "@ionic/vue"

defineProps({
    label: String,
    badgeText: String,
    required: Boolean,
    errorMessage: String,
    modelValue: String
})

const emit = defineEmits(['update:modelValue', 'onChange']); // Define the emit event

function onChange(event: any) {
    const newValue = event.target.value;
    emit('update:modelValue', newValue); // Emit the updated value
    emit('onChange', newValue); // Emit the updated value
}
</script>
<style scoped>
ion-note {
    color: red;
    font-weight: bold;
    font-style: italic;
}
</style>