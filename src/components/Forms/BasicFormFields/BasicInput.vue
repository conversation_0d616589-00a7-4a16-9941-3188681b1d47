<template>
    <div style="border: 1px dotted #ccc;border-radius: 15px;" class="ion-padding">
        <ion-label>
            <b>{{ label }}</b>
            <ion-text v-if="required" color="danger">*</ion-text>
        </ion-label>
        <p></p>
        <ion-input fill="outline" :value="modelValue" :disabled="disabled" :type="inputType" @ion-input="onChange"
            :placeholder="placeholder">
        </ion-input>
        <p></p>
        <ion-note v-if="errorMessage" color="danger">{{ errorMessage }}</ion-note>
    </div>
</template>
<script lang="ts" setup>
import {
    IonLabel,
    IonText,
    IonInput,
    IonNote
} from "@ionic/vue"
import { PropType } from "vue";

defineProps({
    label: String,
    modelValue: String, // Use modelValue for v-model compatibility
    required: Boolean,
    disabled: Boolean,
    errorMessage: String,
    placeholder: String,
    inputType: Object as PropType<'text' | 'number'>,
    onChange: Object as PropType<(value: string | number) => void>
})

const emit = defineEmits(['update:modelValue', 'onChange']); // Define the emit event

function onChange(event: any) {
    const newValue = event.target.value;
    emit('update:modelValue', newValue); // Emit the updated value
    emit('onChange', newValue)
}
</script>
<style scoped>
ion-note {
    color: red;
    font-weight: bold;
    font-style: italic;
}
</style>