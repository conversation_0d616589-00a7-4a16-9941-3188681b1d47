<template>
  <ion-card class="barcode-card ion-text-center">
    <div :style="`color: ${disabled ? 'var(--ion-color-medium)' : 'var(--ion-text-color)'}`">
      <h2>{{ label }}</h2>
    </div>
    <ion-icon :color="disabled ? 'medium' : 'primary'" style="font-size: 5rem;" :icon="barcodeOutline"></ion-icon>
    <ion-item color="light" class="ion-text-center">
      <ion-input :disabled="disabled" ref="barcodeInput" v-model="inputCode" autofocus :debounce="650"
        placeholder="Tap to scan or enter manually" @ion-input="onInputChange" @keyup.enter="emitCode" inputmode="text"
        clear-input />
    </ion-item>
    <div v-if="status" class="barcode-text">
      <span>Status: {{ status }}</span>
    </div>
    <p style="margin-top: 1rem;">
      <ion-button :disabled="disabled" @click="scanBarcode">
        <ion-icon style="margin: 4px;" :icon="cameraOutline" slot="start"></ion-icon>
        Scan with Camera
      </ion-button>
    </p>
  </ion-card>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue';
import { IonButton, IonIcon, IonCard, IonInput, IonItem } from '@ionic/vue';
import { ScannerService } from '@/services/scanner_service';
import { barcodeOutline, cameraOutline } from "ionicons/icons"
import { toastDanger } from '@/utils/Alerts';

defineProps({
  label: {
    type: String,
    default: 'Scan Barcode',
  },
  status: {
    type: String,
    default: '' // Can be 'default', 'success', 'error'
  },
  disabled: {
    type: Boolean,
    default: false
  }
})
const inputCode = ref('');
const barcodeInput = ref<any>(null); // Use 'any' for IonInput component
const scanner = new ScannerService();

function emitCode() {
  emit('code-captured', inputCode.value);
}

function onInputChange(e: any) {
  inputCode.value = e.target.value;
  emitCode();
}

function scanBarcode() {
  scanner.scan().then((code: string | undefined) => {
    if (code) {
      inputCode.value = code;
      emitCode();
    }
  }).catch((e) => {
    toastDanger(e)
  });
}

onMounted(() => {
  nextTick(() => {
    if (barcodeInput.value && typeof barcodeInput.value.setFocus === 'function') {
      barcodeInput.value.setFocus();
    }
  });
});

const emit = defineEmits(['code-captured']);
</script>

<style scoped>
.barcode-card {
  --background: var(--ion-card-background, #fff);
  --color: var(--ion-text-color, #222);
  padding: 1rem;
}

.barcode-text {
  margin-top: 1rem;
  font-weight: bold;
  color: var(--ion-color-primary);
}
</style>
