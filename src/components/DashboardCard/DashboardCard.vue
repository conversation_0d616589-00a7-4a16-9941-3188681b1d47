<template>
  <ion-card
      class="dashboard-card"
      :style="{ borderColor: cardColor, backgroundColor: cardBackground }"
      @click="$emit('cardClick')"
  >
    <ion-icon :icon="icon" class="card-icon" />
    <div class="card-count">{{ count }}</div>
    <div class="card-label">{{ label }}</div>
  </ion-card>
</template>

<script setup lang="ts">
import { IonCard, IonIcon } from '@ionic/vue';

const props = defineProps<{
  icon: string;
  count: number | string;
  label: string;
  color?: string;
}>();

const emit = defineEmits(['cardClick']);

const cardColorMap: Record<string, string> = {
  success: '#A5D6A7',
  info: '#81D4FA',
  warning: '#FFF59D',
  danger: '#EF9A9A',
};

const cardColor = cardColorMap[props.color || 'info'] || '#E0E0E0';
const cardBackground = '#ffffff';
</script>

<style scoped>
.dashboard-card {
  flex: 1 1 200px;
  max-width: 280px;
  margin: 10px;
  padding: 20px;
  border: 2px solid;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  text-align: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;
}
.dashboard-card:hover {
  transform: scale(1.02);
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.12);
}

.card-icon {
  font-size: 30px;
  margin-bottom: 12px;
  color: #333;
}

.card-count {
  font-size: 1.6rem;
  font-weight: bold;
  color: #222;
}

.card-label {
  font-size: 1rem;
  color: #555;
  margin-top: 4px;
}
</style>
