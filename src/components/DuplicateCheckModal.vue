<template>
  <ion-modal
    :is-open="show"
    @didDismiss="handleModalDismissed"
    :handle="true"
    class="duplicate-check-modal"
  >
    <ion-header>
      <ion-toolbar>
        <ion-title>
          <ion-icon :icon="alertCircle" color="warning" class="ion-margin-end"></ion-icon>
          Possible Duplicate Found!
        </ion-title>
      </ion-toolbar>
    </ion-header>

    <ion-content class="ion-padding">
      <ion-text color="medium" v-if="duplicateResult && duplicateResult.bestMatchScore !== undefined">
        <p class="score-text">
          Highest Match Score:
          <span style="background-color: white;">
            <strong :class="getScoreColorClass(duplicateResult.bestMatchScore)">
                {{ duplicateResult.bestMatchScore }}
            </strong>
          </span>

        </p>
      </ion-text>

      <div class="score-legend">
        <h4>Match Score Legend</h4>
        <ul>
          <li>
            <span class="legend-color high-match"></span>
            High Match (Score 80-105): Likely the same person.
          </li>
          <li>
            <span class="legend-color possible-match"></span>
            Possible Match (Score 60-79): Could be the same person; review carefully.
          </li>
          <li>
            <span class="legend-color low-match"></span>
            Low Match (Score < 60): Less likely to be the same person.
          </li>
        </ul>
      </div>
      <ion-grid class="comparison-grid">
        <ion-row>
          <ion-col size-xs="12" size-md="6">
            <ion-card class="comparison-card">
              <ion-card-header>
                <ion-card-title>Your Entered Data</ion-card-title>
              </ion-card-header>
              <ion-card-content>
                <div v-html="displayPatientData(originalPatientInfo)"></div>
              </ion-card-content>
            </ion-card>
          </ion-col>

          <ion-col size-xs="12" size-md="6">
            <ion-card class="comparison-card" v-if="duplicateResult && duplicateResult.bestMatchPatient">
              <ion-card-header class="card-header-with-button">
                <ion-card-title>Best Match Found (ID: {{ duplicateResult.bestMatchPatient.patientID }})</ion-card-title>
                <ion-button
                  @click="openClientProfile(duplicateResult.bestMatchPatient.data)"
                  color="primary"
                  fill="clear"
                  size="small"
                  class="view-profile-btn"
                >
                  <ion-icon :icon="openOutline"></ion-icon>
                </ion-button>
              </ion-card-header>
              <ion-card-content>
                <div v-html="displayPatientData(duplicateResult.bestMatchPatient.data.personInformation)"></div>
              </ion-card-content>
            </ion-card>
            <ion-card class="comparison-card" v-else>
              <ion-card-header>
                <ion-card-title>No Best Match Available</ion-card-title>
              </ion-card-header>
              <ion-card-content>
                <ion-text color="medium">
                  <p>Despite being a possible duplicate, detailed best match information is not available.</p>
                </ion-text>
              </ion-card-content>
            </ion-card>
          </ion-col>
        </ion-row>
      </ion-grid>

      <ion-accordion-group class="ion-margin-top" v-if="otherMatchesCount > 0">
        <ion-accordion value="other-matches">
          <ion-item slot="header">
            <ion-label>
            <span style="font-size: 18px; font-weight: 500;">Other Potential Matches ({{ otherMatchesCount }}) </span>
            <ion-icon :icon="showPotentialMatches ? eyeOff : eye" slot="end" style="vertical-align: middle;"></ion-icon>
            </ion-label>
          </ion-item>
          <div slot="content" class="ion-padding">
            <ion-grid>
              <ion-row>
                <ion-col size-xs="12" size-md="6" v-for="(match, index) in otherPotentialMatches" :key="match.patient.id">
                  <ion-card class="match-card">
                    <ion-card-header class="card-header-with-button">
                      <ion-card-title>Match {{ index + 2 }} (Score: {{ match.score }})</ion-card-title>
                      <ion-button
                        @click="openClientProfile(match.patient.data)"
                        color="primary"
                        fill="clear"
                        size="small"
                        class="view-profile-btn"
                      >
                        <ion-icon :icon="openOutline"></ion-icon>
                      </ion-button>
                    </ion-card-header>
                    <ion-card-content>
                      <div v-html="displayPatientData(match.patient.data.personInformation)"></div>
                    </ion-card-content>
                  </ion-card>
                </ion-col>
              </ion-row>
            </ion-grid>
          </div>
        </ion-accordion>
      </ion-accordion-group>
    </ion-content>

    <ion-footer class="ion-padding ion-text-right">
      <ion-button color="light" fill="solid" @click="handleCancel">
        <ion-icon :icon="closeCircle" slot="start"></ion-icon>
        Cancel Registration
      </ion-button>
      <ion-button color="primary" fill="solid" @click="handleProceed" class="ion-margin-start">
        <ion-icon :icon="checkmarkCircle" slot="start"></ion-icon>
        Proceed Anyway
      </ion-button>
    </ion-footer>
  </ion-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import {
  IonModal,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButton,
  IonIcon,
  IonGrid,
  IonRow,
  IonCol,
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardContent,
  IonText,
  IonAccordionGroup,
  IonAccordion,
  IonItem,
  IonLabel,
  IonList,
  loadingController
} from '@ionic/vue';

import { alertCircle, eye, eyeOff, closeCircle, checkmarkCircle, openOutline } from 'ionicons/icons';
import { useDemographicsStore } from "@/stores/DemographicStore";
import { useAdministerVaccineStore } from "@/apps/Immunization/stores/AdministerVaccinesStore";
import { useRouter } from 'vue-router';

// --- Type Definitions (Ensure these are consistent across your project) ---
interface PatientDemographics {
    given_name: string; middle_name: string; family_name: string; gender: string;
    birthdate: string; birthdate_estimated: boolean; home_region: string;
    home_district: string; home_traditional_authority: string; home_village: string;
    current_region: string; current_district: string; current_traditional_authority: string;
    current_village: string; country: string; landmark: string; cell_phone_number: string;
    occupation: string; marital_status: string; religion: string; education_level: string;
}

interface Patient {
    id: string; patientID: string; message: string; timestamp: string; createdAt: string;
    updatedAt: string; data: any; // Can be more specific if needed
}

interface DuplicateMatchResultResponse {
    isPossibleDuplicate: boolean;
    bestMatchScore: number;
    bestMatchPatient: Patient | null;
    potentialMatches: Array<{ patient: Patient; score: number }>;
}

interface Props {
  show: boolean;
  originalPatientInfo: PatientDemographics;
  duplicateResult: DuplicateMatchResultResponse;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  (e: 'decision', value: 'proceed' | 'cancel'): void;
  (e: 'update:show', value: boolean): void;
  (e: 'openClientProfile', data: any): void;
}>();

const showPotentialMatches = ref(false);

const otherPotentialMatches = computed(() => {
  if (!props.duplicateResult || !props.duplicateResult.potentialMatches) {
    return [];
  }
  const bestMatchId = props.duplicateResult.bestMatchPatient?.id;
  return props.duplicateResult.potentialMatches.filter(
    (match) => match.patient.id !== bestMatchId
  );
});

const otherMatchesCount = computed(() => otherPotentialMatches.value.length);

const handleModalDismissed = async () => {
  emit('decision', 'cancel');
  emit('update:show', false);
  try {
    await loadingController.dismiss();
  } catch (error) {
    // Ignore if no loading controller active
  }
};

const handleCancel = () => {
  emit('decision', 'cancel');
  emit('update:show', false);
};

const handleProceed = () => {
  emit('decision', 'proceed');
  emit('update:show', false);
};
const router = useRouter();

const openClientProfile = async (data: any) => {
    const patientData = useDemographicsStore();
    await patientData.setPatientRecord(data);
    const store = useAdministerVaccineStore();
    store.setVaccineReload(!store.getVaccineReload());
    emit('openClientProfile', data);
    handleCancel();
    router.push("/patientProfile");
};

watch(() => props.show, (newVal) => {
  if (!newVal) {
    showPotentialMatches.value = false;
  }
});

// --- NEW: Match Score Logic and Helper ---
const POSSIBLE_MATCH_THRESHOLD = 60;
const HIGH_MATCH_THRESHOLD = 80;

const getScoreColorClass = (score: number) => {
  if (score >= HIGH_MATCH_THRESHOLD) {
    return 'high-match-score';
  } else if (score >= POSSIBLE_MATCH_THRESHOLD) {
    return 'possible-match-score';
  } else {
    return 'low-match-score';
  }
};
// --- END NEW ---

// --- Function to display patient data in a user-friendly way ---
const displayPatientData = (data: PatientDemographics) => {
  if (!data) return '<p>No data available</p>';

  let html = '';
  let groupIndex = 0; // For alternating background colors

  const fieldGroups = {
    'Names': ['given_name', 'middle_name', 'family_name'],
    'Demographics': ['gender', 'birthdate', 'birthdate_estimated', 'occupation', 'marital_status', 'religion', 'education_level'],
    'Home Address': ['home_region', 'home_district', 'home_traditional_authority', 'home_village'],
    'Current Address': ['current_region', 'current_district', 'current_traditional_authority', 'current_village', 'country', 'landmark'],
    'Contact': ['cell_phone_number']
  };

  const getFieldValue = (key: keyof PatientDemographics, value: any) => {
    if (value === null || value === undefined || value === '') {
        return '<span class="empty-value">N/A</span>';
    }
    if (typeof value === 'boolean') {
        return value ? 'Yes' : 'No';
    }
    return value;
  };

  for (const groupName in fieldGroups) {
    const keys = fieldGroups[groupName as keyof typeof fieldGroups];
    const groupData: { key: string; value: string; originalKey: keyof PatientDemographics }[] = [];

    // Collect all fields for the current group
    for (const key of keys) {
        if (Object.prototype.hasOwnProperty.call(data, key)) {
            const displayKey = key.replace(/_/g, ' ').replace(/\b\w/g, char => char.toUpperCase()); // Convert snake_case to Title Case
            groupData.push({ key: displayKey, value: getFieldValue(key as keyof PatientDemographics, data[key as keyof PatientDemographics]), originalKey: key as keyof PatientDemographics });
        }
    }

    if (groupData.length > 0) {
        html += `<div class="data-group group-${groupIndex % 2}">`; // Alternating classes
        html += `<h4>${groupName}</h4>`;
        html += '<ul>';
        for (const field of groupData) {
            html += `<li><strong>${field.key}:</strong> ${field.value}</li>`;
        }
        html += '</ul>';
        html += '</div>';
        groupIndex++;
    }
  }

  return html;
};

</script>

<style scoped>
/* Scoped styles will apply only to this component */
.duplicate-check-modal {
  /* This is the key for larger width */
  --width: 95%; /* Make modal take 95% of viewport width */
  --max-width: 1400px; /* Limit maximum width */
  --height: 90%; /* Keep current height or adjust as needed */
  --border-radius: 12px;
  /* NEW: Add space at the bottom */
  --min-height: 300px; /* Allow height to adjust based on content */
  --margin-bottom: 200px; /* Space from the bottom */
  --margin-top: 20px; /* Space from the top to keep it centered vertically */
}

ion-toolbar {
  --background: var(--ion-color-primary, #3880ff);
  --color: white;
}

ion-title {
  display: flex;
  align-items: center;
  font-size: 1.2em;
  font-weight: bold;
}

ion-icon.ion-margin-end {
  margin-inline-end: 8px;
}

ion-content {
  --padding-start: 20px;
  --padding-end: 20px;
  --padding-top: 20px;
  --padding-bottom: 20px;
}

.score-text {
  text-align: center;
  font-size: 1.3em;
  margin-bottom: 20px;
  color: #4d4d4d;
}

/* Replace the existing score highlight classes and legend colors in your Vue component with these improved colors */

/* NEW: Improved Score Highlight Classes with vibrant colors */
.high-match-score {
  color: #059669; /* Emerald green - strong, confident */
  font-weight: bold;
}

.possible-match-score {
  color: #d97706; /* Amber orange - attention-grabbing but not alarming */
  font-weight: bold;
}

.low-match-score {
  color: #dc2626; /* Red - clear indication of low match */
  font-weight: bold;
}

/* NEW: Improved Match Score Legend Styling with better colors */
.score-legend {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%); /* Subtle gradient background */
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 18px;
  margin-bottom: 0px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.score-legend h4 {
  margin-top: 0;
  margin-bottom: 12px;
  color: #1e293b; /* Dark slate */
  font-size: 1.1em;
  font-weight: 600;
  border-bottom: 2px solid #cbd5e1;
  padding-bottom: 8px;
}

.score-legend ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.score-legend li {
  display: flex;
  align-items: center;
  font-size: 0.95em;
  font-weight: 500;
  margin-bottom: 0px;
  color: #475569; /* Slate gray */
  padding: 4px 0;
}

.legend-color {
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  margin-right: 12px;
  flex-shrink: 0;
  border: 2px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.legend-color.high-match {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%); /* Emerald gradient */
}

.legend-color.possible-match {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); /* Amber gradient */
}

.legend-color.low-match {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); /* Red gradient */
}

/* Additional improvements for the score display */
.score-text {
  text-align: center;
  font-size: 1.4em;
  margin-bottom: 2px;
  color: #334155; /* Darker slate for better readability */
  font-weight: 600;
}

.score-text span {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  padding: 8px 16px;
  border-radius: 8px;
  border: 2px solid #e2e8f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  display: inline-block;
}

/* Enhanced data group styling for better visual hierarchy */
.data-group.group-0 {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-color: #cbd5e1;
}

.data-group.group-1 {
  background: linear-gradient(135deg, #fefefe 0%, #f8fafc 100%);
  border-color: #d1d5db;
}

.data-group h4 {
  color: #1e293b;
  font-weight: 600;
  border-bottom-color: #94a3b8;
}

.data-group li strong {
  color: #0f172a; /* Very dark slate for labels */
}

/* Improve card titles with better colors */
ion-card-title {
  color: #1e40af !important; /* Blue for better contrast and professionalism */
  font-weight: 600;
}

/* Enhanced button styling */
ion-button[color="primary"] {
  --background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  --background-hover: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  --color: white;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

ion-button[color="light"] {
  --background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  --background-hover: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  --color: #475569;
  border: 2px solid #cbd5e1;
  font-weight: 600;
}

.comparison-grid {
  margin-top: 0px;
}

.comparison-card, .match-card {
  height: 100%;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
  margin-bottom: 16px;
}

.card-header-with-button {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 0;
}

.card-header-with-button ion-card-title {
  flex: 1;
  margin-right: 10px;
}

.view-profile-btn {
  --padding-start: 8px;
  --padding-end: 8px;
  --padding-top: 6px;
  --padding-bottom: 6px;
  min-height: 32px;
  min-width: 40px;
}

.view-profile-btn ion-icon {
  font-size: 18px;
}

ion-card-header {
  padding-bottom: 0;
}

ion-card-title {
  font-size: 1.1em;
  color: var(--ion-color-tertiary, #69bb7b);
}

/* --- NEW: User-friendly data display styling --- */
.comparison-card ion-card-content,
.match-card ion-card-content {
  padding-top: 5px; /* Adjust padding for better look with new content */
}

.data-group {
  margin-bottom: 10px;
  padding: 10px;
  border-radius: 6px;
  border: 1px solid var(--ion-color-step-100, #e6e6e6);
}

.data-group h4 {
  margin-top: 0;
  margin-bottom: 8px;
  color: var(--ion-color-dark-shade, #4d4d4d);
  font-size: 1em;
  border-bottom: 1px dashed var(--ion-color-step-150, #d9d9d9);
  padding-bottom: 5px;
}

.data-group ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.data-group li {
  font-size: 0.9em;
  line-height: 1.4;
  margin-bottom: 4px;
  color: var(--ion-color-medium);
}

.data-group li strong {
  color: var(--ion-color-dark);
}

.empty-value {
    color: var(--ion-color-medium);
    font-style: italic;
}

/* Alternating background colors for data groups */
.data-group.group-0 {
  background-color: var(--ion-color-light, #f8f8f8); /* Light grey */
}
.data-group.group-1 {
  background-color: var(--ion-color-step-50, #f2f2f2); /* Slightly darker grey */
}
/* You can add more .group-X classes if you have more than two alternating groups */
/* --- END NEW --- */


ion-accordion-group {
  margin-top: 20px;
  border: 1px solid var(--ion-color-step-100, #e6e6e6);
  border-radius: 8px;
  overflow: hidden;
}

ion-accordion {
  --ion-item-background: var(--ion-color-light, #f8f8f8);
}

ion-accordion ion-item {
  --padding-start: 16px;
  --inner-padding-end: 16px;
}

ion-accordion ion-label h3 {
  margin: 0;
  font-size: 0.9em;
  font-weight: normal;
}

/* Adjust pre inside accordion for consistency */
ion-accordion ion-label > div { /* Target the div containing rendered data */
  padding: 10px;
  border-radius: 4px;
  margin-top: 10px;
  background-color: var(--ion-color-step-50, #f2f2f2);
}

ion-list {
  background: transparent;
}

ion-item {
  --background: transparent;
  --padding-start: 0;
  --inner-padding-end: 0;
  --inner-border-width: 0;
  --border-width: 0;
  margin-bottom: 10px;
  align-items: flex-start;
}

ion-item:last-child {
  margin-bottom: 0;
}

ion-footer {
  border-top: 1px solid var(--ion-color-step-100, #e6e6e6);
  padding: 15px 20px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

ion-button {
  --padding-start: 20px;
  --padding-end: 20px;
  --padding-top: 12px;
  --padding-bottom: 12px;
  --border-radius: 6px;
  font-weight: bold;
}
</style>