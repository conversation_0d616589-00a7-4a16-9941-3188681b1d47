<template>
    <div style="border: 1px dashed #ccc;">
        <span class="ion-padding">Backdata date: <b>{{ HisDate.toStandardHisDisplayFormat(UserService.getSessionDate()) }}</b></span>
        <ion-item lines="none">
            <ion-button @click="openModal">
                <ion-icon :icon="addOutline"></ion-icon>
                <b>Select provider</b>
            </ion-button>
            <ion-label slot="end">
                <b v-if="!selectedProvider">Provider not selected</b>
                <b v-else>{{ selectedProvider.name }}</b>
            </ion-label>
        </ion-item>
    </div>


    <!-- Inline Modal -->
    <ion-modal class="full-modal" :is-open="isModalOpen" @did-dismiss="closeModal">
        <ion-header>
            <ion-toolbar>
                <ion-title>Select Provider</ion-title>
                <ion-buttons slot="end">
                    <ion-button @click="closeModal">
                        <ion-icon :icon="closeCircleOutline"></ion-icon>
                    </ion-button>
                </ion-buttons>
            </ion-toolbar>
            <ion-toolbar color="light">
                <ion-searchbar v-model="searchFilter" placeholder="Search providers..." :debounce="300"></ion-searchbar>
            </ion-toolbar>
        </ion-header>

        <ion-content class="ion-padding">
            <ion-list>
                <ion-item v-for="provider in displayProviders" :key="provider.id" button
                    @click="selectProvider(provider)"
                    :class="{ 'selected-provider': selectedProvider?.name === provider.name }">
                    <ion-label>
                        <h3>{{ provider.name }}</h3>
                        <p>{{ provider.role }}</p>
                    </ion-label>
                    <ion-icon v-if="selectedProvider?.name === provider.name" :icon="checkmarkOutline" slot="end"
                        color="primary"></ion-icon>
                </ion-item>
            </ion-list>
        </ion-content>
    </ion-modal>
</template>

<script lang="ts" setup>
import { addOutline, closeCircleOutline, checkmarkOutline } from "ionicons/icons";
import {
    IonSearchbar,
    IonButton,
    IonItem,
    IonLabel,
    IonIcon,
    IonModal,
    IonHeader,
    IonToolbar,
    IonTitle,
    IonButtons,
    IonContent,
    IonList
} from '@ionic/vue';
import { ref, computed, onMounted } from "vue";
import { UserService } from "@/services/user_service";
import { toastWarning } from "@/utils/Alerts";
import HisDate from "@/utils/Date";

interface Provider {
    id: number;
    name: string;
    role: string;
}
const searchFilter = ref('');
const selectedProvider = ref<Provider | null>(null);
const providers = ref<Provider[]>([]);
const isModalOpen = ref(false);

const displayProviders = computed(() => {
    if (searchFilter.value.trim() === '') {
        return providers.value;
    }
    // Escape special regex characters and create flexible pattern
    const searchTerm = searchFilter.value.trim().replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    // Create regex pattern that allows for partial matches and word boundaries
    const regexPattern = new RegExp(searchTerm.split('\\s+').join('.*'), 'i');

    return providers.value.filter(provider => {
        const searchableText = `${provider.name} ${provider.role}`.toLowerCase();
        return regexPattern.test(searchableText);
    });
})

const openModal = () => {
    isModalOpen.value = true;
    if (providers.value.length === 0) {
        UserService.getUsers().then((response) => {
            providers.value = response.results.map((user: any) => ({
                id: user.person_id,
                name: `${user?.person?.names?.[0]?.given_name ?? 'Unknown'} ${user?.person?.names?.[0]?.family_name ?? 'Unknown'} (${user.username})`,
                role: user.roles.map((r: any) => r.role).join(', ')
            }));
        }).catch((error) => {
            toastWarning("Failed to fetch providers. Please try again later.");
            console.error("Error fetching providers:", error);
        });
    }
};

const closeModal = () => {
    isModalOpen.value = false;
};

const selectProvider = (provider: Provider) => {
    selectedProvider.value = provider;
    searchFilter.value = ''; // Clear search filter when a provider is selected
    sessionStorage.setItem('selectedBDEProvider', JSON.stringify(provider));
    closeModal();
};

const isProviderSelected = () => selectedProvider.value !== null;

defineExpose({
    selectedProvider,
    isProviderSelected,
    openModal,
    closeModal
})

onMounted(() => {
    sessionStorage.removeItem('selectedBDEProvider');
});
</script>

<style scoped>
.selected-provider {
    --background: rgba(var(--ion-color-primary-rgb), 0.1);
    --border-color: var(--ion-color-primary);
}

.selected-provider ion-label {
    color: var(--ion-color-primary);
}

.full-modal {
    --height: 80vh;
    --min-height: 500px;
    --max-height: 90vh;
    --width: 80%;
    --border-radius: 8px;
}

.full-modal ion-content {
    --padding-top: 0;
    --padding-bottom: 0;
}
</style>