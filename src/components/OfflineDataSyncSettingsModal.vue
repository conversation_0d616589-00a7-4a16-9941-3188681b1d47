<template>
    <ion-header>
        <ion-toolbar>
            <ion-title>
                <div style="display: flex; align-items: center">
                    <ion-icon :icon="iconsContent.wifiOn" class="sub-menu-icon" style="font-size: 1.6rem" />
                    <b style="margin-left: 6px">Data Sync Settings</b>
                </div>
            </ion-title>
            <ion-buttons slot="end">
                <ion-title>
                    <ion-button @click="closeModal()" fill="solid">
                        <span style="font-weight: 400; font-size: 19px">
                            <div style="display: flex; align-items: center">
                                <ion-icon :icon="closeCircleOutline" slot="start" class="sub-menu-icon" />
                                <span style="margin-left: 6px">Close</span>
                            </div>
                        </span>
                    </ion-button>
                </ion-title>
            </ion-buttons>
        </ion-toolbar>
    </ion-header>

    <ion-content class="ion-padding">
        <OfflineDataSyncSettings/>
    </ion-content>

    <ion-footer>
        <ion-toolbar>
            <ion-buttons v-if="false" slot="end">
                <ion-title>
                    <ion-button @click="saveAction" fill="solid">
                        <span style="font-weight: 400; font-size: 20px">
                            <div style="display: flex; align-items: center">
                                <ion-icon :icon="saveOutline" slot="start" class="sub-menu-icon"></ion-icon>
                                <span style="margin-left: 6px">Save</span>
                            </div>
                        </span>
                    </ion-button>
                </ion-title>
            </ion-buttons>
        </ion-toolbar>
    </ion-footer>
</template>

<script lang="ts">
import { defineComponent } from "vue";
export default defineComponent({
    name: "OfflineDataSyncSettingsModal",
});
</script>

<script setup lang="ts">
import { ref, onMounted, watch, computed, inject } from "vue";
import {
    IonButtons,
    IonButton,
    IonContent,
    IonTitle,
    IonToolbar,
    IonHeader,
    IonIcon,
    modalController,
    IonFooter,
    IonList,
    IonItem,
    IonLabel,
    IonInput,
    IonText,
} from "@ionic/vue";
import { saveOutline, closeCircleOutline } from "ionicons/icons";
import { icons } from "@/utils/svg";
import OfflineDataSyncSettings from "@/components/OfflineDataSyncSettings.vue";

const iconsContent = icons;

const emit = defineEmits<{
    (e: "closePopoover", ObjectsArray: any): void;
    (e: "save", ObjectsArray: any): void;
}>();
function closeModal() {
    emit("closePopoover", false);
    modalController.dismiss();
}

const saveAction = () => {

}
</script>

<style scoped>
ion-card {
    margin: 16px 0;
    border-radius: 12px;
}

.sub-menu-icon {
    font-size: 1.2rem;
    font-weight: 600;
}

ion-item {
    --padding-start: 8px;
    margin: 8px 0;
}

.current-ncd-item {
    --background: var(--ion-color-success-tint);
    border-radius: 8px;
    margin: 8px 0;
    border-left: 4px solid var(--ion-color-primary);
}

.current-ncd-item ion-label h2 {
    font-weight: 600;
    color: var(--ion-color-primary);
}

.current-ncd-item ion-label p {
    font-size: 1.1em;
    margin-top: 4px;
    color: var(--ion-color-dark);
}

.custom-input {
    --padding-start: 0;
    font-size: 1.5em;
}

.prefix-container {
    display: flex;
    align-items: center;
    height: 100%;
}

.prefix-part {
    border: none;
    background: #f0f0f0;
    padding: 0 8px;
    border-right: 1px solid #ccc;
    color: var(--ion-color-dark);
    font-weight: 600;
}

.error-message {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 16px;
    margin-top: 8px;
    background-color: rgba(255, 0, 0, 0.1);
    border-radius: 4px;
}

.error-content {
    display: flex;
    align-items: center;
    gap: 8px;
}

.close-button {
    margin-left: auto;
}
</style>