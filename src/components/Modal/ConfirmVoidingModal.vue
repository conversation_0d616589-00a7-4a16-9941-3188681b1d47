<template>
    <!-- Spinner -->
    <div v-if="isLoading" class="spinner-overlay">
        <ion-spinner name="bubbles"></ion-spinner>
        <div class="loading-text">Please wait...</div>
    </div>
    <ion-header style="display: flex; justify-content: space-between">
        <ion-title class="modalTitle">Voiding</ion-title>
        <ion-icon @click="dismiss('dismiss')" style="padding-top: 10px; padding-right: 10px" :icon="iconsContent.cancel"></ion-icon>
    </ion-header>
    <ion-content :fullscreen="true" class="ion-padding" style="--background: #fff">
        <center>
            <h3 class="ion-padding">Do you want to void this encounter?</h3>
        </center>
    </ion-content>

    <ion-footer>
        <ion-row>
            <ion-col>
                <DynamicButton
                    color="danger"
                    @click="dismiss('dismiss')"
                    name="No"
                    fill="solid"
                    style="float: right; margin-right: 2%; width: 100px"
                />
                <DynamicButton @click="dismiss('Yes')" name="Yes" fill="solid" style="float: right; margin-right: 2%; width: 100px" />
            </ion-col>
        </ion-row>
    </ion-footer>
</template>
<script lang="ts">
import { defineComponent } from "vue";
import { IonRow, IonLabel, IonAvatar, IonCol, IonImg, IonList, IonItem, modalController, IonIcon, IonButton } from "@ionic/vue";
import { icons } from "@/utils/svg";
import DynamicButton from "@/components/DynamicButton.vue";

export default defineComponent({
    components: {
        IonLabel,
        IonAvatar,
        IonRow,
        IonImg,
        IonCol,
        IonList,
        IonItem,
        IonIcon,
        IonButton,
        DynamicButton,
    },
    data: () => ({
        selectedResult: {} as any,
        iconsContent: icons,
        isAnyAccordionOpen: true,
        isLoading: false,
    }),
    methods: {
        dismiss(dismiss: any) {
            modalController.dismiss(dismiss);
        },
    },
});
</script>
