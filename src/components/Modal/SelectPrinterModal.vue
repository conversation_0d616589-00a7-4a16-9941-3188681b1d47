<template>
    <ion-header>
        <ion-toolbar>
            <ion-title>Select Printer</ion-title>
            <ion-buttons slot="end">
                <ion-button @click="cancel">
                    <ion-icon :icon="closeCircleOutline"></ion-icon>
                </ion-button>
            </ion-buttons>
        </ion-toolbar>
    </ion-header>

    <ion-content class="ion-padding">
        <ion-row v-if="printers.length === 0" class="centered-content">
            <div class="text-container">
                <div>No printers available.</div>
            </div>
        </ion-row>

        <ion-list v-else>
            <ion-radio-group :compareWith="compareWith" @ionChange="handleChange($event)" :value="selectedOption">
                <ion-item v-for="printer in printers" :key="printer._id">
                    <ion-radio :value="printer">{{ printer.printer_name }}</ion-radio>
                </ion-item>
            </ion-radio-group>
        </ion-list>
    </ion-content>

    <ion-footer>
        <ion-toolbar>
            <ion-row class="ion-justify-content-between ion-align-items-center ion-padding-horizontal">
                <ion-col size="auto">
                    <ion-button @click="cancel" color="light" class="btn-text" style="width: 130px"> Cancel </ion-button>
                </ion-col>
                <ion-col size="auto">
                    <ion-button @click="printData" class="btn-text" style="width: 130px"> Print </ion-button>
                </ion-col>
            </ion-row>
        </ion-toolbar>
    </ion-footer>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import {
    IonCol,
    IonRow,
    IonItem,
    IonList,
    IonRadio,
    IonRadioGroup,
    modalController,
    IonButton,
    IonHeader,
    IonToolbar,
    IonTitle,
    IonButtons,
    IonIcon,
    IonContent,
    IonFooter,
} from "@ionic/vue";
import { toastWarning } from "@/utils/Alerts"; // Assuming this path is correct
import { Service } from "@/services/service"; // Assuming this path is correct
import { closeCircleOutline } from "ionicons/icons";

// Define emits
const emit = defineEmits<{
    "print-selected": [value: any]; // Renamed for clarity
}>();

// Reactive data
const printers = ref<any[]>([]);
const selectedOption = ref<any>(null); // Initialize as null to explicitly indicate no selection

// Methods
const compareWith = (o1: any, o2: any) => {
    // Ensure both objects exist before comparing properties
    return o1 && o2 && o1._id === o2._id;
};

const handleChange = (ev: CustomEvent) => {
    selectedOption.value = ev.detail.value;
};

const cancel = () => {
    modalController.dismiss(null, "cancel"); // Pass null and a role for cancellation
};

const checkIfSelected = (): boolean => {
    if (selectedOption.value) {
        // Directly check if selectedOption has a value
        return true;
    } else {
        toastWarning("Please select a printer to continue.");
        return false;
    }
};

const printData = async () => {
    if (checkIfSelected()) {
        emit("print-selected", selectedOption.value); // Use the new emit name
        await modalController.dismiss(selectedOption.value, "confirm");
    }
};

onMounted(async () => {
    try {
        const res: any = await Service.getJson("/printer_configurations", { location_id: Service.getUserLocationId() });
        printers.value = res;
    } catch (error) {
        console.error("Error fetching printer configurations:", error);
        toastWarning("Failed to load printer configurations. Please try again.");
    }
});
</script>

<style scoped>
ion-content {
    --padding-start: 16px;
    --padding-end: 16px;
    --padding-top: 16px;
    --padding-bottom: 16px;
}

.centered-content {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%; /* Occupy full height of the content area */
    text-align: center;
}

.text-container {
    padding: 20px;
    border-radius: 8px;
    background-color: var(--ion-color-light-shade, #f4f5f8);
    color: var(--ion-color-dark, #000);
}

.btn-text {
    font-weight: 500;
}

ion-footer {
    padding: 10px 0;
}

ion-row.ion-justify-content-between.ion-align-items-center.ion-padding-horizontal {
    width: 100%;
    margin: 0;
}
</style>
