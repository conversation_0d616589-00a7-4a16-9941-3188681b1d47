<template>
    <div class="">
        <div>
            <div class="table-header" style="display: flex; justify-content: space-between; align-items: center">
                <h3 class="encounter-title">Manage web printers</h3>
                <div style="min-width: 400px; display: flex; align-items: center">
                    <span style="font-weight: 700">Default Printer</span><StandardForm :formData="defaultForm" ref="formRef" />
                </div>
                <DynamicButton name="Add printer" fill="solid" @click="addPrinter()" />
            </div>
            <div class="table-responsive">
                <div v-if="isLoading" class="loading-state">
                    <p>Loading printers...</p>
                </div>
                <div v-else-if="tableData.length === 0" class="empty-state">
                    <p>No printers found</p>
                </div>
                <DataTable
                    v-else
                    ref="dataTableRef"
                    class="display nowrap modern-table"
                    width="100%"
                    :data="processedTableData"
                    :columns="tableColumns"
                    :options="dataTableOptions"
                >
                    <thead>
                        <tr>
                            <th>Printer name</th>
                            <th>Ip address</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                </DataTable>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from "vue";
import { useRoute } from "vue-router";
import DataTable from "datatables.net-vue3";
import "datatables.net-buttons";
import "datatables.net-buttons/js/buttons.html5";
import "datatables.net-buttons-dt";
import "datatables.net-responsive";
import DynamicButton from "@/components/DynamicButton.vue";
import { createModal, toastDanger, toastSuccess } from "@/utils/Alerts";
import AddWebPrinter from "@/components/AddWebPrinter.vue";
import "datatables.net-select";
import { Service } from "@/services/service";
import StandardForm from "./Forms/StandardForm.vue";
import { de } from "date-fns/locale";
import component from "@/apps/ANC/components/Dashboard/Dashboard.vue";
import { options } from "preact";
import { FormElement } from "./Forms/interfaces/FormElement";

const route = useRoute();

// Reactive data
const tableData = ref<any[]>([]);
const isLoading = ref(false);
const dataTableRef = ref(null); // Ref for DataTable instance

const dataTableOptions = computed(() => ({
    paging: true,
    lengthChange: false,
    searching: false,
    ordering: true,
    info: true,
    autoWidth: true,
    responsive: true,
    pageLength: 8,
}));

const tableColumns = computed(() => [
    { data: 0, title: "Printer name" },
    { data: 1, title: "Ip address" },
    { data: 2, title: "Date" },
    {
        data: 3,
        title: "Actions",
        orderable: false,
        className: "text-left",
    },
]);
const defaultForm = computed(
    () =>
        [
            {
                componentType: "multiSelectInputField",
                value: Service.getDefaultPrinter() || "",
                trackBy: "id",
                onChange: (value: any) => {
                    localStorage.setItem("defaultPrinter", JSON.stringify(value));
                },
                options: tableData.value.map((item: any) => ({
                    name: item.printer_name,
                    id: item._id,
                    ip_address: item.ip_address,
                })),
            },
        ] satisfies FormElement[]
);
// Generate processed table data with action buttons
const processedTableData = computed(() => {
    return generateTableItems(tableData.value);
});

const generateTableItems = (data: any[]) => {
    if (data.length > 0) {
        return data.map((item: any) => {
            return [
                item.printer_name,
                item.ip_address,
                item.created_at,
                `<div class="action-buttons">
                    <button class="test-print-btn action-button" data-id='${JSON.stringify({
                        id: item._id,
                        printer_name: item.printer_name,
                        ip_address: item.ip_address,
                        created_at: item.created_at,
                        location_id: item.location_id,
                    })}'>Test Print</button>
                    <button class="edit-btn action-button" data-id='${JSON.stringify({
                        id: item._id,
                        printer_name: item.printer_name,
                        ip_address: item.ip_address,
                        created_at: item.created_at,
                        location_id: item.location_id,
                    })}'>Edit</button>
                    <button class="delete-btn action-button delete-button" data-id='${JSON.stringify({
                        id: item._id,
                        printer_name: item.printer_name,
                        ip_address: item.ip_address,
                        created_at: item.created_at,
                        location_id: item.location_id,
                    })}'>Delete</button>
                </div>`,
            ];
        });
    }
    return [];
};

// Setup event listeners for action buttons
const setupTableEventListeners = () => {
    nextTick(() => {
        const table = (dataTableRef.value as any).dt;
        table.columns.adjust().draw();

        // Remove existing event listeners to prevent duplicates
        table.off("click", ".test-print-btn");
        table.off("click", ".edit-btn");
        table.off("click", ".delete-btn");

        // Test Print button event listener
        table.on("click", ".test-print-btn", (e: Event) => {
            const data: any = (e.target as HTMLElement).getAttribute("data-id");
            testPrinting(JSON.parse(data));
        });

        // Edit button event listener
        table.on("click", ".edit-btn", (e: Event) => {
            const data: any = (e.target as HTMLElement).getAttribute("data-id");
            editPrinter(JSON.parse(data));
        });

        // Delete button event listener
        table.on("click", ".delete-btn", (e: Event) => {
            const data: any = (e.target as HTMLElement).getAttribute("data-id");
            deletePrinter(JSON.parse(data));
        });
    });
};

// Methods
const addPrinter = async (data = null) => {
    await createModal(AddWebPrinter, { class: "large-modal" }, true);
    await loadTableData();
};

const editPrinter = async (printerData: any) => {
    console.log("Editing printer:", printerData);
    // Implement edit functionality here
    // await createModal(EditWebPrinter, { class: "large-modal", printer: printerData }, true);
    toastSuccess(`Edit functionality for ${printerData.printer_name} is under development.`);
};

const deletePrinter = async (printerData: any) => {
    if (confirm(`Are you sure you want to delete printer: ${printerData.printer_name}?`)) {
        try {
            await Service.delete(`/printer_configurations/${printerData.id}`, { id: printerData.id });
            toastSuccess(`Printer ${printerData.printer_name} deleted successfully!`);
            await loadTableData();
        } catch (error) {
            console.error("Error deleting printer:", error);
            toastDanger(`Failed to delete printer ${printerData.printer_name}.`);
        }
    }
};

const testPrinting = async (printerData: any) => {
    const zplSampleData = `^XA
        ^FO50,50^A0N,60,60^FDTest Print from Vue App!^FS
        ^FO50,150^A0N,30,30^FDPrinter: ${printerData.printer_name}^FS
        ^FO50,200^A0N,30,30^FDIP Address: ${printerData.ip_address}^FS
        ^XZ`;

    try {
        const response = await fetch(`https://${printerData.ip_address}/print`, {
            method: "POST",
            mode: "cors",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                zpl: zplSampleData,
            }),
        });

        if (response.ok) {
            toastSuccess(`Test print sent successfully to ${printerData.printer_name}`);
        } else {
            const errorData = await response.json();
            toastDanger(`Failed to send test print to ${printerData.printer_name}. Error: ${errorData.message || response.statusText}`);
        }
    } catch (error) {
        console.error("Error sending test print:", error);
        toastDanger(`Error sending test print to ${printerData.printer_name}. Please check your network or server connection.`);
    }
};

// Load table data
const loadTableData = async () => {
    isLoading.value = true;
    try {
        tableData.value = await Service.getJson("/printer_configurations", { location_id: Service.getUserLocationId() });
        // Setup event listeners after data is loaded
        setupTableEventListeners();
    } catch (error) {
        console.error("Error loading printer configurations:", error);
        toastDanger("Failed to load printer configurations.");
    } finally {
        isLoading.value = false;
    }
};

watch(
    route,
    async () => {
        await loadTableData();
    },
    { deep: true }
);

// Watch for changes in processedTableData to setup event listeners
watch(processedTableData, () => {
    setupTableEventListeners();
});

// Lifecycle
onMounted(async () => {
    Service.getDefaultPrinter();
    await loadTableData();
});
</script>

<style>
@import "datatables.net-dt";
@import "datatables.net-buttons-dt";
@import "datatables.net-responsive-dt";
@import "datatables.net-select-dt";

.action-buttons {
    display: flex;
    gap: 8px;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
}

.action-button {
    padding: 6px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f8f9fa;
    color: #333;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s ease;
    min-width: 70px;
    text-align: center;
}

.action-button:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
}

.test-print-btn:hover {
    background-color: #0056b3;
    color: white;
    border-color: #0056b3;
}

.edit-btn:hover {
    background-color: #5a6268;
    color: white;
    border-color: #5a6268;
}

.delete-button {
    border-color: #dc3545;
    color: #dc3545;
}

.delete-button:hover {
    background-color: #dc3545;
    color: white;
    border-color: #dc3545;
}
table.dataTable > tbody > tr.child ul.dtr-details > li:last-child {
    display: flex;
    align-items: center;
}
</style>
