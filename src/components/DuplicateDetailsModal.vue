<template>
  <ion-content>
    <ion-toolbar>
      <ion-title>Duplicate Details for {{ identifierLabel }}</ion-title>
      <ion-buttons slot="end">
        <ion-button @click="dismiss">Close</ion-button>
      </ion-buttons>
    </ion-toolbar>

    <ion-grid>
      <ion-row>
        <ion-col size="12" size-md="10" offset-md="1">
          <ion-card>
            <ion-card-content class="table-responsive">
              <table class="appointments-table">
                <thead>
                  <tr>
                    <th>Given Name</th>
                    <th>Family Name</th>
                    <th>Gender</th>
                    <th>Birth Date</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="patient in patients" :key="patient.person_id">
                    <td>{{ patient.given_name }}</td>
                    <td>{{ patient.family_name }}</td>
                    <td>{{ patient.gender }}</td>
                    <td>{{ patient.birthdate }}</td>
                    <td>
                      <ion-button size="small" fill="clear" @click="viewProfile(patient.patient)">
                        View Details
                      </ion-button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </ion-card-content>
          </ion-card>
        </ion-col>
      </ion-row>
    </ion-grid>
  </ion-content>
</template>

<script setup lang="ts">
import {
  IonContent, IonToolbar, IonTitle, IonButtons, IonButton,
  IonCard, IonCardContent, IonGrid, IonRow, IonCol
} from "@ionic/vue";
import { modalController } from "@ionic/vue";
import { useRouter } from "vue-router";
import { useDemographicsStore } from "@/stores/DemographicStore";
import { resetPatientData } from "@/services/reset_data";

interface Patient {
  person_id: string | number;
  given_name: string;
  family_name: string;
  gender: string;
  birthdate: string;
  patient: any;
}

const props = defineProps<{
  patients: Patient[];
  identifierLabel: string;
}>();

const router = useRouter();

function dismiss() {
  modalController.dismiss();
}

async function viewProfile(patient: any) {
  await resetPatientData();
  await useDemographicsStore().setPatientRecord(patient);
  router.replace({ path: "/patientProfile" });
  dismiss();
}
</script>

<style scoped>
.table-responsive {
  overflow-x: auto;
}

.appointments-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.appointments-table th,
.appointments-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #ccc;
  white-space: nowrap;
}

.appointments-table th {
  background-color: #f1f1f1;
  font-weight: 600;
}

ion-toolbar {
  --padding-start: 16px;
  --padding-end: 16px;
}

ion-card {
  margin: 16px 0;
}
</style>
