<template>
    <ion-page>
        <ion-header>
            <ion-toolbar>
                <div style="display: flex; justify-content: space-between">
                    <ion-title>Add Web Printer</ion-title>
                    <DynamicButton name="X" style="margin-right: 10px" @click="dismiss()" />
                </div>
            </ion-toolbar>
        </ion-header>
        <ion-content class="ion-padding">
            <div style="max-width: 500px; margin: auto">
                <ion-row>
                    <ion-col size="12">
                        <StandardForm :formData="webPrinter" ref="formRef" />
                    </ion-col>
                </ion-row>

                <ion-row class="ion-justify-content-end ion-margin-top">
                    <ion-col size="auto">
                        <DynamicButton name="Add printer" @click="addPrinter()" />
                    </ion-col>
                </ion-row>
            </div>
        </ion-content>
    </ion-page>
</template>

<script setup lang="ts">
import { IonPage, IonHeader, IonToolbar, IonTitle, IonContent, IonRow, IonCol, modalController } from "@ionic/vue";
import { computed, ref } from "vue";
import StandardForm from "./Forms/StandardForm.vue";
import { icons } from "@/utils/svg";
import StandardValidations from "@/validations/StandardValidations";
import DynamicButton from "@/components/DynamicButton.vue";
import { useExposeFromStandardForm } from "@/composables/useExposeFromStandardForm";
import { useLocation } from "@/composables/useLocation";
import { Service } from "@/services/service";

const { formRef, currentFormValues } = useExposeFromStandardForm();
const { facilityList, getFacilities } = useLocation();
getFacilities();
const webPrinter = computed(() => {
    return [
        {
            componentType: "inputField",
            header: "Print name",
            name: "printer_name",
            icon: icons.print,
            validation: (value: string) => {
                return StandardValidations.required(value);
            },
        },
        {
            componentType: "inputField",
            header: "Printer IP address",
            name: "ip_address",
            icon: icons.networkBarDark,
            validation: (value: any) => {
                return StandardValidations.required(value);
            },
        },
        {
            componentType: "multiSelectInputField",
            header: "Facility",
            name: "location_id",
            trackBy: "facility_id",
            openDirection: "auto",
            icon: icons.search,
            validation: (value: string) => {
                return StandardValidations.required(value);
            },
            options: facilityList.value.facilities,
        },
    ] as any; // Consider refining this type if possible
});
const dismiss = () => {
    modalController.dismiss();
};

const addPrinter = () => {
    const data: any = formRef.value?.getFormValues();
    data.location_id = data.location_id.code;
    Service.postJson("/printer_configurations", data);
    modalController.dismiss();
};
</script>

<style scoped>
/* Any custom styles for this page, if not handled by Ionic or StandardForm */
/* Example: */
/* .ion-padding {
    padding: 16px;
} */
</style>
