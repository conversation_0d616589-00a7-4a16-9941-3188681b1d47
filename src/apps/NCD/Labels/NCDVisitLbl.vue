<template>
    <div ref="lblComponent" id="container" :style="'padding: 15px; width:' + stickerWidth">
        <!-- Show patient details only on first page -->
        <div
            style="display: flex; justify-content: space-between; width: 100%"
            v-if="currentPage === 0 && (patient?.latestVisitDate || patient?.patientID)"
        >
            <span v-if="patient?.latestVisitDate"><b>Visit:</b> {{ patient.latestVisitDate }}</span>
            <span v-if="patient?.patientID" style="font-weight: bold; padding: 1.7px">{{ patient.patientID }}</span>
        </div>

        <div
            style="display: flex; flex-wrap: wrap; gap: 20px; width: 100%"
            v-if="currentPage === 0 && (patient?.patientName || patient?.gender || (patient?.diagnoses && patient.diagnoses.length > 0))"
        >
            <span v-if="patient?.patientName || patient?.gender">
                <b>{{ patient?.patientName }}{{ patient?.gender ? `(${patient.gender})` : "" }}</b>
            </span>
            <span v-if="patient?.prescriber"><b>Seen by:</b>{{ patient.prescriber }}</span>
            <span v-if="patient?.nextAppointment"><b>Nxt App:</b> {{ patient.nextAppointment }}</span>
        </div>
        <div v-if="currentPage === 0 && patient?.diagnoses && patient.diagnoses.length > 0">
            <span style="display: flex; flex-wrap: wrap; gap: 8px; width: 100%">
                <b>Diagnoses:</b> {{ patient.diagnoses.filter((d: any) => d).join(", ") }}
            </span>
        </div>

        <div style="display: flex; flex-wrap: wrap; gap: 20px; width: 100%" v-if="currentPage === 0 && hasVitals">
            <span v-if="patient?.vitals?.height">{{ patient.vitals.height || "" }}</span>
            <span v-if="patient?.vitals?.weight">{{ patient.vitals.weight || "" }}</span>
            <span v-if="patient?.vitals?.bloodPressure"> {{ patient.vitals.bloodPressure || "" }} </span>
            <span v-if="patient?.bloodGlucose">{{ patient.bloodGlucose || "" }}</span>
        </div>

        <table style="width: 100%; text-align: center; border-collapse: collapse; border: 1px solid black" v-if="currentMedications.length > 0">
            <thead v-if="currentPage === 0">
                <tr>
                    <th style="border: 1px solid black">Drug</th>
                    <th style="border: 1px solid black">Dose</th>
                    <th style="border: 1px solid black">Freq</th>
                    <th style="border: 1px solid black">Qty</th>
                </tr>
            </thead>
            <tbody>
                <tr v-for="(drug, index) in currentMedications" :key="index">
                    <td style="border: 1px solid black">{{ drug?.name || "" }}</td>
                    <td style="border: 1px solid black">{{ drug?.dose || "" }}</td>
                    <td style="border: 1px solid black">{{ drug?.frequency || "" }}</td>
                    <td style="border: 1px solid black">{{ drug?.quantity || "" }}</td>
                </tr>
            </tbody>
        </table>
    </div>
</template>

<script lang="ts" setup>
import { onMounted, PropType, ref, computed } from "vue";
import domtoimage from "dom-to-image-more";
import { Service } from "@/services/service";

const emit = defineEmits(["label-out"]);
const lblComponent = ref(null);
const currentPage = ref(0);
const stickerWidth = ref("");
const medicationNumber = ref(4);

const props = defineProps({
    imageOut: {
        type: Object as PropType<(img: string[]) => void>,
    },
    patient: {
        type: Object,
    },
});

// Filter out medications with no values
const validMedications = computed(() => {
    if (!props.patient?.medications) return [];
    return props.patient.medications.filter((med: any) => med && (med.name || med.dose || med.frequency || med.quantity));
});

// Check if patient has any vitals data
const hasVitals = computed(() => {
    const vitals = props.patient?.vitals;
    return vitals && (vitals.height || vitals.weight || vitals.bloodPressure || props.patient?.bloodGlucose);
});

// Get medications for current page
const currentMedications = computed(() => {
    const meds = validMedications.value;
    if (meds.length === 0) return [];

    if (currentPage.value === 0) {
        // First page: show up to 2 medications
        return meds.slice(0, 2);
    } else {
        // Subsequent pages: show up to 4 medications
        Service.getIsIpPrintersStatus() ? (medicationNumber.value = 7) : (medicationNumber.value = 4);
        const startIndex = 2 + (currentPage.value - 1) * medicationNumber.value;
        return meds.slice(startIndex, startIndex + medicationNumber.value);
    }
});

// Calculate total number of pages needed
const totalPages = computed(() => {
    const totalMeds = validMedications.value.length;
    if (totalMeds <= 2) return 1;

    const remainingAfterFirst = totalMeds - 2;
    Service.getIsIpPrintersStatus() ? (medicationNumber.value = 7) : (medicationNumber.value = 4);
    return 1 + Math.ceil(remainingAfterFirst / medicationNumber.value);
});

const generateImages = async () => {
    const images: string[] = [];

    for (let page = 0; page < totalPages.value; page++) {
        currentPage.value = page;

        // Wait for DOM to update
        await new Promise((resolve) => setTimeout(resolve, 100));

        try {
            let dataUrl;
            if (Service.getIsIpPrintersStatus()) {
                stickerWidth.value = "800px";
                dataUrl = await domtoimage.toCanvas(lblComponent.value, {
                    height: 300,
                    width: 800,
                });
            } else {
                dataUrl = await domtoimage.toPng(lblComponent.value, {
                    height: 300,
                    width: 594,
                });
            }

            images.push(dataUrl);
        } catch (error) {
            console.error("Error generating image for page", page, error);
        }
    }

    return images;
};

onMounted(async () => {
    try {
        const images = await generateImages();
        emit("label-out", images);
        if (typeof props.imageOut === "function") {
            props.imageOut(images);
        }
    } catch (error) {
        console.error("Error generating patient labels:", error);
    }
});
</script>

<style scoped>
#container {
    font-family: monospace;
    font-size: 24px;
    overflow: hidden;
    padding: 15px;
    width: 594px;
}

ul,
ol {
    list-style-type: none;
    padding-left: 0;
    margin: 0;
}
</style>
