<template>
    <StandardForm :formData="vitalsForm" ref="formRef" />
    <ion-row>
        <ion-accordion-group ref="accordionGroup" class="previousView">
            <ion-accordion value="first" toggle-icon-slot="start" style="border-radius: 10px; background-color: #fff">
                <ion-item slot="header" color="light">
                    <ion-label class="previousLabel">Previous measurements</ion-label>
                </ion-item>
                <div class="ion-padding" slot="content">
                    <PreviousVitals />
                </div>
            </ion-accordion>
        </ion-accordion-group>
    </ion-row>
</template>

<script setup lang="ts">
import StandardForm from "@/components/Forms/StandardForm.vue";
import { onMounted, computed, ref } from "vue";
import { FormElement } from "@/components/Forms/interfaces/FormElement";
import { useHeightWeightForm } from "@/apps/NCD/composables/forms/vitals/useHeightWeightForm";
import { useBloodPressureForm } from "@/apps/NCD/composables/forms/vitals/useBloodPressureForm";
import { useTemperaturePulseRateForm } from "@/apps/NCD/composables/forms/vitals/useTemperaturePulseRateForm";
import { useRespiratoryRateOxygenForm } from "@/apps/NCD/composables/forms/vitals/useRespiratoryRateOxygenForm";
import { toastSuccess, toastWarning } from "@/utils/Alerts";
import { PatientService } from "@/services/patient_service";
import { savePatientRecord } from "@/services/offline_service";
import { ObservationService } from "@/services/observation_service";
import PreviousVitals from "@/components/Graphs/previousVitals.vue";

// Import the separate form modules
const heightWeightForm = useHeightWeightForm();
const bloodPressureForm = useBloodPressureForm();
const temperaturePulseRateForm = useTemperaturePulseRateForm();
const respiratoryRateOxygenForm = useRespiratoryRateOxygenForm();

// Create a ref for the StandardForm component
const formRef = ref<InstanceType<typeof StandardForm> | null>(null);

const onSubmit = async () => {
    if (!formRef.value) {
        console.error("Form reference is not available");
        return formRef;
    }

    const formData = formRef.value.getFormValues();
    const { newVitals, vitalsReasons } = await processVitals(formData);

    if ((!newVitals.length && !vitalsReasons.length) || formRef.value.validateForm()) {
        toastWarning("Vitals not saved");
        return false;
    }

    const patient = new PatientService().getObj();
    (patient.vitals ??= {}).unsaved ??= [];
    patient.vitals.unsaved.push(...newVitals, ...vitalsReasons);

    await savePatientRecord(patient);
    formRef.value.resetForm();
    toastSuccess("Vitals saved successful");
    return true;
};

const processVitals = async (data: object) => {
    const newVitals = [];
    const vitalsReasons = [];

    for (const [key, value] of Object.entries(data)) {
        if ((typeof value === "string" && value) || typeof value === "number") {
            newVitals.push(await ObservationService.buildValueNumber(key, parseInt(String(value))));
        } else if (value?.name) {
            vitalsReasons.push(await ObservationService.buildValueText(key, value.name));
        }
    }

    return { newVitals, vitalsReasons };
};

// Load height data after component mounts
onMounted(async () => {
    await heightWeightForm.loadHeight();
});

// Merge the forms into one complete form
const vitalsForm = computed(() => {
    const mergedForm: FormElement[] = [
        // Height and Weight sections
        ...heightWeightForm.heightWeightFormSection.value,

        // Add separator between sections
        { grid: { s: "3" } },
        { grid: { s: "9" }, componentType: "Dashes" },

        // Blood Pressure sections
        ...bloodPressureForm.bloodPressureFormSection.value,

        // Add separator between sections
        { grid: { s: "3" } },
        { grid: { s: "9" }, componentType: "Dashes" },

        // Temperature and Pulse Rate sections
        ...temperaturePulseRateForm.temperaturePulseRateForm.value,

        // Respiratory Rate and Oxygen sections
        ...respiratoryRateOxygenForm.respiratoryRateOxygenForm.value,
    ] as any;

    return mergedForm;
});

// Expose methods for parent component if needed
defineExpose({
    validateForm: () => formRef.value?.validateForm(),
    onSubmit,
});
</script>

<style></style>
