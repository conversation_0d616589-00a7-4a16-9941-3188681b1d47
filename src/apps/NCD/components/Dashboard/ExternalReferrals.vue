<template>
    <div class="fhir-viewer">
        <div class="container">
            <header class="main-header" v-if="fhirPatientData?.length">
                <div class="header-line">
                    <h5 class="header-title">Referred Patient Overview</h5>
                    <div>
                        <span
                            style="margin-right: 5px"
                            :class="['last-updated-display', 'status-' + getSyncStatus(fhirPatientData[0].entry[0].resource).toLowerCase()]"
                        >
                            Sync Status: {{ getSyncStatus(fhirPatientData[0].entry[0].resource) }}
                        </span>
                        <span v-if="lastUpdatedDate" class="last-updated-display">Last Updated: {{ lastUpdatedDate }}</span>
                    </div>
                </div>
            </header>

            <section v-if="!fhirPatientData?.length" class="card initial-input-card">
                <ion-input
                    v-model="identifierID"
                    placeholder="Enter Patient Identifier (e.g., patient-id-123)"
                    fill="outline"
                    label="Patient ID"
                    label-placement="floating"
                ></ion-input>
                <div class="action-area-initial">
                    <button @click="loadSampleData" class="load-data-btn">
                        <ion-icon name="search-outline"></ion-icon>
                        Find Patient
                    </button>
                </div>
            </section>

            <div v-for="bundle in fhirPatientData" :key="bundle.id">
                <section
                    v-if="bundle.resourceType === 'Bundle' && bundle.entry && bundle.entry[0] && bundle.entry[0].resource.resourceType === 'Patient'"
                    class="card patient-card"
                >
                    <h2 class="card-title"><ion-icon name="person-circle-outline"></ion-icon> Patient Demographics</h2>
                    <div class="patient-info-grid">
                        <div class="info-item">
                            <span class="info-label">Full Name</span>
                            <span class="info-value">{{ getPatientName(bundle.entry[0].resource) }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Gender</span>
                            <span class="info-value">{{ bundle.entry[0].resource.gender || "Not specified" }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Date of Birth</span>
                            <span class="info-value">{{ formatDate(bundle.entry[0].resource.birthDate) }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Age</span>
                            <span class="info-value">{{ calculateAge(bundle.entry[0].resource.birthDate) }} years</span>
                        </div>

                        <!-- Patient Phone Numbers -->
                        <template v-if="getPatientPhoneNumbers(bundle.entry[0].resource).length">
                            <div
                                v-for="(phone, index) in getPatientPhoneNumbers(bundle.entry[0].resource)"
                                :key="`patient-phone-${index}`"
                                class="info-item"
                            >
                                <span class="info-label">Patient Phone </span>
                                <span class="info-value">{{ phone.value }}</span>
                            </div>
                        </template>

                        <!-- Guardian Phone Numbers -->
                        <template v-if="getGuardianPhoneNumbers(bundle.entry[0].resource).length">
                            <div
                                v-for="(phone, index) in getGuardianPhoneNumbers(bundle.entry[0].resource)"
                                :key="`guardian-phone-${index}`"
                                class="info-item"
                            >
                                <span class="info-label">Guardian Phone </span>
                                <span class="info-value">{{ phone.value }}</span>
                            </div>
                        </template>

                        <div v-for="(id, index) in getPatientIdentifiers(bundle.entry[0].resource)" :key="id.value || index" class="info-item">
                            <span class="info-label">{{ id.systemDisplay }}</span>
                            <span class="info-value">{{ id.value }}</span>
                        </div>
                    </div>
                </section>
            </div>

            <section v-if="extractedObservations?.length > 0" class="card observations-card">
                <h2 class="card-title"><ion-icon name="clipboard-outline"></ion-icon> Clinical Observations</h2>
                <div class="observations-grid">
                    <div v-for="obs in extractedObservations" :key="obs.id" class="observation-item">
                        <div class="obs-header">
                            <h3 class="obs-title">{{ obs.display }}</h3>
                        </div>
                        <div class="obs-details">
                            <div class="obs-detail obs-value-highlight">
                                <span class="obs-label">Value:</span>
                                <span class="obs-value">{{ obs.value }}</span>
                            </div>
                            <div class="obs-detail">
                                <span class="obs-label">Date:</span>
                                <span class="obs-value">{{ formatDateTime(obs.date) }}</span>
                            </div>
                            <div v-if="obs.category && obs.category !== 'N/A'" class="obs-detail">
                                <span class="obs-label">Category:</span>
                                <span class="obs-value">{{ obs.category }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <div v-if="fhirPatientData?.length && !hasPatientData" class="no-data-found">
                <p><ion-icon name="alert-circle-outline"></ion-icon> No patient data found for the provided identifier. Please try again.</p>
            </div>
            <div v-else-if="fhirPatientData?.length && hasPatientData && !hasObservationsDataComputed" class="no-data-found">
                <p><ion-icon name="information-circle-outline"></ion-icon> No clinical observations found for this patient.</p>
            </div>

            <footer class="main-footer" v-if="fhirPatientData?.length">
                <button @click="backToSearch" class="back-to-search-btn small-btn">
                    <ion-icon name="arrow-back-outline"></ion-icon>
                    Back to Search
                </button>
                <button @click="registerNewPatient" class="register-btn small-btn">
                    <ion-icon name="person-add-outline"></ion-icon>
                    Register New Patient
                </button>
            </footer>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from "vue";
import { FhirService } from "@/services/fhir_service"; // Assuming this path is correct in your project
import { IonInput, IonIcon } from "@ionic/vue";
import { addIcons } from "ionicons";
import {
    personCircleOutline,
    clipboardOutline,
    searchOutline,
    informationCircleOutline,
    alertCircleOutline,
    personAddOutline,
    arrowBackOutline,
} from "ionicons/icons";
import { useRouter } from "vue-router";
import { useDemographicsStore } from "@/stores/DemographicStore"; // Assuming this path is correct in your project

// Initialize Ionicons for use in the template
addIcons({
    "person-circle-outline": personCircleOutline,
    "clipboard-outline": clipboardOutline,
    "search-outline": searchOutline,
    "information-circle-outline": informationCircleOutline,
    "alert-circle-outline": alertCircleOutline,
    "person-add-outline": personAddOutline,
    "arrow-back-outline": arrowBackOutline,
});

// Props definition for component reusability
const props = defineProps({
    initialData: {
        type: Array,
        default: () => [], // Default to an empty array
    },
    apiEndpoint: {
        type: String,
        default: "/api/fhir/patient-data", // Default API endpoint
    },
});

// Vue Router instance for navigation
const route = useRouter();

// Reactive state variables
const isLoading = ref(false); // Not currently used, but good for future loading indicators
const fhirPatientData = ref([]) as any; // Raw FHIR bundle data
const identifierID = ref() as any; // Input field for patient identifier
const lastUpdatedDate = ref() as any; // Displays the last updated timestamp of fetched data

const extractedPatientData = ref(null) as any;
const extractedObservations = ref([]) as any;

const hasPatientData = computed(() => {
    return fhirPatientData.value.some(
        (bundle: any) =>
            bundle.resourceType === "Bundle" &&
            bundle.entry &&
            bundle.entry.some((entry: any) => entry.resource && entry.resource.resourceType === "Patient")
    );
});

const hasObservationsDataComputed = computed(() => {
    return extractedObservations.value.length > 0;
});

const loadSampleData = async () => {
    fhirPatientData.value = [];
    lastUpdatedDate.value = null;
    extractedPatientData.value = null;
    extractedObservations.value = [];

    if (!identifierID.value) {
        console.error("Please enter a Patient Identifier.");
        return;
    }

    isLoading.value = true; // Set loading state
    try {
        // Fetch patient bundle
        const patientBundle = await FhirService.getFhirPatient(identifierID.value);

        if (patientBundle && patientBundle.entry && patientBundle.entry.length > 0) {
            const patientResource = patientBundle.entry[0].resource;
            const patientId = patientResource.id;

            // Fetch observation bundle for the patient
            const observationBundle = await FhirService.getFhirObservations(patientId);

            // Populate raw FHIR data
            fhirPatientData.value = [patientBundle];
            if (observationBundle) {
                fhirPatientData.value.push(observationBundle);

                // Extract and structure observation data
                extractedObservations.value = observationBundle.entry
                    .filter((entry: any) => entry.resource && entry.resource.resourceType === "Observation")
                    .map((entry: any) => {
                        const obs = entry.resource;
                        return {
                            id: obs.id,
                            display: getObservationDisplay(obs),
                            value: getObservationValue(obs),
                            date: obs.effectiveDateTime,
                            category:
                                obs.category && obs.category[0] && obs.category[0].coding && obs.category[0].coding[0]
                                    ? obs.category[0].coding[0].display || obs.category[0].coding[0].code
                                    : "N/A",
                            // Add other relevant observation fields here as needed
                        };
                    });
            }

            // Extract and structure patient demographic data
            const name = patientResource.name && patientResource.name[0];
            extractedPatientData.value = {
                personInformation: {
                    given_name: name && name.given ? name.given.join(" ") : "",
                    middle_name: name && name.middle ? name.middle.join(" ") : "",
                    family_name: name && name.family ? name.family : "",
                    gender: patientResource.gender == "male" ? "M" : patientResource?.gender == "female" ? "F" : "",
                    birthdate: patientResource.birthDate || "",
                    cell_phone_number: patientResource.telecom && patientResource.telecom[0] ? patientResource.telecom[0].value : "",
                },
                guardianInformation: {
                    saved: [
                        {
                            cell_phone_number:
                                patientResource.contact && patientResource.contact[0] && patientResource.contact[0].telecom
                                    ? patientResource.contact[0].telecom[0].value
                                    : "",
                        },
                    ],
                },
                // Potentially add identifiers, addresses etc. here from patientResource
            };

            // Determine the latest 'lastUpdated' timestamp across all fetched bundles
            const allBundles = [patientBundle, observationBundle].filter(Boolean);
            const latestUpdate = allBundles.reduce((latest, bundle) => {
                if (bundle.meta && bundle.meta.lastUpdated) {
                    const currentBundleDate = new Date(bundle.meta.lastUpdated);
                    return latest === null || currentBundleDate > latest ? currentBundleDate : latest;
                }
                return latest;
            }, null);

            if (latestUpdate) {
                lastUpdatedDate.value = formatDateTime(latestUpdate.toISOString());
            }
        } else {
            console.warn("No patient bundle found for the provided identifier.");
            // Handle case where no patient data is found (e.g., show a message to the user)
        }
    } catch (error) {
        console.error("Error loading FHIR data:", error);
        // Clear data on error to prevent displaying partial/stale information
        fhirPatientData.value = [];
        lastUpdatedDate.value = null;
        extractedPatientData.value = null;
        extractedObservations.value = [];
        // In a real app, you'd show a user-friendly error message here
    } finally {
        isLoading.value = false; // Always reset loading state
    }
};

const getPatientName = (patient: any) => {
    if (patient.name && patient.name[0]) {
        const name = patient.name[0];
        const given = name.given ? name.given.join(" ") : "";
        const family = name.family || "";
        return `${given} ${family}`.trim() || "N/A";
    }
    return "N/A";
};

const getPatientIdentifiers = (patient: any) => {
    if (patient.identifier && patient.identifier.length > 0) {
        return patient.identifier.map((id: any) => {
            let systemDisplay = "Unknown System";
            if (id.type && id.type.coding && id.type.coding[0] && id.type.coding[0].display) {
                systemDisplay = id.type.coding[0].display;
            } else if (id.system) {
                const parts = id.system.split("/");
                systemDisplay = parts[parts.length - 1] || id.system;
                if (id.system === "urn:oid:2.16.840.1.113883.4.6") {
                    systemDisplay = "Social Security Number";
                }
            }
            sessionStorage.setItem(systemDisplay, id.value);

            let assignerDisplay = null;
            if (id.assigner && id.assigner.display) {
                assignerDisplay = id.assigner.display;
            } else if (id.assigner && id.assigner.reference) {
                assignerDisplay = id.assigner.reference.split("/").pop() || id.assigner.reference;
            }

            return {
                value: id.value || "N/A",
                systemDisplay: systemDisplay,
                periodStart: id.period ? id.period.start : null,
                periodEnd: id.period ? id.period.end : null,
                assignerDisplay: assignerDisplay,
            };
        });
    }
    return [];
};

const getPatientPhoneNumbers = (patient: any) => {
    if (patient.telecom && patient.telecom.length > 0) {
        return patient.telecom.filter((tl: any) => tl.system === "phone" && tl.value);
    }
    return [];
};

const getGuardianPhoneNumbers = (patient: any) => {
    if (patient.contact && patient.contact.length > 0) {
        // Find contacts with a 'GUARD' relationship
        const guardians = patient.contact.filter((contact: any) =>
            contact.relationship?.some((rel: any) => rel.coding?.some((code: any) => code.code === "GUARD"))
        );

        const guardianPhones: any[] = [];
        guardians.forEach((guardian: any) => {
            if (guardian.telecom && guardian.telecom.length > 0) {
                guardian.telecom.filter((tl: any) => tl.system === "phone" && tl.value).forEach((phone: any) => guardianPhones.push(phone));
            }
        });
        return guardianPhones;
    }
    return [];
};

const formatDate = (dateString: any) => {
    if (!dateString) return "N/A";
    try {
        return new Date(dateString).toLocaleDateString("en-US", {
            year: "numeric",
            month: "long",
            day: "numeric",
        });
    } catch (e) {
        console.error("Invalid date string for formatDate:", dateString, e);
        return "Invalid Date";
    }
};

const formatDateTime = (dateTimeString: any) => {
    if (!dateTimeString) return "N/A";
    try {
        const date = new Date(dateTimeString);
        return date.toLocaleDateString("en-US", {
            year: "numeric",
            month: "short",
            day: "numeric",
            hour: "2-digit",
            minute: "2-digit",
            hour12: true,
        });
    } catch (e) {
        console.error("Invalid date time string for formatDateTime:", dateTimeString, e);
        return "Invalid Date/Time";
    }
};

const calculateAge = (birthDate: any) => {
    if (!birthDate) return "N/A";
    try {
        const today = new Date();
        const birth = new Date(birthDate);
        let age = today.getFullYear() - birth.getFullYear();
        const monthDiff = today.getMonth() - birth.getMonth();
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
            age--;
        }
        return age;
    } catch (e) {
        console.error("Invalid birth date string for calculateAge:", birthDate, e);
        return "N/A";
    }
};

const getSyncStatus = (resource: any) => {
    if (resource.meta && resource.meta.tag) {
        const syncTag = resource.meta.tag.find((tag: any) => tag.system === "http://your-domain.org/fhir/StructureDefinition/sync-status");
        return syncTag ? syncTag.code : "Unknown";
    }
    return "Unknown";
};

const getObservationDisplay = (observation: any) => {
    if (observation.code && observation.code.coding && observation.code.coding[0]) {
        return observation.code.coding[0].display || observation.code.coding[0].code || "Unknown Observation";
    }
    return "Unknown Observation";
};

const getObservationValue = (observation: any) => {
    if (observation.valueQuantity) {
        return `${observation.valueQuantity.value} ${observation.valueQuantity.unit || ""}`.trim();
    } else if (observation.valueString) {
        return observation.valueString;
    } else if (observation.valueBoolean !== undefined) {
        return observation.valueBoolean ? "True" : "False";
    }
    return "N/A";
};

const registerNewPatient = () => {
    const demographicsStore = useDemographicsStore();
    demographicsStore.setRecord(extractedPatientData.value);
    sessionStorage.setItem("ichis_diagnosis", JSON.stringify(extractedObservations.value)); // Store the extracted observations in session storageextractedObservations.value);
    navigateTo("/registration/manual");
};

/**
 * Resets the component state to allow a new patient search.
 */
const backToSearch = () => {
    fhirPatientData.value = [];
    identifierID.value = "";
    lastUpdatedDate.value = null;
    extractedPatientData.value = null;
    extractedObservations.value = []; // Also clear extracted observations on back to search
};

const navigateTo = (path: string) => {
    route.push({ path });
};

watch(
    () => route,
    async () => {
        fhirPatientData.value = [];
        extractedObservations.value = [];
    },
    { deep: true }
);
// Lifecycle hook: Executed when the component is mounted to the DOM
onMounted(() => {
    // If initialData prop is provided, process it on mount
    if (props.initialData.length > 0) {
        fhirPatientData.value = props.initialData;

        // Calculate the latest update date from initial data bundles
        const allBundles = props.initialData.filter(Boolean);
        const latestUpdate: any = allBundles.reduce((latest: any, bundle: any) => {
            if (bundle.meta && bundle.meta.lastUpdated) {
                const currentBundleDate = new Date(bundle.meta.lastUpdated);
                return latest === null || currentBundleDate > latest ? currentBundleDate : latest;
            }
            return latest;
        }, null);

        if (latestUpdate) {
            lastUpdatedDate.value = formatDateTime(latestUpdate.toISOString());
        }

        // Attempt to extract patient information from initialData
        const patientBundle: any = props.initialData.find(
            (bundle: any) =>
                bundle.resourceType === "Bundle" &&
                bundle.entry &&
                bundle.entry.some((entry: any) => entry.resource && entry.resource.resourceType === "Patient")
        );

        if (patientBundle) {
            const patientResource = patientBundle.entry.find((entry: any) => entry.resource && entry.resource.resourceType === "Patient")?.resource;
            if (patientResource) {
                const name = patientResource.name && patientResource.name[0];
                extractedPatientData.value = {
                    personInformation: {
                        given_name: name && name.given ? name.given.join(" ") : "",
                        middle_name: name && name.middle ? name.middle.join(" ") : "",
                        family_name: name && name.family ? name.family : "",
                        gender: patientResource.gender || "",
                        birthdate: patientResource.birthDate || "",
                    },
                };
            }
        }

        // Extract and structure observation data from initialData
        const observationBundle: any = props.initialData.find(
            (bundle: any) =>
                bundle.resourceType === "Bundle" &&
                bundle.entry &&
                bundle.entry.some((entry: any) => entry.resource && entry.resource.resourceType === "Observation")
        );

        if (observationBundle) {
            extractedObservations.value = observationBundle.entry
                .filter((entry: any) => entry.resource && entry.resource.resourceType === "Observation")
                .map((entry: any) => {
                    const obs = entry.resource;
                    return {
                        id: obs.id,
                        display: getObservationDisplay(obs),
                        value: getObservationValue(obs),
                        date: obs.effectiveDateTime,
                        category:
                            obs.category && obs.category[0] && obs.category[0].coding && obs.category[0].coding[0]
                                ? obs.category[0].coding[0].display || obs.category[0].coding[0].code
                                : "N/A",
                    };
                });
        }
    }
});
</script>

<style scoped>
/* Base styles */
.fhir-viewer {
    font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
    height: 100vh;
    padding: 15px;
    background: #f7f7f7; /* Light grey background */
    color: #333;
    overflow: scroll;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* Header */
.main-header {
    padding-top: 15px;
}

.main-header .header-line {
    display: flex;
    justify-content: space-between; /* Pushes items to opposite ends */
    align-items: center;
    padding-bottom: 10px; /* Space above the bottom of the header */
    border-bottom: 1px solid #e0e0e0; /* Subtle separator */
    margin-bottom: 15px; /* Space between header line and content below (if any) */
}

.main-header .header-title {
    font-size: 1.2rem;
    margin: 0;
    font-weight: 700;
    letter-spacing: -0.5px;
    color: #1b5e20;
    text-align: left; /* Ensure it stays left-aligned */
}

.last-updated-display {
    font-size: 0.75rem; /* Smaller font for timestamp */
    color: #666;
    background-color: #f0f4f0; /* Even more subtle background */
    padding: 3px 8px; /* Reduced padding */
    border-radius: 12px; /* More rounded */
    border: 1px solid #e0e0e0; /* Lighter border */
    white-space: nowrap; /* Prevent wrapping */
    flex-shrink: 0; /* Don't let it shrink */
}

/* Card base style */
.card {
    background: #ffffff;
    border-radius: 15px;
    padding: 25px;
    border: 1px solid #e0e0e0;
    transition: transform 0.2s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-3px);
}

.card-title {
    font-size: 1.1rem;
    color: #2e7d32;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 700;
    padding-bottom: 12px;
    border-bottom: 1px solid rgba(46, 125, 50, 0.1);
}

.card-title ion-icon {
    font-size: 1.8rem;
    color: #4caf50;
}

/* Initial Input Card */
.initial-input-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    padding: 40px;
    text-align: center;
}

ion-input {
    --background: #fdfdfd;
    --border-radius: 10px;
    --padding-start: 15px;
    --border-color: #e0e0e0;
    --highlight-color-focused: #4caf50;
    transition: all 0.2s ease;
    width: 100%;
    max-width: 400px;
    font-size: 1rem;
    color: #444;
}

ion-input::part(label) {
    color: #666;
    font-weight: 500;
}

ion-input:focus-within {
    border-color: #4caf50;
}

.action-area-initial {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
}

/* General button styles for .load-data-btn */
.load-data-btn {
    background: linear-gradient(45deg, #4caf50, #388e3c);
    color: white;
    border: none;
    padding: 14px 30px;
    border-radius: 30px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    letter-spacing: 0.5px;
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 200px;
    justify-content: center;
}

.load-data-btn ion-icon {
    font-size: 1.2rem;
}

.load-data-btn:hover {
    transform: translateY(-2px);
    background: linear-gradient(45deg, #388e3c, #4caf50);
}

/* Small button styles applied to .register-btn and .back-to-search-btn */
.small-btn {
    padding: 10px 20px; /* Smaller padding */
    border-radius: 20px; /* More rounded */
    font-size: 0.9rem; /* Smaller font size */
    font-weight: 500; /* Lighter font weight */
    min-width: unset; /* Override min-width */
}

.register-btn {
    background: linear-gradient(45deg, #66bb6a, #43a047);
}

.back-to-search-btn {
    background: #757575; /* Grey for back button */
}

.register-btn:hover {
    transform: translateY(-2px);
    background: linear-gradient(45deg, #43a047, #66bb6a);
}

.back-to-search-btn:hover {
    background: #5a5a5a;
    transform: translateY(-1px);
}

.small-btn ion-icon {
    font-size: 1.1rem; /* Adjust icon size for small buttons */
}

.no-data-message {
    font-size: 0.9rem;
    color: #777;
    margin-top: 8px;
}

/* Patient Info Grid */
.patient-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 15px;
}

.info-item {
    padding: 18px;
    background: rgba(76, 175, 80, 0.05);
    border-radius: 12px;
    border-left: 5px solid #4caf50;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    transition: background 0.2s ease;
}

.info-item:hover {
    background: rgba(76, 175, 80, 0.08);
}

.info-label {
    font-weight: 600;
    color: #555;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 6px;
}

.info-value {
    font-size: 1rem;
    color: #222;
    font-weight: 500;
    word-break: break-word;
}

.age-item .info-value {
    font-size: 1.3rem;
    font-weight: 700;
    color: #1b5e20;
}

/* Styles for individual identifier info-items */
.identifier-info-item {
    /* Specific styling if needed, default info-item is good */
    /* You could adjust border-left color here if you want identifiers to stand out */
    border-left-color: #2196f3; /* A different color for identifiers, for example */
    background: rgba(33, 150, 243, 0.05); /* Lighter blue background */
}

.identifier-info-item:hover {
    background: rgba(33, 150, 243, 0.08);
}

.identifier-info-item .info-label {
    /* The systemDisplay will be the label now */
    color: #2196f3; /* Matching the border */
}

.identifier-value-highlight {
    font-size: 1.3rem; /* Make the ID value more prominent */
    font-weight: 700;
    color: #0d47a1; /* Darker blue */
}

.identifier-sub-details {
    display: flex;
    flex-wrap: wrap;
    gap: 8px 10px;
    margin-top: 10px;
    padding-top: 8px;
    border-top: 1px dotted rgba(0, 0, 0, 0.1);
}

.sub-detail-chip {
    background-color: rgba(33, 150, 243, 0.15); /* Light blue background for chips */
    color: #1976d2; /* Darker blue text */
    padding: 4px 10px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
    white-space: nowrap;
}

/* Styles for phone number info-items */
.phone-info-item {
    border-left-color: #ff9800; /* Orange color for phone numbers */
    background: rgba(255, 152, 0, 0.05); /* Lighter orange background */
}

.phone-info-item:hover {
    background: rgba(255, 152, 0, 0.08);
}

.phone-info-item .info-label {
    color: #f57c00; /* Darker orange for label */
}

/* Observations Grid */
.observations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 15px;
}

.observation-item {
    background: #ffffff;
    border-radius: 15px;
    padding: 20px;
    border: 1px solid rgba(76, 175, 80, 0.2);
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;
}

.observation-item:hover {
    transform: translateY(-3px);
    border-color: #4caf50;
}

.obs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.obs-title {
    font-weight: 700;
    color: #333;
    font-size: 1rem;
    margin: 0;
}

.obs-details {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.obs-detail {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    padding: 3px 0;
    border-bottom: 1px dotted rgba(0, 0, 0, 0.08);
}

.obs-detail:last-child {
    border-bottom: none;
}

.obs-label {
    font-weight: 500;
    color: #666;
    font-size: 0.9rem;
    flex-shrink: 0;
    margin-right: 10px;
}

.obs-value {
    font-weight: 600;
    color: #212121;
    font-size: 0.8rem;
    text-align: right;
}

/* Highlighted Observation Value */
.obs-value-highlight .obs-value {
    font-size: 1.1rem;
    font-weight: 700;
    color: #1b5e20;
    padding: 2px 6px;
    background-color: #e6f7ea;
    border-radius: 4px;
}

/* Status Badges (kept for patient card) */
.status-badge {
    display: inline-block;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    white-space: nowrap;
}

.status-ichis-mahis-pending {
    background: #eceff1;
    color: #607d8b;
    border: 1px solid #cfd8dc;
}

/* No Data Found Messages */
.no-data-found {
    text-align: center;
    padding: 30px;
    background: #ffffff;
    border-radius: 15px;
    border: 1px solid #e0e0e0;
    color: #555;
    font-size: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.no-data-found ion-icon {
    font-size: 1.6rem;
    color: #95a695;
}

/* Main Footer */
.main-footer {
    display: flex;
    justify-content: space-between;
    gap: 20px;
    padding: 25px 0;
    margin-top: 20px;
    border-top: 1px solid #e0e0e0;
}

/* Responsive Design */
@media (max-width: 992px) {
    .container {
        padding: 0 18px;
    }
    .card {
        padding: 20px;
    }
    .card-title {
        font-size: 1.4rem;
    }
    .patient-info-grid,
    .observations-grid {
        grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    }
    .load-data-btn {
        padding: 12px 25px;
        font-size: 0.95rem;
    }
    .main-footer {
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }
    .small-btn {
        width: 100%;
    }
}
</style>
