import { ref, computed } from "vue";
import { FormElement } from "@/interfaces/FormInterface";
import { icons } from "@/utils/svg";
import StandardValidations from "@/validations/StandardValidations";
import { useVitals } from "@/composables/useVitals";

export const useTemperaturePulseRateForm = () => {
    const vitalsComposable = useVitals();
    const temperaturePulseRateForm = computed(() => {
        return [
            {
                componentType: "Heading",
                name: "Temperature and rates",
                grid: { s: "3" },
            },
            {
                componentType: "inputField",
                name: "Temperature",
                header: "Temperature",
                unit: "°C",
                type: "number",
                icon: icons.temprature,
                grid: { s: "4.5" },
                validation: (value: string) => {
                    return StandardValidations.vitalsTemperature(value);
                },
                disabled: (allFormValues: any) => {
                    return allFormValues["Check temperature not done"];
                },
            },
            {
                componentType: "inputField",
                name: "Pulse",
                header: "Pulse rate",
                unit: "BMP",
                icon: icons.pulse,
                type: "number",
                grid: { s: "4.5" },
                validation: (value: string) => {
                    return StandardValidations.vitalsPulseRate(value);
                },
                disabled: (allFormValues: any) => {
                    return allFormValues["Check pulse rate not done"];
                },
            },

            {
                grid: { s: "3" },
            },
            {
                componentType: "Alert",
                grid: { s: "9" },
                condition: async (allFormValues: any) => {
                    if (StandardValidations.vitalsTemperature(allFormValues["Temperature"]) == null) {
                        const tempStatus = vitalsComposable.getTemperatureStatus(allFormValues["Temperature"]);
                        return await vitalsComposable.updateRate("temp", allFormValues["Temperature"], "°C", tempStatus, 4);
                    } else {
                        return false;
                    }
                },
            },

            {
                grid: { s: "3" },
            },
            {
                componentType: "Alert",
                grid: { s: "9" },
                condition: async (allFormValues: any) => {
                    if (StandardValidations.vitalsPulseRate(allFormValues["Pulse"]) == null) {
                        const pulseStatus = vitalsComposable.getPulseRateStatus(allFormValues["Pulse"]);
                        return await vitalsComposable.updateRate("pulse", allFormValues["Pulse"], "BMP", pulseStatus, 4);
                    } else {
                        return false;
                    }
                },
            },

            {
                grid: { s: "3" },
            },
            {
                componentType: "checkboxField",
                name: "Check temperature not done",
                type: "single",
                label: "Temperature not done",
                grid: { s: "4.5" },
            },
            {
                componentType: "checkboxField",
                name: "Check pulse rate not done",
                type: "single",
                label: "Pulse not done",
                grid: { s: "4.5" },
            },

            {
                grid: { s: "3" },
            },
            {
                componentType: "multiSelectInputField",
                header: "Specify Reason",
                name: "Temperature",
                isMultiple: false,
                trackBy: "id",
                grid: { s: "4.5" },
                icon: icons.search,
                options: [
                    {
                        id: 1,
                        name: "Patient uncooperative",
                    },
                    {
                        id: 2,
                        name: "Machine not working",
                    },
                    {
                        id: 3,
                        name: "Machine not available",
                    },
                ],
                validation: (value: any) => {
                    if (!value || value.length === 0) {
                        return "Please select at least one option";
                    }
                    return null;
                },
                condition: (allFormValues: any) => {
                    return allFormValues["Check temperature not done"];
                },
                taggable: false,
                hideSelected: false,
                closeOnSelect: true,
            },
            {
                componentType: "multiSelectInputField",
                header: "Specify Reason",
                name: "Pulse",
                isMultiple: false,
                trackBy: "id",
                grid: { s: "4.5" },
                icon: icons.search,
                options: [
                    {
                        id: 1,
                        name: "Patient uncooperative",
                    },
                    {
                        id: 2,
                        name: "Machine not working",
                    },
                    {
                        id: 3,
                        name: "Machine not available",
                    },
                ],
                validation: (value: any) => {
                    if (!value || value.length === 0) {
                        return "Please select at least one option";
                    }
                    return null;
                },
                condition: (allFormValues: any) => {
                    return allFormValues["Check pulse rate not done"];
                },
                taggable: false,
                hideSelected: false,
                closeOnSelect: true,
            },
        ] satisfies FormElement[];
    });

    return {
        temperaturePulseRateForm,
    };
};
