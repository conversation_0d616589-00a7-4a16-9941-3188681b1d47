// composables/forms/useHeightWeightForm.ts
import { ref, computed } from "vue";
import { FormElement } from "@/interfaces/FormInterface";
import { icons } from "@/utils/svg";
import StandardValidations from "@/validations/StandardValidations";
import { useVitals } from "@/composables/useVitals";

export const useHeightWeightForm = () => {
    const vitalsComposable = useVitals();
    const height = ref("");

    // Load height data
    const loadHeight = async () => {
        height.value = await vitalsComposable.checkHeight();
    };

    // Height and Weight form section
    const heightWeightFormSection = computed(() => {
        return [
            {
                componentType: "Heading",
                name: "Height and weight",
                grid: { s: "3" },
            },
            {
                componentType: "inputField",
                name: "height",
                header: "Height",
                unit: "cm",
                type: "number",
                icon: icons.height,
                value: height.value || "",
                grid: { s: "4.5" },
                validation: (value: string) => {
                    return StandardValidations.vitalsHeight(value);
                },
                disabled: (allFormValues: any) => {
                    return allFormValues["Check height not done"] || height.value;
                },
            },
            {
                componentType: "inputField",
                name: "weight",
                header: "Weight",
                unit: "kg",
                icon: icons.weight,
                type: "number",
                grid: { s: "4.5" },
                validation: (value: string) => {
                    return StandardValidations.vitalsWeight(value);
                },
                disabled: (allFormValues: any) => {
                    return allFormValues["Check weight not done"];
                },
            },

            {
                grid: { s: "3" },
            },
            {
                componentType: "Alert",
                condition: async (allFormValues: any) => {
                    if (
                        StandardValidations.vitalsWeight(allFormValues.weight) == null &&
                        StandardValidations.vitalsHeight(allFormValues.height) == null
                    ) {
                        return await vitalsComposable.setBMI(allFormValues.height, allFormValues.weight);
                    } else {
                        return false;
                    }
                },
                grid: { s: "9" },
            },

            {
                grid: { s: "3" },
            },
            {
                componentType: "checkboxField",
                name: "Check height not done",
                type: "single",
                label: "Height not done",
                value: "",
                grid: { s: "4.5" },
                disabled: (allFormValues: any) => {
                    return height.value;
                },
            },
            {
                componentType: "checkboxField",
                name: "Check weight not done",
                type: "single",
                label: "Weight not done",
                grid: { s: "4.5" },
            },

            {
                grid: { s: "3" },
            },
            {
                componentType: "multiSelectInputField",
                header: "Specify Reason",
                name: "height",
                isMultiple: false,
                trackBy: "id",
                grid: { s: "4.5" },
                icon: icons.search,
                options: [
                    {
                        id: 1,
                        name: "Patient uncooperative",
                    },
                    {
                        id: 2,
                        name: "Machine not working",
                    },
                    {
                        id: 3,
                        name: "Machine not available",
                    },
                ],
                validation: (value: any) => {
                    if (!value || value.length === 0) {
                        return "Please select at least one option";
                    }
                    return null;
                },
                condition: (allFormValues: any) => {
                    return allFormValues["Check height not done"];
                },
                taggable: false,
                hideSelected: false,
                closeOnSelect: true,
            },
            {
                componentType: "multiSelectInputField",
                header: "Specify Reason",
                name: "Weight",
                isMultiple: false,
                trackBy: "id",
                grid: { s: "4.5" },
                icon: icons.search,
                options: [
                    {
                        id: 1,
                        name: "Patient uncooperative",
                    },
                    {
                        id: 2,
                        name: "Machine not working",
                    },
                    {
                        id: 3,
                        name: "Machine not available",
                    },
                ],
                validation: (value: any) => {
                    if (!value || value.length === 0) {
                        return "Please select at least one option";
                    }
                    return null;
                },
                condition: (allFormValues: any) => {
                    return allFormValues["Check weight not done"];
                },
                taggable: false,
                hideSelected: false,
                closeOnSelect: true,
            },
        ] satisfies FormElement[];
    });

    return {
        height,
        loadHeight,
        heightWeightFormSection,
    };
};
