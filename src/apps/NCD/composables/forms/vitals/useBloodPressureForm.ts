// composables/forms/useBloodPressureForm.ts
import { computed } from "vue";
import { FormElement } from "@/interfaces/FormInterface";
import { icons } from "@/utils/svg";
import StandardValidations from "@/validations/StandardValidations";
import { useVitals } from "@/composables/useVitals";

export const useBloodPressureForm = () => {
    const vitalsComposable = useVitals();

    // Blood Pressure form section
    const bloodPressureFormSection = computed(() => {
        return [
            {
                componentType: "Heading",
                name: "Blood pressure",
                grid: { s: "3" },
            },
            {
                componentType: "inputField",
                name: "Systolic",
                header: "Systolic Pressure",
                unit: "mmHg",
                type: "number",
                icon: icons.systolicPressure,
                grid: { s: "4.5" },
                validation: (value: string) => {
                    return StandardValidations.vitalsSystolic(value);
                },
                disabled: (allFormValues: any) => {
                    return allFormValues["Check blood pressure not done"];
                },
            },
            {
                componentType: "inputField",
                name: "Diastolic",
                header: "Diastolic pressure",
                unit: "mmHg",
                icon: icons.diastolicPressure,
                type: "number",
                grid: { s: "4.5" },
                validation: (value: string) => {
                    return StandardValidations.vitalsDiastolic(value);
                },
                disabled: (allFormValues: any) => {
                    return allFormValues["Check blood pressure not done"];
                },
            },

            {
                grid: { s: "3" },
            },
            {
                componentType: "Alert",
                grid: { s: "9" },
                condition: async (allFormValues: any) => {
                    if (
                        StandardValidations.vitalsSystolic(allFormValues.Systolic) == null &&
                        StandardValidations.vitalsDiastolic(allFormValues.Diastolic) == null
                    ) {
                        return await vitalsComposable.updateBP(allFormValues.Systolic, allFormValues.Diastolic);
                    } else {
                        return false;
                    }
                },
            },

            {
                grid: { s: "3" },
            },
            {
                componentType: "checkboxField",
                name: "Check blood pressure not done",
                type: "single",
                label: "Blood pressure not done",
                value: "",
                grid: { s: "4.5" },
            },
            {
                componentType: "multiSelectInputField",
                header: "Specify Reason",
                name: "Blood Pressure",
                isMultiple: false,
                trackBy: "id",
                grid: { s: "4.5" },
                icon: icons.search,
                options: [
                    {
                        id: 1,
                        name: "Patient uncooperative",
                    },
                    {
                        id: 2,
                        name: "Machine not working",
                    },
                    {
                        id: 3,
                        name: "Machine not available",
                    },
                ],
                validation: (value: any) => {
                    if (!value || value.length === 0) {
                        return "Please select at least one option";
                    }
                    return null;
                },
                condition: (allFormValues: any) => {
                    return allFormValues["Check blood pressure not done"];
                },
                taggable: false,
                hideSelected: false,
                closeOnSelect: true,
            },
        ] satisfies FormElement[];
    });

    return {
        bloodPressureFormSection,
    };
};
