import { computed } from "vue";
import { FormElement } from "@/interfaces/FormInterface";
import { icons } from "@/utils/svg";
import StandardValidations from "@/validations/StandardValidations";
import { useVitals } from "@/composables/useVitals";

export const useRespiratoryRateOxygenForm = () => {
    const vitalsComposable = useVitals();
    const respiratoryRateOxygenForm = computed(() => {
        return [
            {
                grid: { s: "3" },
            },
            {
                componentType: "inputField",
                name: "Respiratory rate",
                header: "Respiratory rate",
                unit: "BMP",
                icon: icons.respiratory,
                type: "number",
                grid: { s: "4.5" },
                validation: (value: string) => {
                    return StandardValidations.vitalsRespiratoryRate(value);
                },
                disabled: (allFormValues: any) => {
                    return allFormValues["Check respiratory rate not done"];
                },
            },
            {
                componentType: "inputField",
                name: "SAO2",
                header: "Oxygen saturation",
                unit: "%",
                icon: icons.oxgenStaturation,
                type: "number",
                grid: { s: "4.5" },
                validation: (value: string) => {
                    return StandardValidations.vitalsOxygenSaturation(value);
                },
                disabled: (allFormValues: any) => {
                    return allFormValues["Check oxygen saturation not done"];
                },
            },

            {
                grid: { s: "3" },
            },
            {
                componentType: "Alert",
                grid: { s: "9" },
                condition: async (allFormValues: any) => {
                    if (StandardValidations.vitalsRespiratoryRate(allFormValues["Respiratory rate"]) == null) {
                        const respiratoryStatus = vitalsComposable.getRespiratoryRateStatus(allFormValues["Respiratory rate"]);
                        return await vitalsComposable.updateRate("respiratory", allFormValues["Respiratory rate"], "BMP", respiratoryStatus, 4);
                    } else {
                        return false;
                    }
                },
            },

            {
                grid: { s: "3" },
            },
            {
                componentType: "Alert",
                grid: { s: "9" },
                condition: async (allFormValues: any) => {
                    if (StandardValidations.vitalsOxygenSaturation(allFormValues["SAO2"]) == null) {
                        const SAO2 = vitalsComposable.getOxygenSaturationStatus(allFormValues["SAO2"]);
                        return await vitalsComposable.updateRate("oxygen", allFormValues["SAO2"], "%", SAO2, 4);
                    } else {
                        return false;
                    }
                },
            },

            {
                grid: { s: "3" },
            },

            {
                componentType: "checkboxField",
                name: "Check respiratory rate not done",
                type: "single",
                label: "Respiratory rate not done",
                grid: { s: "4.5" },
            },
            {
                componentType: "checkboxField",
                name: "Check oxygen saturation not done",
                type: "single",
                label: "Oxygen saturation not done",
                value: "",
                grid: { s: "4.5" },
            },

            {
                grid: { s: "3" },
            },
            {
                componentType: "multiSelectInputField",
                header: "Specify Reason",
                name: "Respiratory rate",
                isMultiple: false,
                trackBy: "id",
                grid: { s: "4.5" },
                icon: icons.search,
                options: [
                    {
                        id: 1,
                        name: "Patient uncooperative",
                    },
                    {
                        id: 2,
                        name: "Machine not working",
                    },
                    {
                        id: 3,
                        name: "Machine not available",
                    },
                ],
                validation: (value: any) => {
                    if (!value || value.length === 0) {
                        return "Please select at least one option";
                    }
                    return null;
                },
                condition: (allFormValues: any) => {
                    return allFormValues["Check respiratory rate not done"];
                },
                taggable: false,
                hideSelected: false,
                closeOnSelect: true,
            },
            {
                componentType: "multiSelectInputField",
                header: "Specify Reason",
                name: "SAO2",
                isMultiple: false,
                trackBy: "id",
                grid: { s: "4.5" },
                icon: icons.search,
                options: [
                    {
                        id: 1,
                        name: "Patient uncooperative",
                    },
                    {
                        id: 2,
                        name: "Machine not working",
                    },
                    {
                        id: 3,
                        name: "Machine not available",
                    },
                ],
                validation: (value: any) => {
                    if (!value || value.length === 0) {
                        return "Please select at least one option";
                    }
                    return null;
                },
                condition: (allFormValues: any) => {
                    return allFormValues["Check oxygen saturation not done"];
                },
                taggable: false,
                hideSelected: false,
                closeOnSelect: true,
            },
        ] satisfies FormElement[];
    });

    return {
        respiratoryRateOxygenForm,
    };
};
