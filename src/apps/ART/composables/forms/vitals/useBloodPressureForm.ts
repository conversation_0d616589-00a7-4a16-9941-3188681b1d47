// composables/forms/useBloodPressureForm.ts
import { computed } from "vue";
import { FormElement } from "@/interfaces/FormInterface";
import { icons } from "@/utils/svg";
import StandardValidations from "@/validations/StandardValidations";
import { useVitals } from "@/composables/useVitals";

export const useBloodPressureForm = () => {
    const vitalsComposable = useVitals();

    // Blood Pressure form section
    const bloodPressureFormSection = computed(() => {
        return [
            {
                componentType: "Heading",
                name: "Blood pressure",
                grid: { s: "3" },
            },
            {
                componentType: "inputField",
                name: "Systolic",
                header: "Systolic Pressure",
                unit: "mmHg",
                type: "number",
                icon: icons.systolicPressure,
                grid: { s: "4.5" },
                validation: (value: string) => {
                    return StandardValidations.notRequiredVitalsSystolicPressure(value);
                },
            },
            {
                componentType: "inputField",
                name: "Diastolic",
                header: "Diastolic pressure",
                unit: "mmHg",
                icon: icons.diastolicPressure,
                type: "number",
                grid: { s: "4.5" },
                validation: (value: string) => {
                    return StandardValidations.notRequiredVitalsDiastolicPressure(value);
                },
            },
            {
                grid: { s: "3" },
            },
            {
                componentType: "Alert",
                grid: { s: "9" },
                condition: async (allFormValues: any) => {
                    if (
                        StandardValidations.vitalsSystolic(allFormValues.Systolic) == null &&
                        StandardValidations.vitalsDiastolic(allFormValues.Diastolic) == null
                    ) {
                        return await vitalsComposable.updateBP(allFormValues.Systolic, allFormValues.Diastolic);
                    } else {
                        return false;
                    }
                },
            },
        ] satisfies FormElement[];
    });

    return {
        bloodPressureFormSection,
    };
};
