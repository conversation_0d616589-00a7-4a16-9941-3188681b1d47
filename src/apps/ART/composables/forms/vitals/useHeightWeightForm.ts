// composables/forms/useHeightWeightForm.ts
import { ref, computed } from "vue";
import { FormElement } from "@/interfaces/FormInterface";
import { icons } from "@/utils/svg";
import StandardValidations from "@/validations/StandardValidations";
import { useVitals } from "@/composables/useVitals";

export const useHeightWeightForm = () => {
    const vitalsComposable = useVitals();
    const height = ref("");

    // Load height data
    const loadHeight = async () => {
        height.value = await vitalsComposable.checkHeightServer();
    };

    // Height and Weight form section
    const heightWeightFormSection = computed(() => {
        return [
            {
                componentType: "Heading",
                name: "Height and weight",
                grid: { s: "3" },
            },
            {
                componentType: "inputField",
                name: "Height (cm)",
                header: "Height",
                unit: "cm",
                type: "number",
                icon: icons.height,
                value: height.value || "",
                grid: { s: "4.5" },
                validation: (value: string) => {
                    return StandardValidations.vitalsHeight(value);
                },
                disabled: () => {
                    return height.value;
                },
            },
            {
                componentType: "inputField",
                name: "Weight",
                header: "Weight",
                unit: "kg",
                icon: icons.weight,
                type: "number",
                grid: { s: "4.5" },
                validation: (value: string) => {
                    return StandardValidations.vitalsWeight(value);
                },
            },

            {
                grid: { s: "3" },
            },
            {
                componentType: "Alert",
                grid: { s: "9" },
                condition: async (allFormValues: any) => {
                    if (
                        StandardValidations.vitalsWeight(allFormValues.Weight) == null &&
                        StandardValidations.vitalsHeight(allFormValues["Height (cm)"]) == null
                    ) {
                        return await vitalsComposable.setBMI(allFormValues["Height (cm)"], allFormValues.Weight);
                    } else {
                        return false;
                    }
                },
            },
        ] satisfies FormElement[];
    });

    return {
        height,
        loadHeight,
        heightWeightFormSection,
    };
};
