import { ref, computed } from "vue";
import { FormElement } from "@/interfaces/FormInterface";
import { icons } from "@/utils/svg";
import StandardValidations from "@/validations/StandardValidations";
import { useVitals } from "@/composables/useVitals";

export const useTemperaturePulseRateForm = () => {
    const vitalsComposable = useVitals();
    const temperaturePulseRateForm = computed(() => {
        return [
            {
                componentType: "Heading",
                name: "Temperature and rates",
                grid: { s: "3" },
            },
            {
                componentType: "inputField",
                name: "Temperature",
                header: "Temperature",
                unit: "°C",
                type: "number",
                icon: icons.temprature,
                grid: { s: "4.5" },
                validation: (value: string) => {
                    return StandardValidations.notRequiredVitalsTemperature(value);
                },
            },
            {
                componentType: "inputField",
                name: "Pulse",
                header: "Pulse rate",
                unit: "BMP",
                icon: icons.pulse,
                type: "number",
                grid: { s: "4.5" },
                validation: (value: string) => {
                    return StandardValidations.notRequiredVitalsPulseRate(value);
                },
            },

            {
                grid: { s: "3" },
            },
            {
                componentType: "Alert",
                grid: { s: "9" },
                condition: async (allFormValues: any) => {
                    if (StandardValidations.vitalsTemperature(allFormValues["Temperature"]) == null) {
                        const tempStatus = vitalsComposable.getTemperatureStatus(allFormValues["Temperature"]);
                        return await vitalsComposable.updateRate("temp", allFormValues["Temperature"], "°C", tempStatus, 4);
                    } else {
                        return false;
                    }
                },
            },

            {
                grid: { s: "3" },
            },
            {
                componentType: "Alert",
                grid: { s: "9" },
                condition: async (allFormValues: any) => {
                    if (StandardValidations.vitalsPulseRate(allFormValues["Pulse"]) == null) {
                        const pulseStatus = vitalsComposable.getPulseRateStatus(allFormValues["Pulse"]);
                        return await vitalsComposable.updateRate("pulse", allFormValues["Pulse"], "BMP", pulseStatus, 4);
                    } else {
                        return false;
                    }
                },
            },
        ] satisfies FormElement[];
    });

    return {
        temperaturePulseRateForm,
    };
};
