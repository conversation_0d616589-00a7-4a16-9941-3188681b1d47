import { computed } from "vue";
import { FormElement } from "@/interfaces/FormInterface";
import { icons } from "@/utils/svg";
import StandardValidations from "@/validations/StandardValidations";
import { useVitals } from "@/composables/useVitals";

export const useRespiratoryRateOxygenForm = () => {
    const vitalsComposable = useVitals();
    const respiratoryRateOxygenForm = computed(() => {
        return [
            {
                grid: { s: "3" },
            },
            {
                componentType: "inputField",
                name: "Respiratory rate",
                header: "Respiratory rate",
                unit: "BMP",
                icon: icons.respiratory,
                type: "number",
                grid: { s: "4.5" },
                validation: (value: string) => {
                    return StandardValidations.notRequiredVitalsRespiratoryRate(value);
                },
            },
            {
                componentType: "inputField",
                name: "SAO2",
                header: "Oxygen saturation",
                unit: "%",
                icon: icons.oxgenStaturation,
                type: "number",
                grid: { s: "4.5" },
                validation: (value: string) => {
                    return StandardValidations.notRequiredVitalsOxygenSaturation(value);
                },
            },

            {
                grid: { s: "3" },
            },
            {
                componentType: "Alert",
                grid: { s: "9" },
                condition: async (allFormValues: any) => {
                    if (StandardValidations.vitalsRespiratoryRate(allFormValues["Respiratory rate"]) == null) {
                        const respiratoryStatus = vitalsComposable.getRespiratoryRateStatus(allFormValues["Respiratory rate"]);
                        return await vitalsComposable.updateRate("respiratory", allFormValues["Respiratory rate"], "BMP", respiratoryStatus, 4);
                    } else {
                        return false;
                    }
                },
            },

            {
                grid: { s: "3" },
            },
            {
                componentType: "Alert",
                grid: { s: "9" },
                condition: async (allFormValues: any) => {
                    if (StandardValidations.vitalsOxygenSaturation(allFormValues["SAO2"]) == null) {
                        const SAO2 = vitalsComposable.getOxygenSaturationStatus(allFormValues["SAO2"]);
                        return await vitalsComposable.updateRate("oxygen", allFormValues["SAO2"], "%", SAO2, 4);
                    } else {
                        return false;
                    }
                },
            },
        ] satisfies FormElement[];
    });

    return {
        respiratoryRateOxygenForm,
    };
};
