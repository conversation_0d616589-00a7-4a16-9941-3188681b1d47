<template>
    <ion-page>
        <Toolbar />
        <ion-content>
            <div class="container">
                <h4 style="width: 100%; text-align: center; font-weight: 700">Mastercard</h4>
            </div>
            <ion-grid>
                <ion-row>
                    <ion-col size-lg="3" size-md="4" size-sm="12" v-for="(card, index) in displayCards" :key="index">
                        <ion-card>
                            <ion-card-content>
                                <ion-list>
                                    <ion-item v-for="(option, oIndex) in card" :key="oIndex">
                                        <ion-label>{{ option.label }}</ion-label>
                                        <ion-label style="font-weight: bold;" slot="end">
                                            {{
                                            option.value
                                            }}</ion-label>
                                    </ion-item>
                                </ion-list>
                            </ion-card-content>
                        </ion-card>
                    </ion-col>
                </ion-row>
            </ion-grid>
            <ion-progress-bar v-if="isTableLoading" type="indeterminate"></ion-progress-bar>
            <table class="styled-table">
                <thead>
                    <tr>
                        <th>VISIT DATE</th>
                        <th>WEIGHT (Kg)</th>
                        <th>REG</th>
                        <th>VIRAL LOAD</th>
                        <th>TB STATUS</th>
                        <th>OUTCOME</th>
                        <th>PILLS DISPENSED</th>
                        <th>ACTIONS</th>
                    </tr>
                </thead>
                <tbody v-if="isTableInit">
                    <tr v-for="(opt, index) in patientVisitRows" :key="index">
                        <td v-for="(td, tdIndex) in opt" :key="tdIndex">
                            <span v-html="td.val" v-if="td.val"></span>
                            <ion-button @click="td.btn.onClick"
                                v-else-if="td.btn && typeof td?.btn.onClick === 'function'">
                                {{ td.btn.label }}
                            </ion-button>
                        </td>
                    </tr>
                </tbody>
                <tbody v-else>
                    <tr v-for="row in 6" :key="row">
                        <td>...</td>
                        <td>...</td>
                        <td>...</td>
                        <td>...</td>
                        <td>...</td>
                        <td>...</td>
                        <td>...</td>
                        <td>...</td>
                    </tr>
                </tbody>
            </table>
            <!-- Ionic Modal -->
            <ion-modal :is-open="isModalVisible" @ionModalDidDismiss="closeModal">
                <div class="modal-content">
                    <h3>Details</h3>
                    <ul>
                        <li v-for="detail in modalDetails" :key="detail.label">
                            <strong>{{ detail.label }}:</strong> {{ detail.value }}
                        </li>
                    </ul>
                    <ion-button expand="block" @click="closeModal">Close</ion-button>
                </div>
            </ion-modal>
        </ion-content>
    </ion-page>

</template>
<script lang="ts" setup>
import {
    IonButton,
    IonProgressBar,
    IonLabel,
    IonList,
    IonItem,
    IonGrid,
    IonRow,
    IonCol,
    IonCard,
    IonCardContent,
    IonPage,
    IonContent
} from "@ionic/vue"
import Toolbar from "@/components/Toolbar.vue";
import { onMounted, ref, computed } from "vue"
import { PatientService } from "@/services/patient_service";
import { RelationshipService } from "@/services/relationship_service";
import { chunk, isEmpty } from "lodash";
import { ObservationService } from "@/services/observation_service";
import HisDate from "@/utils/Date";
import Date from "@/utils/Date";
import { ProgramService } from "@/services/program_service";

const tbStats = ref<any>([])
const cardContent = ref<any>([])
const patient = new PatientService()
const patientVisitRows = ref<any>([])
const isModalVisible = ref(false);
const modalDetails = ref<any>([]);
const displayCards = computed(() => chunk(cardContent.value, 7) as any)

const isTableInit = ref(false)
const isTableLoading = ref(false)

function hasTbStat(conceptName: string) {
    return tbStats.value.includes(conceptName) ? 'Yes' : 'No'
}

function setPatientCards() {
    cardContent.value = [
        {
            label: "ARV Number",
            value: '...',
            staticValue: () => patient.getArvNumber(),
            other: {
                editable: true,
                category: "arv_number"
            }
        },
        {
            label: "National Patient ID",
            value: '...',
            staticValue: () => patient.getNationalID()
        },
        {
            label: "Given Name",
            value: '...',
            staticValue: () => patient.getGivenName(),
            other: {
                editable: true,
                attribute: "given_name",
                category: "demographics",
            }
        },
        {
            label: "Family Name",
            value: '...',
            staticValue: () => patient.getFamilyName(),
            other: {
                editable: true,
                attribute: "family_name",
                category: "demographics",
            },
        },
        {
            label: "Age",
            value: '...',
            staticValue: () => patient.getAge(),
            other: {
                editable: true,
                attribute: "year_birth_date",
                category: "demographics",
            }
        },
        {
            label: "Sex",
            value: '...',
            staticValue: () => patient.getGender(),
            other: {
                editable: true,
                attribute: "gender",
                category: "demographics",
            }
        },
        {
            label: "Location",
            value: '...',
            staticValue: () => patient.getCurrentVillage(),
            other: {
                editable: true,
                attribute: "home_region",
                category: "demographics",
            }
        },
        {
            label: "Landmark",
            value: '...',
            // staticValue: () => patient.getAttribute(19)
        },
        {
            label: "Guardian",
            value: '...',
            asyncValue: async (item: any) => {
                const relations = await RelationshipService.getGuardianDetails(patient.getID())
                if (isEmpty(relations)) {
                    item.other.editable = true
                    return 'add'
                }
                return relations.map((r: any) => ` ${r.name} (${r.relationshipType})`).join(" ")
            },
            other: {
                editable: false,
                attribute: "",
                category: "guardian",
            }
        },
        {
            label: "Init W(KG)",
            value: '...',
            asyncValue: () => patient.getInitialWeight()
        },
        {
            label: "Init H(CM)",
            value: '...',
            asyncValue: () => patient.getInitialHeight()
        },
        {
            label: "Init BMI",
            value: '...',
            asyncValue: () => patient.getInitialBMI()
        },
        {
            label: 'init Preg',
            value: '...',
            condition: () => patient.isFemale(),
            asyncValue: () => patient.getInitialObs('Is patient pregnant', 'value_coded')
        },
        {
            label: 'init Breastfeeding',
            value: '...',
            condition: () => patient.isFemale(),
            asyncValue: () => patient.getInitialObs('Is patient breast feeding', 'value_coded')
        },
        {
            label: 'cur Preg',
            value: '...',
            condition: () => patient.isFemale(),
            asyncValue: async () => (await patient.isPregnant()) ? 'Yes' : 'No'
        },
        {
            label: 'cur Breastfeeding',
            value: '...',
            condition: () => patient.isFemale(),
            asyncValue: async () => (await patient.isBreastfeeding()) ? 'Yes' : 'No'
        },
        {
            label: "TI",
            value: '...',
            asyncValue: () => ObservationService.getFirstValueCoded(
                patient.getID(), "Ever received ART"
            )
        },
        {
            label: "Agrees to follow up",
            value: '...',
            asyncValue: () => ObservationService.getFirstValueCoded(
                patient.getID(), "Agrees to followup"
            )
        },
        {
            label: "Reason for starting ART",
            value: '...',
            asyncValue: () => ObservationService.getFirstValueCoded(
                patient.getID(), "Reason for ART eligibility"
            )
        },
        {
            label: "HIV test date",
            value: '...',
            asyncValue: async () => {
                const date = await ObservationService.getFirstValueDatetime(
                    patient.getID(), 'Confirmatory HIV test date'
                )
                return date ? HisDate.toStandardHisDisplayFormat(date) : ''
            }
        },
        {
            label: "HIV test place",
            value: "...",
            asyncValue: () => ObservationService.getFirstValueText(
                patient.getID(), "Confirmatory HIV test location"
            )
        },
        {
            label: "Date of starting first line ART",
            value: '...',
            asyncValue: async () => {
                const date = await ObservationService.getFirstValueDatetime(
                    patient.getID(), 'Date ART started'
                )
                return date ? HisDate.toStandardHisDisplayFormat(date) : ''
            }
        },
        {
            label: "Pulmonary TB within the last 2 years",
            value: '...',
            init: async () => {
                tbStats.value = (await ObservationService.getAllValueCoded(
                    patient.getID(), "Who stages criteria present"
                )) || []
            },
            staticValue: () => hasTbStat('Tuberculosis (PTB or EPTB) within the last 2 years')
        },
        {
            label: "Extra pulmonary TB (EPTB)",
            value: '...',
            staticValue: () => hasTbStat('Extrapulmonary tuberculosis (EPTB)')
        },
        {
            label: "Pulmonary TB (current)",
            value: "...",
            staticValue: () => hasTbStat('Pulmonary tuberculosis (current)')
        },
        {
            label: "Kaposis sarcoma",
            value: '...',
            staticValue: () => hasTbStat('Kaposis sarcoma')
        }
    ]
}

async function loadCardData() {
    for (const item of cardContent.value) {
        if (typeof item.init === 'function') {
            await item.init()
        }

        if (typeof item.condition === 'function') {
            item.visible = item.condition()
        } else {
            item.visible = true
        }

        if (typeof item.asyncValue === 'function') {
            item.asyncValue(item).then((value: any) => item.value = value || '')
        } else if (typeof item.staticValue === 'function') {
            item.value = item.staticValue()
        }
    }
}

function loadVisitDates() {
    isTableInit.value = false
    isTableLoading.value = true
    return PatientService.getPatientVisits(patient.getID(), true)
        .then((dates: string[]) => {
            patientVisitRows.value = dates.map((date) => {
                return [
                    { val: Date.toStandardHisDisplayFormat(date), actualDate: date },
                    { val: '...' },
                    { val: '...' },
                    { val: '...' },
                    { val: '...' },
                    { val: '...' },
                    { val: '...' },
                    {
                        btn: {
                            label: "More"
                        }
                    }
                ]
            })
        }).finally(() => {
            isTableInit.value = true
            isTableLoading.value = false
        })
}

function setModalDetails(data: Record<string, any>) {
    const fmtTurple = (turple: Array<[string, number]>) => turple.map(([t, v]: any) => `${t} (${v})`).join('/')
    modalDetails.value = [
        {
            label: 'Outcome',
            value: data.outcome
        },
        {
            label: 'Outcome Date',
            value: data.outcome_date
        },
        {
            label: 'Side effects',
            value: data.side_effects
        },
        {
            label: 'Viral load',
            value: data.viral_load
        },
        {
            label: 'Pills Brought',
            value: fmtTurple(data.pills_brought)
        },
        {
            label: 'Pills dispensed',
            value: fmtTurple(data.pills_dispensed)
        },
        {
            label: 'Visit by',
            value: data.visit_by
        },
        {
            label: "Regimen",
            value: data.regimen
        },
        {
            label: 'Adherence',
            value: fmtTurple(data.adherence)
        },
        {
            label: 'TB Status',
            value: data.tb_status
        },
        {
            label: 'Height (cm)',
            value: data.height
        },
        {
            label: 'Weight (Kg)',
            value: data.weight
        },
        {
            label: 'BMI',
            value: data.bmi
        },
        {
            label: "Is pregnant",
            value: data.isPregnant,
            visible: patient.isFemale()
        },
        {
            label: "Is breastfeeding",
            value: data.isBreastfeeding,
            visible: patient.isFemale()
        }
    ]
}

async function writeDataToVisitDates() {
    isTableLoading.value = true
    for (let i = 0; i < patientVisitRows.value.length; ++i) {
        const r = patientVisitRows.value[i]
        const date: string = r[0].actualDate
        const patientId = patient.getID()
        const data = await ProgramService.getCurrentProgramInformation(patientId, date)
        const drugs = await ProgramService.getMastercardDrugInformation(patientId, date)
        const pillsDispensed = (drugs?.pills_given || []).map((d: any) => {
            return `${d['short_name'] || d['name']} <b>(${d.quantity || '?'})</b>`
        }).join('<br/>')

        let isPregnant = 'N/A'
        let isBreastfeeding = 'N/A'

        if (patient.isFemale()) {
            const pregObs = await ObservationService.getFirstObs(patientId, 'Is patient pregnant', date)
            if (pregObs && Date.toStandardHisDisplayFormat(pregObs.obs_datetime) === date) {
                isPregnant = pregObs.value_coded
            }
            const bfeed = await ObservationService.getFirstObs(patientId, 'Is patient breast feeding', date)
            if (bfeed && Date.toStandardHisDisplayFormat(bfeed.obs_datetime) === date) {
                isBreastfeeding = bfeed.value_coded
            }
        }
        r[1].val = data.weight
        r[2].val = data.regimen
        r[3].val = data.viral_load
        r[4].val = data.tb_status
        r[5].val = data.outcome.match(/Unk/i) ? "Unknown" : data.outcome
        r[6].val = pillsDispensed
        r[7].btn = {
            label: "More",
            onClick: () => openModal(data)
        }
    }
    isTableLoading.value = true
}

function openModal(row: any) {
    setModalDetails(row);
    isModalVisible.value = true;
}

function closeModal() {
    isModalVisible.value = false;
}

onMounted(() => {
    setPatientCards()
    loadCardData()
    loadVisitDates().then(() => writeDataToVisitDates()).finally(() => isTableLoading.value = false)
})
</script>
<style scoped>
.styled-table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    font-size: 16px;
    text-align: left;
}

.styled-table th {
    border: 1px solid black;
    padding: 10px;
    background-color: #006400;
    /* Green color */
    color: white;
    /* White text for contrast */
    font-weight: bold;
}

.styled-table td {
    border: 1px solid black;
    padding: 10px;
}

.styled-table tr:nth-child(even) {
    background-color: #f9f9f9;
}

.styled-table tr:hover {
    background-color: #f1f1f1;
}

.modal-content {
    padding: 20px;
    text-align: left;
}

.modal-content h3 {
    margin-bottom: 20px;
}

.modal-content ul {
    list-style-type: none;
    padding: 0;
}

.modal-content ul li {
    margin-bottom: 10px;
}
</style>