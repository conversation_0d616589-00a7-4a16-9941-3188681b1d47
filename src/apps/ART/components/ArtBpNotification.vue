<template>
    <div class="art-bp-notification" v-if="!showRiskFactorForm && !showBpCaptureForm">
        <h1 class="ion-text-center">
            HTN Alert
        </h1>
        <div v-if="hasPressureReading" class="vertically-align ion-text-center">
            <h2 v-show="patientOnBpDrugs" style="font-weight:bold;">
                (Patient already on BP drugs)
            </h2>
            <h2>
                <span class="name"> {{ patientName }} </span> has <span v-show="highBP"> a high </span> blood
                pressure of
                <span class="bp">
                    {{ sysBp }} / {{ dsBP }}
                </span>
                <br />
                <span>
                    Retesting BP is <span
                        style="font-weight: bold; color: #000000;text-decoration: underline;">optional</span>. <br>
                    Do you want to test BP?
                </span>
            </h2>
        </div>
        <div v-if="!hasPressureReading" class="vertically-align ion-text-center">
            No BP Reading found
            <br />
            <h1>
                Do you want to test BP?
            </h1>
        </div>
        <div class="footer-buttons ion-text-center">
            <ion-button @click="openRiskFactorForm" :disabled="noButtonDisabled" size="large" color="danger">
                No
            </ion-button>
            <ion-button @click="recaptureBp" size="large" color="success" :disabled="recaptureBpDisabled">
                Yes
            </ion-button>
        </div>
    </div>

    <div v-if="showRiskFactorForm" class="risk-factor-form">
        <h2>Risk Factors Assessment</h2>
        <BasicMultiSelect :label="formModel.riskFactors.label" v-model="formData.riskFactors"
            @on-change="() => formModel.riskFactors.onFormUpdate()" :error-message="validationMessages.riskFactors"
            :disabled="formModel.riskFactors.isSaving.value" />
        <div class="footer-buttons ion-text-center">
            <ion-button @click="formModel.riskFactors.save" :disabled="formModel.riskFactors.isSaving.value"
                color="primary">
                Save
            </ion-button>
            <ion-button @click="openBpManagement" fill="outline">
                BP management
            </ion-button>
            <ion-button @click="showRiskFactorForm = false" :disabled="formModel.riskFactors.isSaving.value"
                fill="outline" color="primary">
                Cancel
            </ion-button>
        </div>
    </div>

    <div v-if="showBpCaptureForm" class="bp-capture-form">
        <h2>BP Capture Form</h2>
        <BasicInput label="Systolic BP" v-model="formData.bp.systolic"
            :error-message="validationMessages.bp.systolic" />
        <BasicInput label="Diastolic BP" v-model="formData.bp.diastolic"
            :error-message="validationMessages.bp.diastolic" />
        <div class="footer-buttons ion-text-center">
            <ion-button @click="formModel.bp.save" color="primary">Save</ion-button>
            <ion-button @click="cancelBpCapture" fill="outline" color="primary">Cancel</ion-button>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import {
    IonButton,
    loadingController
} from "@ionic/vue";
import ART_PROP from "@/apps/ART/art_global_props";
import { BPManagementService } from "@/apps/ART/services/htn_service";
import { ConceptService } from "@/services/concept_service";
import { ObservationService } from "@/services/observation_service";
import { useDemographicsStore } from '@/stores/DemographicStore';
import { storeToRefs } from 'pinia';
import { PatientService } from '@/services/patient_service';
import { toastSuccess, toastDanger, toastWarning } from '@/utils/Alerts';
import BasicMultiSelect from '@/components/Forms/BasicFormFields/BasicMultiSelect.vue';
import BasicInput from '@/components/Forms/BasicFormFields/BasicInput.vue';
import { saveEncounterData, EncounterTypeId } from '@/services/encounter_type';
import { modal } from '@/utils/modal';
import BPManagement from './BPManagement.vue';

const demographicsStore = useDemographicsStore();
const { patient } = storeToRefs(demographicsStore) as any;
const patientService = new PatientService();

const sysBp = ref(0);
const dsBP = ref(0);
const patientOnBpDrugs = ref(false);
const isPregnant = ref(false);
const systolicThreshold = ref(145);
const diastolicTheshold = ref(94);
const showRiskFactorForm = ref(false);
const noButtonDisabled = ref(false);
const showBpCaptureForm = ref(false);
const recaptureBpDisabled = ref(false); // Add this state to manage the 'Yes' button's disabled state

const patientId = patient.value.patientID;
const providerId = -1; // Replace with actual provider ID if available

const bpManagementService = new BPManagementService(patientId, providerId);

const formData = ref<any>({
    riskFactors: [],
    bp: {
        systolic: '',
        diastolic: ''
    }
});

const validationMessages = ref({
    riskFactors: '',
    bp: {
        systolic: '',
        diastolic: ''
    }
});

const formModel = {
    riskFactors: {
        label: "Risk Factors",
        isSaving: ref(false),
        onFormUpdate: () => validationMessages.value.riskFactors = '',
        init: async () => {
            const concepts = ConceptService.getConceptsByCategory("risk factors");
            const risks = concepts.map(async (concept) => {
                const val = await ObservationService.getFirstValueCoded(patientId, concept.name);
                return {
                    label: concept.name,
                    value: concept.concept_id,
                    checked: `${val}` === 'Yes'
                };
            });
            formData.value.riskFactors = await Promise.all(risks);
        },
        save: async () => {
            formModel.riskFactors.isSaving.value = true;
            validationMessages.value.riskFactors = '';
            try {
                if (formData.value.riskFactors.length === 0) {
                    const message = 'Please select at least one risk factor';
                    validationMessages.value.riskFactors = message;
                    toastDanger(message);
                    return;
                }
                const history = new BPManagementService(patientId, providerId);
                const encounter = await history.createEncounter();
                if (encounter) {
                    const obs = formData.value.riskFactors.map(async (r: any) => {
                        const val = r.checked ? 'Yes' : 'No';
                        return {
                            concept_id: r.value,
                            value_coded: await ConceptService.getConceptID(val)
                        };
                    });
                    await history.saveObservationList(await Promise.all(obs));
                }
                toastSuccess("Risk assessment has been saved successfully");
                noButtonDisabled.value = true;
                showRiskFactorForm.value = false;
            } catch (error) {
                console.error('Error saving risk factors:', error);
                toastDanger('Failed to save risk factors');
            } finally {
                formModel.riskFactors.isSaving.value = false;
            }
        }
    },
    bp: {
        save: async () => {
            validationMessages.value.bp.systolic = '';
            validationMessages.value.bp.diastolic = '';

            const systolic = parseInt(formData.value.bp.systolic, 10);
            const diastolic = parseInt(formData.value.bp.diastolic, 10);

            if (!systolic || systolic < 90 || systolic > 200) {
                validationMessages.value.bp.systolic = 'Systolic BP must be between 90 and 200';
                toastDanger(validationMessages.value.bp.systolic);
                return;
            }

            if (!diastolic || diastolic < 60 || diastolic > 120) {
                validationMessages.value.bp.diastolic = 'Diastolic BP must be between 60 and 120';
                toastDanger(validationMessages.value.bp.diastolic);
                return;
            }

            try {
                const processedBpData = [
                    await ObservationService.buildValueNumber('Systolic', systolic),
                    await ObservationService.buildValueNumber('Diastolic', diastolic)
                ];

                const patient = new PatientService().getObj();
                await saveEncounterData(patient.patientID, EncounterTypeId.VITALS, processedBpData);

                toastSuccess('BP data saved successfully.');
                showBpCaptureForm.value = false;
                recaptureBpDisabled.value = true; // Disable the 'Yes' button after saving vitals
            } catch (error) {
                console.error('Error saving BP data:', error);
                toastDanger('Failed to save BP data.');
            }
        }
    }
};

const openBpManagement = () => {
    modal.show(BPManagement)
}

const openRiskFactorForm = () => {
    showRiskFactorForm.value = true;
    formModel.riskFactors.init();
};

const recaptureBp = () => {
    showBpCaptureForm.value = true;
    console.log('BP capture form displayed, ART BP notification hidden');
};

const cancelBpCapture = () => {
    showBpCaptureForm.value = false;
    formData.value.bp.systolic = '';
    formData.value.bp.diastolic = '';
    console.log('BP capture form canceled');
};

const patientName = computed(() => patientService.getFullName());

const hasPressureReading = computed(() => sysBp.value > 0 && dsBP.value > 0);

const highBP = computed(() => {
    return (sysBp.value >= systolicThreshold.value
        && dsBP.value >= diastolicTheshold.value || dsBP.value >= diastolicTheshold.value)
        && !isPregnant.value;
});

const onSubmit = async () => {
    if (!recaptureBpDisabled.value && !noButtonDisabled.value) {
        toastWarning("Please perfom and action on this page before proceeding")
        return false
    }
    return true
}
onMounted(async () => {
    const loading = await loadingController.create({
        message: 'Verifying Blood Pressure...',
        backdropDismiss: false
    });
    await loading.present();

    formModel.riskFactors.init();
    systolicThreshold.value = (await ART_PROP.systolicThreshold()) || 145;
    diastolicTheshold.value = (await ART_PROP.diastolicThreshold()) || 94;
    dsBP.value = (await bpManagementService.getDiastolicBp()) || 0;
    sysBp.value = (await bpManagementService.getSystolicBp()) || 0;
    patientOnBpDrugs.value = (await bpManagementService.onBpDrugs()) || false;
    isPregnant.value = patientService.isChildBearing()
        ? (await patientService.isPregnant()) || false
        : false;

    loadingController.dismiss();
});

defineExpose({
    onSubmit
})
</script>

<style scoped>
.art-bp-notification {
    padding: 20px;
    margin-top: 10%;
}

.footer-buttons {
    margin-top: 30px;
    display: flex;
    justify-content: center;
    gap: 10px;
}

.risk-factor-form {
    padding: 20px;
    margin-top: 4%;
}

.bp {
    color: red;
    font-style: italic;
}

.name {
    color: blue;
    font-style: italic;
}

.green {
    width: 170px;
}

.bp-capture-form {
    padding: 20px;
    margin-top: 4%;
}

.form-group {
    margin-bottom: 15px;
}

label {
    display: block;
    margin-bottom: 5px;
}

input {
    width: 100%;
    padding: 8px;
    box-sizing: border-box;
}
</style>