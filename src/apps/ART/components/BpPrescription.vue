<template>
    <ion-card>
        <ion-card-content>
            <ion-grid v-if="!useCustomDrugs">
                <ion-row>
                    <ion-col size="6" v-for="(option, drugName) in defaultHtnDrugs" :key="drugName">
                        <ion-card style="min-height: 25vh;">
                            <ion-card-content>
                                <BasicMultiSelect single-select v-model="option.drugs" :label="drugName" />
                                <ion-item color="light" lines="none">
                                    <ion-checkbox slot="end" v-model="option.includeNote" />
                                    <ion-label slot="end" style="padding: 10px;">Add note</ion-label>
                                </ion-item>
                                <BasicNote required v-if="option.includeNote" v-model="option.notes"
                                    :error-message="!option.notes.length ? 'Dont forget to add a note' : ''"
                                    :label="`Additional Notes for ${drugName}`" />
                            </ion-card-content>
                        </ion-card>
                    </ion-col>
                </ion-row>
            </ion-grid>
            <ion-item lines="none">
                <ion-checkbox disabled v-model="useCustomDrugs" />
                <ion-label style="padding: 10px;">More drugs</ion-label>
            </ion-item>
            <ion-grid v-if="useCustomDrugs">
                <ion-row>
                    <ion-col>
                        COming soon
                    </ion-col>
                </ion-row>
            </ion-grid>
            <ion-button :disabled="recordSaved || !canSaveDrugs" @click="onFinish" expand="full"
                class="ion-padding">Save</ion-button>
        </ion-card-content>
    </ion-card>
</template>
<script lang="ts" setup>
import {
    IonButton,
    IonCard,
    IonCardContent,
    IonCheckbox,
    IonGrid,
    IonRow,
    IonCol,
    IonItem,
    IonLabel
} from "@ionic/vue"
import BasicMultiSelect from "@/components/Forms/BasicFormFields/BasicMultiSelect.vue";
import BasicNote from "@/components/Forms/BasicFormFields/BasicNote.vue";
import { BPManagementService } from "../services/htn_service";
import { useDemographicsStore } from "@/stores/DemographicStore";
import { storeToRefs } from "pinia";
import { computed, onMounted, ref } from "vue";
import { toastSuccess } from "@/utils/Alerts";

const demographicsStore = useDemographicsStore();
const { patient } = storeToRefs(demographicsStore) as any;

const patientId = patient.value.patientID
const bpManagementService = new BPManagementService(patientId, -1)
const defaultHtnDrugs = ref({} as Record<string, any>)
const useCustomDrugs = ref(false)
const canSaveDrugs = computed(() => buildPayload().selectedDrugs.length > 0)
const recordSaved = ref(false)

const emit = defineEmits(['newPrescription'])

const buildPayload = () => {
    return Object.keys(defaultHtnDrugs.value).reduce((a: any, k: string) => {
        const data = defaultHtnDrugs.value[k]
        const selectedDrug = data.drugs.filter((d: any) => d.checked)
        if (!selectedDrug.length) return a
        if (data.includeNote && data.notes && data.isNewNote) {
            a.notes.push(bpManagementService.buildObs('Clinician notes', {
                'value_drug': selectedDrug[0].drug.drug_id,
                'value_text': data.notes
            }))
        }
        a.selectedDrugs.push(selectedDrug[0].drug)
        return a
    }, { selectedDrugs: [], notes: [] })
}

const onFinish = async () => {
    const payload = buildPayload()
    if (payload.notes.length) {
        const obs = await Promise.all(payload.notes)
        await bpManagementService.createEncounter()
        await bpManagementService.saveObservationList(obs)
    }
    sessionStorage.setItem('BP_DRUGS_PRESCRIBED', JSON.stringify({
        [patientId]: payload.selectedDrugs
    }))
    emit('newPrescription', payload.selectedDrugs)
    toastSuccess('Prescription saved!')
    recordSaved.value = true
}

onMounted(() => {
    defaultHtnDrugs.value = (() => {
        const htnDrugs: any = bpManagementService.getDrugs();
        const drugRef = BPManagementService.htnDrugReferences()
        return Object.keys(htnDrugs).reduce((acc, drugName) => {
            acc[drugName] = {
                drugs: htnDrugs[drugName].drugs.map((drug: any) => ({
                    label: drug.amount,
                    value: drug.drugID,
                    checked: false,
                    drug: drugRef.find((d: any) => d.drug_id === drug.drugID)
                })),
                notes: "",
                includeNote: false,
                isNewNote: true
            }
            return acc
        }, {} as Record<string, any>)
    })()
})
</script>
