<template>
    <div class="dashboard-container">
        <ion-segment v-model="selectedSegment">
            <ion-segment-button value="dashboard">
                Overview
            </ion-segment-button>
            <ion-segment-button value="viralLoadAlerts">
                <div class="segment-content">
                    VL Alerts
                    <ion-badge color="primary" v-if="notificationCount > 0">{{ notificationCount }}</ion-badge>
                </div>
            </ion-segment-button>
        </ion-segment>

        <div v-if="selectedSegment === 'dashboard'">
            <ARTDashboard></ARTDashboard>
        </div>

        <div v-if="selectedSegment === 'viralLoadAlerts'">
            <ARTVlAlert></ARTVlAlert>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { IonSegment, IonSegmentButton, IonBadge } from '@ionic/vue';
import ARTDashboard from './ARTDashboard.vue';
import ARTVlAlert from './ARTVlAlert.vue';
import { Notification } from '@/apps/ART/composables/notifications';

// Reactive state to track the selected segment
const selectedSegment = ref('dashboard');

// Get notification functionality
const { notificationCount, loadNotifications } = Notification();

// Load notifications on mount
onMounted(() => {
    loadNotifications();
});
</script>
<style scoped>
.dashboard-container {
    margin-top: 40px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: auto;
    width: 75%;
    font-family: Arial, sans-serif;
    display: block;
    height: auto;
}

.segment-content {
    display: flex;
    align-items: center;
    gap: 8px;
}
</style>