<template>
    <div class="filing-number-trail">
        <!-- Navigation Bar -->
        <div class="status-bar">
            <div class="status-info">
                <div class="filing-search">
                    <span class="label">Specify Filing#:</span>
                    <ion-input style="height: 1vh;" fill="outline" type="text"
                        @ion-change="(e: any) => specifyFilingNumber(e.detail.value)"
                        placeholder="Enter filing number..." />
                </div>
            </div>
            <div class="navigation-buttons">
                <button class="nav-btn prev-btn" @click="goToPrevPage" :disabled="currentPage === 0 || isLoading">
                    Prev
                </button>
                <div class="page-info">
                    <span class="label">Page:</span>
                    <span class="value">{{ currentPage + 1 }}</span>
                </div>
                <button class="nav-btn next-btn" @click="goToNextPage" :disabled="isLoading">
                    Next
                </button>
            </div>
        </div>

        <div class="table-container">
            <table class="filing-trail-table">
                <thead>
                    <tr>
                        <th>Filing #</th>
                        <th>Given name</th>
                        <th>Family name</th>
                        <th>Outcome</th>
                        <th>LAD</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="record in filingRecords" :key="record.identifier">
                        <td>{{ record.identifier }}</td>
                        <td>{{ record.given_name }}</td>
                        <td>{{ record.family_name }}</td>
                        <td>{{ formatOutcome(record.state) }}</td>
                        <td>{{ formatDate(record.appointment_date) }}</td>
                        <td>
                            <ion-button @click="onSelectFn(record)">Use</ion-button>
                        </td>
                    </tr>
                </tbody>
            </table>

            <!-- Empty state -->
            <div v-if="filingRecords.length === 0" class="empty-state">
                <p>No filing records found</p>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { IonButton, IonInput } from "@ionic/vue"
import { ref, onMounted, PropType } from 'vue'
import { FilingNumberService } from '@/apps/ART/services/filing_number_service'
import { PatientService } from '@/services/patient_service'
import HisDate from '@/utils/Date'
import { alertConfirmation, toastDanger, toastSuccess, toastWarning } from "@/utils/Alerts"
import { BadRequestError } from "@/services/service"

interface FilingRecord {
    patient_id: number;
    identifier: string;
    given_name: string;
    family_name: string;
    state: string;
    appointment_date: string;
}

const props = defineProps({
    onArchive: {
        type: Object as PropType<(record: FilingRecord) => void>,
    }
})

const emit = defineEmits(['onArchive'])

const patientService = new PatientService()
const filingNumbeService = new FilingNumberService()
filingNumbeService.setPatientID(patientService.getID())

const filingRecords = ref<FilingRecord[]>([])
const currentPage = ref<number>(0)
const isLoading = ref<boolean>(false)

// Sample data - replace with actual data fetching logic
const loadFilingRecords = async (pageNumber = 0) => {
    try {
        isLoading.value = true
        currentPage.value = pageNumber
        filingRecords.value = await filingNumbeService.getArchivingCandidates(pageNumber) // Fetch data from the service
    } catch (error) {
        toastWarning("Unable to load Filing number")
        console.error('Error loading filing records:', error)
    } finally {
        isLoading.value = false
    }
}

const goToPrevPage = async () => {
    if (currentPage.value > 0) {
        await loadFilingRecords(currentPage.value - 1)
    }
}

const goToNextPage = async () => {
    await loadFilingRecords(currentPage.value + 1)
}

const specifyFilingNumber = async (filingNumber: string) => {
    if (!filingNumber) {
        loadFilingRecords(0)
        return
    }
    const res = filingRecords.value.filter((f) => f.identifier.toLowerCase() === filingNumber.toLowerCase().trim())
    if (res.length) {
        filingRecords.value = res
        return
    }
    filingRecords.value = await filingNumbeService.getFilingNumber(filingNumber)
}

const formatOutcome = (outcome: string) => outcome.match(/trans/i)
    ? 'TO'
    : outcome.match(/stop/i)
        ? 'Tx stoppeFid'
        : outcome

const formatDate = (date: string) => HisDate.toStandardHisDisplayFormat(date)

const onSelectFn = async (candidate: FilingRecord) => {
    if (!(await alertConfirmation(`Are you sure you want to archive the filing number ${candidate.identifier}? This action cannot be undone.`))) {
        return
    }
    try {
        const archived = await filingNumbeService.archivePatient(
            Number(candidate.patient_id),
            candidate.identifier
        )
        if (typeof props.onArchive === 'function') {
            emit('onArchive', archived, candidate)
        }
        if (archived) {
            filingRecords.value = []
            toastSuccess("Filing number archived successfully, You can now reassign it")
        }
    } catch (e) {
        if (e instanceof BadRequestError) {
            toastDanger(e.errors)
        } else {
            toastDanger(`${e}`)
        }
    }
}

onMounted(() => {
    loadFilingRecords()
})
</script>

<style scoped>
.filing-number-trail {
    padding: 1rem;
}

.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f8f9fa;
    color: #495057;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
}

.status-info {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.current-filing,
.current-status {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.filing-search,
.page-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.filing-input {
    padding: 0.375rem 0.5rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 0.875rem;
    min-width: 200px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.filing-input:focus {
    outline: 0;
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.filing-input::placeholder {
    color: #6c757d;
}

.navigation-buttons {
    display: flex;
    gap: 0.5rem;
}

.nav-btn {
    background-color: #007bff;
    border: 1px solid #007bff;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    min-width: 60px;
}

.nav-btn:hover:not(:disabled) {
    background-color: #0056b3;
    border-color: #0056b3;
}

.nav-btn:disabled {
    background-color: #6c757d;
    border-color: #6c757d;
    cursor: not-allowed;
    opacity: 0.65;
}

.label {
    font-size: 0.875rem;
    color: #6c757d;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.value {
    font-size: 1rem;
    font-weight: 600;
    color: #495057;
}

.status-indicator {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-indicator.active {
    background-color: #28a745;
    color: white;
}

.status-indicator.dormant {
    background-color: #dc3545;
    color: white;
}

.status-indicator.none {
    background-color: #6c757d;
    color: white;
}

.get-filing-btn {
    background-color: #007bff;
    border: 1px solid #007bff;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
}

.get-filing-btn:hover:not(:disabled) {
    background-color: #0056b3;
    border-color: #0056b3;
}

.get-filing-btn:disabled {
    background-color: #6c757d;
    border-color: #6c757d;
    cursor: not-allowed;
}

.table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.filing-trail-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.filing-trail-table thead {
    background-color: #f8f9fa;
}

.filing-trail-table th,
.filing-trail-table td {
    padding: 0.75rem 1rem;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.filing-trail-table th {
    font-weight: 600;
    color: #495057;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.filing-trail-table tbody tr:hover {
    background-color: #f8f9fa;
}

.filing-trail-table tbody tr:last-child td {
    border-bottom: none;
}

.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.status-badge.active {
    background-color: #d4edda;
    color: #155724;
}

.status-badge.voided {
    background-color: #f8d7da;
    color: #721c24;
}

.status-badge.pending {
    background-color: #fff3cd;
    color: #856404;
}

.empty-state {
    padding: 2rem;
    text-align: center;
    color: #6c757d;
}

.empty-state p {
    margin: 0;
    font-style: italic;
}

/* Responsive design */
@media (max-width: 768px) {
    .status-bar {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .status-info {
        flex-direction: column;
        gap: 1rem;
        width: 100%;
    }

    .filing-input {
        min-width: 100%;
    }

    .navigation-buttons {
        justify-content: center;
    }

    .filing-trail-table {
        font-size: 0.8rem;
    }

    .filing-trail-table th,
    .filing-trail-table td {
        padding: 0.5rem;
    }
}

@media (max-width: 576px) {
    .status-info {
        gap: 0.75rem;
    }

    .filing-search,
    .page-info {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        padding: 0.5rem;
        background: rgba(0, 0, 0, 0.05);
        border-radius: 4px;
    }

    .filing-input {
        min-width: 150px;
        flex: 1;
        margin-left: 0.5rem;
    }

    .navigation-buttons {
        width: 100%;
        justify-content: space-between;
    }

    .nav-btn {
        flex: 1;
        max-width: 120px;
    }

    .table-container {
        overflow-x: auto;
    }

    .filing-trail-table {
        min-width: 500px;
    }
}
</style>