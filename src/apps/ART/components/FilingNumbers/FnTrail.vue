<template>
    <div class="filing-number-trail">
        <!-- Status Bar -->
        <div class="status-bar">
            <div class="status-info">
                <div class="current-filing">
                    <span class="label">Current Filing #:</span>
                    <span class="value">{{ currentFilingNumber || 'None' }}</span>
                </div>
                <div class="current-status">
                    <span class="label">Status:</span>
                    <span :class="['status-indicator', currentStatus.toLowerCase()]">
                        {{ currentStatus }}
                    </span>
                </div>
            </div>
            <button v-if="currentStatus === 'Dormant'" class="get-filing-btn" @click="getNewFilingNumber"
                :disabled="isGettingFiling">
                <span v-if="isGettingFiling">Getting...</span>
                <span v-else>Get Filing #</span>
            </button>
        </div>

        <div class="table-container">
            <table class="filing-trail-table">
                <thead>
                    <tr>
                        <th>Status</th>
                        <th>Filing #</th>
                        <th>Date Created</th>
                        <th>Date Voided</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="record in filingRecords" :key="record.id">
                        <td>
                            <span :class="['status-badge', record.status.toLowerCase()]">
                                {{ record.status }}
                            </span>
                        </td>
                        <td>{{ record.filingNumber }}</td>
                        <td>{{ record.dateCreated }}</td>
                        <td>{{ record.dateVoided }}</td>
                    </tr>
                </tbody>
            </table>

            <!-- Empty state -->
            <div v-if="filingRecords.length === 0" class="empty-state">
                <p>No filing records found</p>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick } from 'vue'
import { FilingNumberService } from '@/apps/ART/services/filing_number_service'
import { PatientService } from '@/services/patient_service'
import HisDate from '@/utils/Date'
import { createModal, toastDanger } from '@/utils/Alerts'
import FnAssign from './FnAssign.vue'
import ArtFormWrapper from '../ArtFormWrapper.vue'
import { modalController } from '@ionic/vue'

const patientService = new PatientService()
const filingNumbeService = new FilingNumberService()
const patientID = patientService.getID()
filingNumbeService.setPatientID(patientID)

interface FilingRecord {
    id: string
    status: 'Active' | 'Voided'
    filingNumber: string
    dateCreated: Date
    dateVoided?: Date
}

const filingRecords = ref<FilingRecord[]>([])
const currentFilingNumber = ref<string>('')
const currentStatus = ref<'Active' | 'Dormant' | 'N/A'>('N/A')
const isGettingFiling = ref<boolean>(false)

// Sample data - replace with actual data fetching logic
const loadFilingRecords = async () => {
    filingRecords.value = []
    const data = await filingNumbeService.getPastFilingNumbers() // Fetch data from the service
    filingRecords.value = data.map((record: any) => {
        const isActive = record.voided === 0
        return {
            id: record.identifier,
            status: isActive ? 'Active' : 'Voided',
            filingNumber: filingNumbeService.formatNumber(record.identifier),
            dateCreated: HisDate.toStandardHisDisplayFormat(record.date_created),
            dateVoided: !isActive ? HisDate.toStandardHisDisplayFormat(record.date_voided) : 'N/A',
        }
    })
}

const initPatientStatus = async () => {
    isGettingFiling.value = true
    try {
        patientService.patient = await PatientService.findByID(patientID)
        currentFilingNumber.value = patientService.getFilingNumber()
        currentStatus.value = filingNumbeService.isActiveFilingNum(currentFilingNumber.value)
            ? 'Active'
            : filingNumbeService.isDormantFilingNum(currentFilingNumber.value)
                ? 'Dormant'
                : 'N/A'
        nextTick(() => {
        })
    } catch (e) {
        toastDanger(`${e}`)
    } finally {
        isGettingFiling.value = false
    }
}

const getNewFilingNumber = () => {
    createModal(ArtFormWrapper, {}, false, {
        title: "Assign filing number",
        useComponent: FnAssign,
        onClose: () => {
            init()
            modalController.dismiss()
        }
    });
}

const init = () => filingNumbeService.loadFilingPrefix().then(() => initPatientStatus().then(loadFilingRecords))

onMounted(() => {
    init()
})
</script>

<style scoped>
.filing-number-trail {
    padding: 1rem;
}

.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f8f9fa;
    color: #495057;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
}

.status-info {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.current-filing,
.current-status {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.label {
    font-size: 0.875rem;
    color: #6c757d;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.value {
    font-size: 1rem;
    font-weight: 600;
    color: #495057;
}

.status-indicator {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-indicator.active {
    background-color: #28a745;
    color: white;
}

.status-indicator.dormant {
    background-color: #dc3545;
    color: white;
}

.status-indicator.none {
    background-color: #6c757d;
    color: white;
}

.get-filing-btn {
    background-color: #007bff;
    border: 1px solid #007bff;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
}

.get-filing-btn:hover:not(:disabled) {
    background-color: #0056b3;
    border-color: #0056b3;
}

.get-filing-btn:disabled {
    background-color: #6c757d;
    border-color: #6c757d;
    cursor: not-allowed;
}

.table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.filing-trail-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.filing-trail-table thead {
    background-color: #f8f9fa;
}

.filing-trail-table th,
.filing-trail-table td {
    padding: 0.75rem 1rem;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.filing-trail-table th {
    font-weight: 600;
    color: #495057;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.filing-trail-table tbody tr:hover {
    background-color: #f8f9fa;
}

.filing-trail-table tbody tr:last-child td {
    border-bottom: none;
}

.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.status-badge.active {
    background-color: #d4edda;
    color: #155724;
}

.status-badge.voided {
    background-color: #f8d7da;
    color: #721c24;
}

.status-badge.pending {
    background-color: #fff3cd;
    color: #856404;
}

.empty-state {
    padding: 2rem;
    text-align: center;
    color: #6c757d;
}

.empty-state p {
    margin: 0;
    font-style: italic;
}

/* Responsive design */
@media (max-width: 768px) {
    .status-bar {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .status-info {
        flex-direction: column;
        gap: 1rem;
        width: 100%;
    }

    .filing-trail-table {
        font-size: 0.8rem;
    }

    .filing-trail-table th,
    .filing-trail-table td {
        padding: 0.5rem;
    }
}

@media (max-width: 576px) {
    .status-info {
        gap: 0.75rem;
    }

    .current-filing,
    .current-status {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        padding: 0.5rem;
        background: rgba(0, 0, 0, 0.05);
        border-radius: 4px;
    }

    .table-container {
        overflow-x: auto;
    }

    .filing-trail-table {
        min-width: 500px;
    }
}
</style>