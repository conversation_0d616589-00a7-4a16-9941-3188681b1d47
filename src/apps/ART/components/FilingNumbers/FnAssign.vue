<template>
    <div>
        <FnViewer v-if="useComponent === 'FnViewer'" :filing-items="filingItems" />
        <FnArchiver v-if="useComponent === 'FnArchiver'" @on-archive="onArchive" />
    </div>
</template>
<script lang="ts" setup>
import { onMounted, PropType, ref } from 'vue';
import FnViewer from './FnViewer.vue';
import FnArchiver from './FnArchiver.vue';
import { FilingNumberService } from '../../services/filing_number_service';
import { PatientService } from '@/services/patient_service';
import { isEmpty } from 'lodash';
import { toastWarning } from '@/utils/Alerts';
import { printArtFilingNumberLbl } from '../../Labels';

const useComponent = ref<'FnViewer' | 'FnArchiver' | null>(null)

const filingItems = ref<any>([
    {
        statusChange: 'Dormant → Active',
        name: 'N/A',
        newNumber: 'N/A',
        oldNumber: 'N/A'
    },
    {
        statusChange: 'Active → Dormant',
        name: 'N/A',
        newNumber: 'N/A',
        oldNumber: 'N/A'
    }
])

const filingService = new FilingNumberService()
const activePatient = new PatientService()

function onArchive(archieved: any, candidate: any) {
    filingItems.value = [
        {
            statusChange: 'Dormant → Active',
            name: activePatient.getFullName(),
            newNumber: archieved.active_number,
            oldNumber: activePatient.getFilingNumber()
        },
        {
            statusChange: 'Active → Dormant',
            name: `${candidate.given_name} ${candidate.family_name}`,
            newNumber: archieved.dormant_number,
            oldNumber: archieved.active_number
        }
    ]
    useComponent.value = 'FnViewer'
    printArtFilingNumberLbl(activePatient.getID())
}

onMounted(async () => {
    const patientID = activePatient.getID()
    activePatient.patient = await PatientService.findByID(patientID)

    filingService.setPatientID(patientID)
    const assigned = await filingService.assignFilingNumber()

    if (isEmpty(assigned)) {
        useComponent.value = 'FnArchiver'
        return toastWarning("No filing number to assign")
    } else {
        useComponent.value = 'FnViewer'
    }
    const activeFilingID = activePatient.getFilingNumber()
    filingItems.value[0] = {
        statusChange: 'Dormant → Active',
        name: activePatient.getFullName(),
        newNumber: assigned.new_identifier.identifier,
        oldNumber: filingService.isDormantFilingNum(activeFilingID)
            ? filingService.formatNumber(activeFilingID)
            : 'N/A'
    }

    if (!isEmpty(assigned.archived_identifier)) {
        const archivedPatient = await (async () => {
            const patient = new PatientService()
            patient.patient = await PatientService.findByID(assigned.archived_identifier.patient_id)
            return patient
        })()

        filingItems.value[1] = {
            statusChange: 'Active → Dormant',
            name: archivedPatient.getFullName(),
            newNumber: assigned.archived_identifier.identifier,
            oldNumber: assigned.new_identifier.identifier
        }
    }
    printArtFilingNumberLbl(patientID)
})
</script>