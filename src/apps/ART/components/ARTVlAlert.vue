<template>
    <div class="vl-alerts-container">
        <!-- Filter Segment -->
        <ion-segment mode="ios" v-model="selectedCategory" class="category-segment">
            <ion-segment-button value="high_vl">
                <ion-label>High VL ({{ highVlCount }})</ion-label>
            </ion-segment-button>
            <ion-segment-button value="rejected">
                <ion-label>Rejected ({{ rejectedCount }})</ion-label>
            </ion-segment-button>
            <ion-segment-button value="normal">
                <ion-label>Normal Results ({{ normalCount }})</ion-label>
            </ion-segment-button>
        </ion-segment>

        <!-- Results Table -->
        <div class="table-container">
            <table class="vl-table">
                <thead>
                    <tr>
                        <th>ARV #</th>
                        <th>Accession #</th>
                        <th>Order Date</th>
                        <th v-if="selectedCategory === 'rejected'">Rejection Reason</th>
                        <th>View</th>
                        <th>Clear</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-if="filteredResults.length === 0">
                        <td :colspan="selectedCategory === 'rejected' ? 6 : 5" class="empty-message">
                            {{ getEmptyMessage() }}
                        </td>
                    </tr>
                    <tr v-for="item in filteredResults" :key="item.id">
                        <td>{{ item.arv || '-' }}</td>
                        <td>{{ item.accession || '-' }}</td>
                        <td>{{ item.order_date || '-' }}</td>
                        <td v-if="selectedCategory === 'rejected'">{{ item.rejection_reason || '-' }}</td>
                        <td>
                            <ion-button fill="clear" size="small" @click="viewItem(item)">
                                <ion-icon :icon="eyeOutline" slot="icon-only"></ion-icon>
                            </ion-button>
                        </td>
                        <td>
                            <ion-button fill="clear" size="small" @click="clearItem(item)" color="success">
                                <ion-icon :icon="checkmarkOutline" slot="icon-only"></ion-icon>
                            </ion-button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import {
    IonSegment,
    IonSegmentButton,
    IonLabel,
    IonButton,
    IonIcon
} from '@ionic/vue'
import {
    eyeOutline,
    checkmarkOutline
} from 'ionicons/icons'
import { Notification } from '@/apps/ART/composables/notifications'

// Reactive data
const selectedCategory = ref('high_vl')
const { notificationData, loadNotifications, clearNotification, openNotification } = Notification()

// Computed properties for counts
const highVlCount = computed(() => {
    if (!notificationData.value.length) return 0
    const vlData = notificationData.value[0]?.vlMessageObs?.highVL || []
    return vlData.length
})

const rejectedCount = computed(() => {
    if (!notificationData.value.length) return 0
    const vlData = notificationData.value[0]?.vlMessageObs?.rejectedVL || []
    return vlData.length
})

const normalCount = computed(() => {
    if (!notificationData.value.length) return 0
    const vlData = notificationData.value[0]?.vlMessageObs?.normalVL || []
    return vlData.length
})

// Computed properties for filtered results
const filteredResults = computed(() => {
    if (!notificationData.value.length) return []

    const vlMessageObs = notificationData.value[0]?.vlMessageObs
    if (!vlMessageObs) return []

    switch (selectedCategory.value) {
        case 'high_vl':
            return vlMessageObs.highVL || []
        case 'rejected':
            return vlMessageObs.rejectedVL || []
        case 'normal':
            return vlMessageObs.normalVL || []
        default:
            return []
    }
})

const viewItem = (item: any) => openNotification(item)

const clearItem = (item: any) => {
    if (item.id) {
        clearNotification(item.id, (id: any) => {
            console.log(`Cleared notification ${id}`)
        })
    }
}

const getEmptyMessage = () => {
    switch (selectedCategory.value) {
        case 'high_vl':
            return 'No high viral load results found'
        case 'rejected':
            return 'No rejected samples found'
        case 'normal':
            return 'No normal results found'
        default:
            return 'No results found'
    }
}

// Load notifications on mount
onMounted(() => {
    loadNotifications()
})
</script>

<style scoped>
.vl-alerts-container {
    padding: 16px;
}

.category-segment {
    margin-bottom: 20px;
    --background: rgba(0, 100, 2, 0.034);
    border-radius: 8px;
}

.table-container {
    background: white;
    border-radius: 8px;
    overflow-x: auto;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.vl-table {
    width: 100%;
    border-collapse: collapse;
    min-width: 600px;
}

.vl-table thead {
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
}

.vl-table th {
    padding: 16px 12px;
    font-weight: 600;
    font-size: 14px;
    color: #334155;
    text-align: left;
    border-bottom: 2px solid #e2e8f0;
}

.vl-table td {
    padding: 12px;
    border-bottom: 1px solid #e2e8f0;
    vertical-align: middle;
}

.vl-table tbody tr {
    transition: all 0.2s ease;
}

.vl-table tbody tr:hover {
    background-color: #f8fafc;
}

.empty-message {
    text-align: center;
    color: #64748b;
    font-style: italic;
    padding: 40px 12px;
    background-color: #f8fafc;
}
</style>