<template>
    <ion-item lines="none" v-if="formData.isNonePatientVisit">
        <ion-icon size="large" slot="start" :icon="informationCircleOutline"></ion-icon>
        <ion-label style="font-size:1.3rem;padding:10px;"> {{ formData.nonePatientStatus }} </ion-label>
    </ion-item>
    <div v-if="!formData.isNonePatientVisit">
        <VlInfoAlert :patientID="Number(patient.patientID)" />
    </div>
    <ion-accordion-group :value="activeAccordion" @ion-change="handleAccordion">
        <ion-accordion v-if="!formData.isNonePatientVisit" value="labInvestigationForm">
            <ion-item slot="header" color="light">
                <ion-label class="previousLabel">Lab investigations</ion-label>
            </ion-item>
            <ion-card class="ion-padding" slot="content">
                <ion-card-content>
                    <ArtLabOrders :key="formData.labKey" :patientID="Number(patient.patientID)" />
                </ion-card-content>
            </ion-card>
        </ion-accordion>
        <ion-accordion v-if="!formData.isNonePatientVisit" value="weightChart">
            <ion-item slot="header" color="light">
                <ion-label class="previousLabel">Weight Chart</ion-label>
            </ion-item>
            <ion-card class="ion-padding" slot="content">
                <ion-card-content>
                    <ArtWeightChart />
                </ion-card-content>
            </ion-card>
        </ion-accordion>
        <ion-accordion v-if="!formData.isNonePatientVisit && formData.is_transfer_in" value="medicalTranserForm"
            toggle-icon-slot="start">
            <ion-item slot="header" color="light">
                <ion-label class="previousLabel">Medication transfer-in</ion-label>
            </ion-item>
            <ion-card class="ion-padding" slot="content">
                <ion-card-content>
                    <ion-grid>
                        <ion-row>
                            <ion-col>
                                <BasicDateInput required :label="formModel.date_last_received_arvs.label"
                                    :error-message="validationMessages.date_last_received_arvs"
                                    @on-change="(val) => onFormUpdate('date_last_received_arvs', val)"
                                    v-model="formData.date_last_received_arvs" />
                            </ion-col>
                        </ion-row>
                        <ion-row>
                            <ion-col>
                                <BasicSelect required multiple :label="formModel.selected_arvs.label"
                                    :error-message="validationMessages.selected_arvs" :options="formData.arv_drugs"
                                    v-model="formData.selected_arvs"
                                    @on-change="(val) => onFormUpdate('selected_arvs', val)" />
                            </ion-col>
                            <ion-col>
                                <BasicSelect required :label="formModel.drug_interval.label"
                                    :error-message="validationMessages.drug_interval"
                                    :options="formModel.drug_interval.options()" v-model="formData.drug_interval"
                                    @on-change="(val) => onFormUpdate('drug_interval', val)" />
                            </ion-col>
                        </ion-row>
                        <ion-row v-if="formData.selected_arvs.length">
                            <ion-col>
                                <ion-grid v-for="arv in formData.selected_arvs" :key="arv.other.drug.drug_id">
                                    <ion-row>
                                        <ion-col>
                                            <ion-label class="form-label">
                                                {{ arv.label }}
                                                <span style="color: red">*</span>
                                            </ion-label>
                                        </ion-col>
                                    </ion-row>
                                    <ion-row>
                                        <ion-col>
                                            <BasicInput required label="Given Amount"
                                                v-model="arv.other.dispensed_quantity"
                                                :error-message="validationMessages[arv.other.dispensed_quantity_error_id]"
                                                :options="formData.arv_drugs"
                                                @on-change="(val) => onFormUpdate('selected_arvs', val)" />
                                        </ion-col>
                                        <ion-col>
                                            <BasicInput required label="Pill brought" v-model="arv.other.pill_brought"
                                                :error-message="validationMessages[arv.other.pills_brought_error_id]"
                                                :options="formData.arv_drugs"
                                                @on-change="(val) => onFormUpdate('selected_arvs', val)" />
                                        </ion-col>
                                    </ion-row>
                                </ion-grid>
                            </ion-col>
                        </ion-row>
                    </ion-grid>
                </ion-card-content>
            </ion-card>
        </ion-accordion>
        <ion-accordion v-if="formModel.is_pregnant.required() || formModel.is_breastfeeding.required()"
            value="pregnancyForm">
            <ion-item slot="header" color="light">
                <ion-label class="previousLabel">Pregnancy and Breastfeeding</ion-label>
            </ion-item>
            <ion-card class="ion-padding" slot="content">
                <ion-card-content>
                    <ion-grid>
                        <ion-row>
                            <ion-col v-if="formModel.is_pregnant.required()">
                                <BasicYesNoSelect required label="Pregnant?" v-model="formData.is_pregnant"
                                    @on-change="(val: any) => onFormUpdate('is_pregnant', val)"
                                    :error-message="validationMessages['is_pregnant']" />
                            </ion-col>
                            <ion-col v-if="formModel.is_breastfeeding.required()">
                                <BasicYesNoSelect required label="Breastfeeding?" v-model="formData.is_breastfeeding"
                                    @on-change="(val: any) => onFormUpdate('is_breastfeeding', val)"
                                    :error-message="validationMessages['is_breastfeeding']" />
                            </ion-col>
                        </ion-row>
                    </ion-grid>
                </ion-card-content>
            </ion-card>
        </ion-accordion>
        <ion-accordion v-if="formModel.current_fp_methods.required() || formData.onPermanentFpMethods"
            value="familyPlanningForm">
            <ion-item slot="header" color="light">
                <ion-label class="previousLabel">Family Planning</ion-label>
            </ion-item>
            <ion-card class="ion-padding" slot="content">
                <ion-card-content>
                    <div style="border: 1px dashed #ccc; padding: 20px;" v-if="formData.onPermanentFpMethods">
                        <h1 class="ion-text-center">
                            Patient is on Tubal ligation method
                        </h1>
                    </div>
                    <ion-grid>
                        <ion-row v-if="formModel.current_fp_methods.required() || formModel.fp_methods.required()">
                            <ion-col v-if="formModel.current_fp_methods.required()">
                                <BasicMultiSelect required :label="formModel.current_fp_methods.label"
                                    v-model="formData.current_fp_methods"
                                    @on-change="(val: any) => onFormUpdate('current_fp_methods', val)"
                                    :error-message="validationMessages['current_fp_methods']" />
                            </ion-col>
                            <ion-col v-if="formModel.fp_methods.required()">
                                <BasicMultiSelect required :label="formModel.fp_methods.label"
                                    v-model="formData.fp_methods"
                                    @on-change="(val: any) => onFormUpdate('fp_methods', val)"
                                    :error-message="validationMessages['fp_methods']" />
                            </ion-col>
                        </ion-row>
                        <ion-row v-if="formModel.reason_for_no_fpm.required()">
                            <ion-col>
                                <BasicRadioSelect required :label="formModel.reason_for_no_fpm.label"
                                    v-model="formData.reason_for_no_fpm"
                                    @on-change="(val: any) => onFormUpdate('reason_for_no_fpm', val)"
                                    :error-message="validationMessages['reason_for_no_fpm']"
                                    :options="formModel.reason_for_no_fpm.options()" />
                            </ion-col>
                        </ion-row>
                        <ion-row v-if="formModel.specific_reason_for_no_fpm.required()">
                            <ion-col>
                                <BasicRadioSelect required :label="formModel.specific_reason_for_no_fpm.label"
                                    v-model="formData.specific_reason_for_no_fpm"
                                    :options="formModel.specific_reason_for_no_fpm.options()"
                                    @on-change="(val: any) => onFormUpdate('specific_reason_for_no_fpm', val)"
                                    :error-message="validationMessages['specific_reason_for_no_fpm']" />
                            </ion-col>
                        </ion-row>
                        <ion-row v-if="formModel.offer_contraceptives.required()">
                            <ion-col>
                                <BasicRadioSelect required :label="formModel.offer_contraceptives.label"
                                    v-model="formData.offer_contraceptives"
                                    @on-change="(val: any) => onFormUpdate('offer_contraceptives', val)"
                                    :error-message="validationMessages['offer_contraceptives']"
                                    :options="formModel.offer_contraceptives.options()" />
                            </ion-col>
                        </ion-row>
                        <ion-row v-if="formModel.offered_intervention.required()">
                            <ion-col>
                                <BasicMultiSelect required :label="formModel.offered_intervention.label"
                                    v-model="formData.offered_intervention"
                                    @on-change="(val: any) => onFormUpdate('offered_intervention', val)"
                                    :error-message="validationMessages['offered_intervention']" />
                            </ion-col>
                        </ion-row>
                    </ion-grid>
                </ion-card-content>
            </ion-card>
        </ion-accordion>
        <ion-accordion
            v-if="formModel.offer_cxca.required() || (formModel.cxcaModel.isDueForAppointment() && formData.cxcaEnabled)"
            value="cervicalCancerScreeningForm">
            <ion-item slot="header" color="light">
                <ion-label class="previousLabel">Cervical Cancer Screening</ion-label>
            </ion-item>
            <ion-card class="ion-padding" slot="content">
                <ion-card-content>
                    <div style="border: 1px dashed #ccc; padding: 20px;"
                        v-if="formModel.cxcaModel.isDueForAppointment()">
                        <h1 class="ion-text-center">
                            Patient is due for Cervical Cancer Screening on
                            {{ HisDate.toStandardHisDisplayFormat(formData.CxCaAppointDate.appointment_date) }}
                        </h1>
                    </div>
                    <ion-grid>
                        <ion-row v-if="formModel.offer_cxca.required()">
                            <ion-col>
                                <BasicYesNoSelect required :label="formModel.offer_cxca.label"
                                    v-model="formData.offer_cxca"
                                    @on-change="(val: any) => onFormUpdate('offer_cxca', val)"
                                    :error-message="validationMessages['offer_cxca']" />
                            </ion-col>
                        </ion-row>
                        <ion-row v-if="formModel.reason_for_no_cxca.required()">
                            <ion-col>
                                <BasicRadioSelect required :label="formModel.reason_for_no_cxca.label"
                                    v-model="formData.reason_for_no_cxca"
                                    @on-change="(val: any) => onFormUpdate('reason_for_no_cxca', val)"
                                    :error-message="validationMessages['reason_for_no_cxca']"
                                    :options="formModel.reason_for_no_cxca.options()" />
                            </ion-col>
                        </ion-row>
                        <ion-row v-if="formModel.previous_cxca_test_date.required()">
                            <ion-col>
                                <BasicDateInput required :label="formModel.previous_cxca_test_date.label"
                                    v-model="formData.previous_cxca_test_date"
                                    @on-change="(val) => onFormUpdate('previous_cxca_test_date', val.value.standardDate)"
                                    :error-message="validationMessages['previous_cxca_test_date']" />
                            </ion-col>
                        </ion-row>
                    </ion-grid>
                </ion-card-content>
            </ion-card>
        </ion-accordion>
        <ion-accordion v-if="!formData.isNonePatientVisit" value="sideEffectHistoryForm">
            <ion-item slot="header" color="light">
                <ion-label class="previousLabel">Side effects / Contraindications history</ion-label>
            </ion-item>
            <ion-card class="ion-padding" slot="content">
                <ion-card-content>
                    <table v-if="formData.prevSideEffectsRows.length > 0" class="modern-table">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Condition</th>
                                <th>Drug Induced</th>
                                <th>Drug</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="row in formData.prevSideEffectsRows" :key="row">
                                <td>
                                    {{ row.date }}
                                </td>
                                <td>
                                    {{ row.condition }}
                                </td>
                                <td>
                                    {{ row.drug_induced ? 'Yes' : 'No' }}
                                </td>
                                <td>
                                    {{ row.drug }}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <div style="border: 1px dashed #ccc; padding: 20px;" v-else>
                        <h1 class="ion-text-center">
                            No previous side effects found
                        </h1>
                    </div>
                </ion-card-content>
            </ion-card>
        </ion-accordion>
        <ion-accordion v-if="formModel.side_effects.required()" value="sideEffectsForm">
            <ion-item slot="header" color="light">
                <ion-label class="previousLabel">{{ formModel.side_effects.label }}</ion-label>
            </ion-item>
            <ion-card class="ion-padding" slot="content">
                <ion-card-content>
                    <ion-grid>
                        <ion-row v-if="formModel.side_effects.required()">
                            <ion-col size-md="4" size-sm="12" v-for="(option, index) in formData.side_effects"
                                :key="index">
                                <BasicYesNoSelect required
                                    :badge-text="option.contraIndication?.type ? `Cause: ${option.contraIndication?.type}` : undefined"
                                    :label="option.label" v-model="option.value"
                                    @on-change="(val: any) => onFormUpdate('side_effects', { value: val, option })"
                                    :error-message="validationMessages[option.id]" />
                            </ion-col>
                        </ion-row>
                    </ion-grid>
                </ion-card-content>
            </ion-card>
        </ion-accordion>
        <ion-accordion v-if="formModel.other_side_effects.required()" value="otherSideEffectsForm">
            <ion-item slot="header" color="light">
                <ion-label class="previousLabel">{{ formModel.other_side_effects.label }}</ion-label>
            </ion-item>
            <ion-card class="ion-padding" slot="content">
                <ion-card-content>
                    <ion-grid>
                        <ion-row v-if="formModel.other_side_effects.required()">
                            <ion-col size-md="4" size-sm="12" v-for="(option, index) in formData.other_side_effects"
                                :key="index">
                                <BasicYesNoSelect
                                    :badge-text="option.contraIndication?.type ? `Cause: ${option.contraIndication?.type}` : undefined"
                                    required :label="option.label" v-model="option.value"
                                    @on-change="(val: any) => onFormUpdate('other_side_effects', { ...option, value: val })"
                                    :error-message="validationMessages[option.id]" />
                            </ion-col>
                        </ion-row>
                        <ion-row v-if="formModel.other_side_effect_specify.required()">
                            <ion-col>
                                <BasicInput required :label="formModel.other_side_effect_specify.label"
                                    v-model="formData.other_side_effect_specify"
                                    @on-change="(val: any) => onFormUpdate('other_side_effect_specify', val)"
                                    :error-message="validationMessages['other_side_effect_specify']" />
                            </ion-col>
                        </ion-row>
                    </ion-grid>
                </ion-card-content>
            </ion-card>
        </ion-accordion>
        <ion-accordion v-if="formModel.on_tb_treatment.required()" value="tbTreatmentForm">
            <ion-item slot="header" color="light">
                <ion-label class="previousLabel">TB Treatment</ion-label>
            </ion-item>
            <ion-card class="ion-padding" slot="content">
                <ion-card-content>
                    <ion-grid>
                        <ion-row v-if="formModel.on_tb_treatment.required()">
                            <ion-col>
                                <BasicYesNoSelect required :label="formModel.on_tb_treatment.label"
                                    v-model="formData.on_tb_treatment"
                                    @on-change="(val: any) => onFormUpdate('on_tb_treatment', val)"
                                    :error-message="validationMessages['on_tb_treatment']" />
                            </ion-col>
                        </ion-row>
                        <ion-row v-if="formModel.tb_date_started_treatment_known.required()">
                            <ion-col>
                                <BasicYesNoSelect required :label="formModel.tb_date_started_treatment_known.label"
                                    v-model="formData.tb_date_started_treatment_known"
                                    @on-change="(val: any) => onFormUpdate('tb_date_started_treatment_known', val)"
                                    :error-message="validationMessages['tb_date_started_treatment_known']" />
                            </ion-col>
                        </ion-row>
                        <ion-row v-if="formModel.tb_start_date.required() || formModel.tb_treatment_period.required()">
                            <ion-col v-if="formModel.tb_start_date.required()">
                                <BasicDateInput required :label="formModel.tb_start_date.label"
                                    v-model="formData.tb_start_date"
                                    @on-change="(val) => onFormUpdate('tb_start_date', val)"
                                    :error-message="validationMessages['tb_start_date']" />
                            </ion-col>
                            <ion-col v-if="formModel.tb_treatment_period.required()">
                                <BasicInput required :label="formModel.tb_treatment_period.label"
                                    v-model="formData.tb_treatment_period"
                                    @on-change="(val: any) => onFormUpdate('tb_treatment_period', val)"
                                    :error-message="validationMessages['tb_treatment_period']" />
                            </ion-col>
                        </ion-row>
                        <ion-row v-if="formModel.tb_side_effects.required()">
                            <ion-col size="12">
                                <ion-item>
                                    <ion-label style="font-weight: bold; font-size: 1.3rem;">TB associated
                                        symptoms</ion-label>
                                </ion-item>
                            </ion-col>
                            <ion-col size-md="6" size-sm="12" v-for="(option, index) in formData.tb_side_effects"
                                :key="index">
                                <BasicYesNoSelect required :label="option.label" v-model="option.value"
                                    @on-change="(val: any) => onFormUpdate('tb_side_effects', { ...option, value: val })"
                                    :error-message="validationMessages[option.id]" />
                            </ion-col>
                        </ion-row>
                        <ion-row
                            v-if="formModel.chestXrayScreeningDone.required() || formModel.molecularScreeningDone.required()">
                            <ion-col size="12">
                                <ion-item>
                                    <ion-label style="font-weight: bold; font-size: 1.3rem;">TB Screening methods
                                        used</ion-label>
                                </ion-item>
                            </ion-col>
                            <ion-col v-if="formModel.chestXrayScreeningDone.required()">
                                <BasicYesNoSelect required :label="formModel.chestXrayScreeningDone.label"
                                    v-model="formData.chestXrayScreeningDone"
                                    @on-change="(val: any) => onFormUpdate('chestXrayScreeningDone', val)"
                                    :error-message="validationMessages['chestXrayScreeningDone']" />
                            </ion-col>
                            <ion-col v-if="formModel.molecularScreeningDone.required()">
                                <BasicYesNoSelect required :label="formModel.molecularScreeningDone.label"
                                    v-model="formData.molecularScreeningDone"
                                    @on-change="(val: any) => onFormUpdate('molecularScreeningDone', val)"
                                    :error-message="validationMessages['molecularScreeningDone']" />
                            </ion-col>
                        </ion-row>
                        <ion-row
                            v-if="formModel.chestXrayScreeningResult.required() || formModel.molecularScreeningResult.required()">
                            <ion-col size="12">
                                <ion-item>
                                    <ion-label style="font-weight: bold; font-size: 1.3rem;">TB Screening
                                        results</ion-label>
                                </ion-item>
                            </ion-col>
                            <ion-col v-if="formModel.chestXrayScreeningResult.required()">
                                <BasicRadioSelect required :label="formModel.chestXrayScreeningResult.label"
                                    v-model="formData.chestXrayScreeningResult"
                                    @on-change="(val: any) => onFormUpdate('chestXrayScreeningResult', val)"
                                    :error-message="validationMessages['chestXrayScreeningResult']"
                                    :options="formModel.chestXrayScreeningResult.option()" />
                            </ion-col>
                            <ion-col v-if="formModel.molecularScreeningResult.required()">
                                <BasicRadioSelect required :label="formModel.molecularScreeningResult.label"
                                    v-model="formData.molecularScreeningResult"
                                    @on-change="(val: any) => onFormUpdate('molecularScreeningResult', val)"
                                    :error-message="validationMessages['molecularScreeningResult']"
                                    :options="formModel.molecularScreeningResult.option()" />
                            </ion-col>
                        </ion-row>
                        <ion-row>
                        </ion-row>
                        <ion-row v-if="formModel.tb_status.required()">
                            <ion-col>
                                <BasicRadioSelect required :label="formModel.tb_status.label"
                                    v-model="formData.tb_status"
                                    @on-change="(val: any) => onFormUpdate('tb_status', val)"
                                    :error-message="validationMessages['tb_status']"
                                    :options="formModel.tb_status.option()" />
                            </ion-col>
                        </ion-row>
                    </ion-grid>
                    <ion-grid>
                        <ion-row v-if="formModel.routine_tb_therapy.required()">
                            <ion-col>
                                <BasicRadioSelect required :label="formModel.routine_tb_therapy.label"
                                    v-model="formData.routine_tb_therapy"
                                    :options="formModel.routine_tb_therapy.option()"
                                    @on-change="(val: any) => onFormUpdate('routine_tb_therapy', val)"
                                    :error-message="validationMessages['routine_tb_therapy']" />
                            </ion-col>
                        </ion-row>
                        <ion-row v-if="formModel.dateStartedTpt.required()">
                            <ion-col v-if="formModel.dateStartedTpt.required()">
                                <BasicDateInput required :label="formModel.dateStartedTpt.label"
                                    v-model="formData.dateStartedTpt"
                                    @on-change="(val: any) => onFormUpdate('dateStartedTpt', val)"
                                    :error-message="validationMessages['dateStartedTpt']" />
                            </ion-col>
                            <ion-col v-if="formModel.transferFacility.required()">
                                <BasicFacilitySelector required :label="formModel.transferFacility.label"
                                    v-model="formData.transferFacility"
                                    @on-change="(val: any) => onFormUpdate('transferFacility', val)"
                                    :error-message="validationMessages['transferFacility']" />
                            </ion-col>
                        </ion-row>
                    </ion-grid>
                    <ion-grid v-if="formModel.tptDrugQuantities.required()">
                        <ion-row>
                            <ion-col>
                                <ion-item>
                                    <ion-label style="font-weight: bold; font-size: 1.3rem;">TPT
                                        Dispensations</ion-label>
                                </ion-item>
                            </ion-col>
                        </ion-row>
                        <ion-row v-for="(order, index) in formData.tptDrugQuantities" :key="index">
                            <ion-col>
                                <ion-item lines="none">
                                    <ion-label>
                                        {{ order.name }}
                                    </ion-label>
                                </ion-item>
                            </ion-col>
                            <ion-col>
                                <BasicInput v-model="order.quantity"
                                    @on-change="(val: any) => onFormUpdate('tptDrugQuantities', { index, field: 'quantity', value: val })" />
                            </ion-col>
                        </ion-row>
                        <ion-row>
                            <ion-col>
                                <div style="color:red;font-weight:bold;font-style:italic;">{{
                                    validationMessages['tptDrugQuantities'] }}</div>
                            </ion-col>
                        </ion-row>
                    </ion-grid>
                </ion-card-content>
            </ion-card>
        </ion-accordion>
        <ion-accordion value="medicationPrescription">
            <ion-item slot="header" color="light">
                <ion-label class="previousLabel">Medical prescription</ion-label>
            </ion-item>
            <ion-card class="ion-padding" slot="content">
                <ion-grid>
                    <ion-row v-if="formModel.allergic_to_sulphur.required()">
                        <ion-col>
                            <BasicRadioSelect required v-model="formData.allergic_to_sulphur"
                                :label="formModel.allergic_to_sulphur.label"
                                :options="formModel.allergic_to_sulphur.options()"
                                @on-change="(val: any) => onFormUpdate('allergic_to_sulphur', val)"
                                :error-message="validationMessages['allergic_to_sulphur']" />
                        </ion-col>
                    </ion-row>
                    <ion-row>
                        <ion-col>
                            <BasicMultiSelect required v-model="formData.medication_to_prescribe"
                                :label="formModel.medication_to_prescribe.label"
                                @on-change="(val: any) => onFormUpdate('medication_to_prescribe', val)"
                                :error-message="validationMessages['medication_to_prescribe']" />
                        </ion-col>
                    </ion-row>
                </ion-grid>
            </ion-card>
        </ion-accordion>
    </ion-accordion-group>
</template>
<script lang="ts" setup>
import { onMounted, ref } from "vue";
import {
    IonIcon,
    IonCard,
    IonCardContent,
    IonItem,
    IonLabel,
    IonAccordion,
    IonAccordionGroup,
    IonGrid,
    IonRow,
    IonCol
} from "@ionic/vue"
import { informationCircleOutline } from "ionicons/icons"
import { useDemographicsStore } from "@/stores/DemographicStore";
import { storeToRefs } from "pinia";
import { PatientService } from "@/services/patient_service";
import { ConsultationService } from "../services/consultation_service";
import ART_PROP from "../art_global_props";
import HisDate from "@/utils/Date";
import BasicYesNoSelect from "@/components/Forms/BasicFormFields/BasicYesNoSelect.vue";
import BasicRadioSelect from "@/components/Forms/BasicFormFields/BasicRadioSelect.vue";
import BasicMultiSelect from "@/components/Forms/BasicFormFields/BasicMultiSelect.vue";
import BasicInput from "@/components/Forms/BasicFormFields/BasicInput.vue";
import BasicSelect from "@/components/Forms/BasicFormFields/BasicSelect.vue";
import BasicDateInput from "@/components/Forms/BasicFormFields/BasicDateInput.vue";
import { ConceptService } from "@/services/concept_service";
import dayjs from "dayjs";
import { UserService } from "@/services/user_service";
import { RegimenService } from "@/services/regimen_service";
import { uniqueBy } from "@/utils/Arrays";
import { PrescriptionService } from "../services/prescription_service";
import { findIndex, isEmpty, uniqBy } from "lodash";
import { infoAlert, toastDanger, toastSuccess, toastWarning } from "@/utils/Alerts";
import { infoActionSheet, optionsActionSheet } from "@/utils/ActionSheets";
import { PatientTypeService } from "../services/patient_type_service";
import VlInfoAlert from "./consultation/vl-info-alert.vue";
import BasicFacilitySelector from "@/components/Forms/BasicFormFields/BasicFacilitySelector.vue";
import ArtLabOrders from "./Lab/ArtLabOrders.vue";
import ArtWeightChart from "./charts/ArtWeightChart.vue";
import { modal } from "@/utils/modal";
import NewOrder from "./Lab/NewOrder.vue"
import SideEffectInput from "./SideEffectInput.vue";
import { Option } from "@/components/Forms/FieldInterface";

const demographicsStore = useDemographicsStore();
const { patient } = storeToRefs(demographicsStore) as any;
const patientService = new PatientService()
const consultationService = new ConsultationService(patient.value.patientID, -1);
const prescriptionService = new PrescriptionService(patient.value.patientID, -1);
const validationMessages = ref<Record<string, string>>({})
const activeAccordion = ref("labInvestigationForm")

const formData = ref<any>({
    labKey: 1, // increment this to refresh the lab orders component
    isNonePatientVisit: false,
    prevSideEffectsRows: [],
    canSubmit: false,
    is_pregnant: '',
    is_breastfeeding: '',
    pregnant_breastfeeding: '',
    has_fp_methods: '',
    current_fp_methods: [],
    reason_for_no_fpm: '',
    specific_reason_for_no_fpm: '',
    offer_contraceptives: '',
    offered_intervention: '',
    offer_cxca: '',
    cxca_reminder: '',
    reason_for_no_cxca: '',
    previous_cxca_test_date: '',
    side_effects: [],
    other_side_effects: [],
    other_side_effect_specify: '',
    on_tb_treatment: '',
    tb_date_started_treatment_known: '',
    tb_start_date: '',
    tb_treatment_period: '',
    tb_side_effects: [],
    tb_screening_testing_methods: [],
    tb_screening_testing_results: [],
    tb_status: '',
    hasTbHistoryObs: false,
    routine_tb_therapy: '',
    refer_to_clinician: '',
    chestXrayScreeningDone: '',
    molecularScreeningDone: '',
    chestXrayScreeningResult: '',
    molecularScreeningResult: '',
    isChildBearing: false,
    hasPregnancyObsToday: false,
    isPregnantObsStatus: false,
    currentlyBreastfeeding: false,
    onPermanentFpMethods: false,
    patientHitMenopause: false,
    cxcaEnabled: false,
    CxCaMaxAge: -1,
    CxCaStartAge: -1,
    DueForCxCa: false,
    clientHadAHysterectomy: false,
    CxCaAppointDate: null,
    isWeightLossControlled: false,
    hasTbTreatmentDate: false,
    dateStartedTpt: '',
    tptDrugQuantities: [] as any,
    transferFacility: '' as string,
    arv_drugs: [] as any,
    is_transfer_in: true,
    date_art_started: "",
    date_last_received_arvs: "",
    selected_arvs: [],
    drug_interval: null as any,
    weightTrail: [],
    regimens: [] as any,
    lostTenPercentBodyWeight: false,
    tbTestOrderedForSuspectCase: false,
    allergic_to_sulphur: '',
    medication_to_prescribe: [] as any[],
    tptStatus: {} as any,
    autoSelect3HP: false,
    currentWeight: -1,
    reasonForDecliningTpt: '',
    nonePatientStatus: ''
})

const formModel: any = {
    isNonePatientVisit: {
        init: async () => {
            formData.value.nonePatientStatus = ''
            formData.value.isNonePatientVisit = await consultationService.getClient() === 'No';
            if (formData.value.isNonePatientVisit) {
                formData.value.nonePatientStatus = 'Patient is not present on this visit'
            } else {
                formData.value.isNonePatientVisit = (await PatientTypeService.isDrugRefillPatient(patient.value.patientID))
                if (formData.value.isNonePatientVisit) {
                    formData.value.nonePatientStatus = 'Client not enrolled at this facility (emergency consultation or drug refill)'
                    activeAccordion.value = 'medicationPrescription'
                }
            }
        },
        onSubmit: async () => {
            await consultationService.createEncounter()
            await consultationService.saveObservationList([
                (await consultationService.buildValueCoded('Patient present', 'None'))
            ])
            return true
        }
    },
    weightModel: {
        init: async () => {
            formData.value.weightTrail = await patientService.getWeightHistory()
            formData.value.weightLossPercentageNum = patientService.getWeightLossPercentageFromTrail(formData.value.weightTrail)
            formData.value.lostTenPercentBodyWeight = formData.value.weightLossPercentageNum >= 10
        },
        async showWeightlossConfirmation() {
            const action = await infoActionSheet(
                'Recommendation',
                `Patient's weight has dropped by ${formData.value.weightLossPercentageNum}% , is this controlled weight loss??`,
                'Please verify',
                [
                    { name: 'Confirm weight loss', slot: 'start', color: 'success' },
                    { name: 'Confirm controlled', slot: 'end', color: 'primary' }
                ]
            )
            return action === 'Confirm weight loss' ? 'Yes' : 'No'
        }
    },
    date_last_received_arvs: {
        label: "Last ARV Dispensation",
        required: () => !formData.value.isNonePatientVisit && formData.value.is_transfer_in === true,
        init: async () => {
            formData.value.is_transfer_in = await formModel.date_last_received_arvs.getTransferInStatus()
            formData.value.date_art_started = await formModel.date_last_received_arvs.getDateStartedArt()
        },
        buildObs: () => consultationService.buildValueDate(
            'Date drug received from previous facility', formData.value.date_last_received_arvs
        ),
        validation: () => {
            const required = hasValue('date_last_received_arvs')
            if (required) {
                const dateLastReceived = dayjs(formData.value.date_last_received_arvs)
                if (dateLastReceived.isBefore(dayjs(formData.value.date_art_started))) {
                    validationMessages.value['date_last_received_arvs'] = 'Date last received ARVs cannot be before ART start date'
                    return false
                }
                if (dateLastReceived.isAfter(dayjs(consultationService.getDate()))) {
                    validationMessages.value['date_last_received_arvs'] = 'Date last received ARVs cannot be after today'
                    return false
                }
                return true
            }
            return required
        },
        getDateStartedArt: async () => {
            const dateStarted = await ConsultationService.getFirstValueDatetime(patient.value.patientID, 'Date ART started')
            return dateStarted ? HisDate.toStandardHisFormat(dateStarted) : ''
        },
        getTransferInStatus: async () => {
            const receivedArvs = await ConsultationService.getFirstValueCoded(
                patient.value.patientID, 'Ever received ART'
            )
            const transferLetterObs = await ConsultationService.getFirstObs(
                patient.value.patientID, 'Has transfer letter'
            )
            const date = transferLetterObs ? HisDate.toStandardHisFormat(transferLetterObs.obs_datetime) : ''
            return receivedArvs
                && receivedArvs.match(/yes/i)
                && transferLetterObs
                && `${transferLetterObs.value_coded}`.match(/yes/i)
                && date === consultationService.getDate()
        }
    },
    selected_arvs: {
        label: "Last ARV drugs dispensed",
        init: async () => {
            formData.value.arv_drugs = uniqBy((await prescriptionService.getARVs()).map((d: any) => ({
                label: d.name,
                value: d.name,
                other: {
                    drug: d,
                    name: d.name,
                    pill_brought: null,
                    dispensed_quantity: null,
                    dispensed_quantity_error_id: `dispensed_quantity_error_${d.drug_id}`,
                    pills_brought_error_id: `pills_brought_error_${d.drug_id}`,
                }
            })), 'label').sort((a: any, b: any) =>  a.label.localeCompare(b.label))
        },
        required: () => !formData.value.isNonePatientVisit && formData.value.is_transfer_in,
        buildObs: () => {
            return formData.value.selected_arvs.map(async (arv: any) => {
                return {
                    ...(await consultationService.buildObs(
                        'Drug received from previous facility', {
                        'value_drug': arv.other.drug.drug_id,
                        'value_datetime': (() => {
                            prescriptionService.setNextVisitInterval(formData.value.drug_interval.value)
                            return prescriptionService.calculateDateFromInterval()
                        })(),
                        'value_numeric': arv.other.dispensed_quantity
                    }
                    )),
                    child: [
                        await consultationService.buildObs(
                            'Number of tablets brought to clinic', {
                            'value_drug': arv.other.drug.drug_id,
                            'value_numeric': arv.other.pill_brought,
                            'value_datetime': formData.value.date_last_received_arvs
                        })
                    ]
                }
            })
        },
        validation: () => {
            const required = hasValue('selected_arvs')
            let isValid = required
            if (required) {
                formData.value.selected_arvs.forEach((arv: any) => {
                    validationMessages.value[arv.other.dispensed_quantity_error_id] = ''
                    if (!isDigit(arv.other.dispensed_quantity)) {
                        validationMessages.value[arv.other.dispensed_quantity_error_id] = 'A valid digit is required'
                        isValid = false
                    }

                    validationMessages.value[arv.other.pills_brought_error_id] = ''
                    if (!isDigit(arv.other.pill_brought)) {
                        validationMessages.value[arv.other.pills_brought_error_id] = 'A valid digit is required'
                        isValid = false
                    }
                })
            }
            return isValid
        }
    },
    drug_interval: {
        label: "Duration period for last received ARVs",
        required: () => !formData.value.isNonePatientVisit && formData.value.is_transfer_in,
        validation: () => hasValue('drug_interval'),
        options: () => {
            return [
                { label: '2 weeks', value: 14 },
                { label: '1 month', value: 28 },
                { label: '2 months', value: 56 },
                { label: '3 months', value: 84 },
                { label: '4 months', value: 112 },
                { label: '5 months', value: 140 },
                { label: '6 months', value: 168 },
                { label: '7 months', value: 196 },
                { label: '8 months', value: 224 },
                { label: '9 months', value: 252 },
                { label: '10 months', value: 280 },
                { label: '11 months', value: 308 },
                { label: '12 months', value: 336 }
            ]
        }
    },
    pregnancyModel: {
        init: async () => {
            if (!patientService.isFemale()) return
            formData.value.patientHitMenopause = await consultationService.patientHitMenopause()
            if (!formData.value.patientHitMenopause && patientService.isChildBearing()) {
                formData.value.hasPregnancyObsToday = await patientService.hasPregnancyObsToday()
                formData.value.isPregnantObsStatus = await patientService.isPregnant()
                formData.value.currentlyBreastfeeding = await patientService.isBreastfeeding()
                formData.value.onPermanentFpMethods = await consultationService.getTLObs()
            }
        },
        isPregnant() {
            return formData.value.isPregnantObsStatus || formData.value.is_pregnant === 'Yes'
        },
        pregnancyEligible() {
            return patientService.isFemale() &&
                patientService.isChildBearing() &&
                !formData.value.onPermanentFpMethods
        }
    },
    is_pregnant: {
        label: "Is patient pregnant?",
        required: () => !formData.value.isNonePatientVisit && !formData.value.hasPregnancyObsToday && formModel.pregnancyModel.pregnancyEligible(),
        buildObs: () => consultationService.buildValueCoded(
            'Is patient pregnant',
            formData.value.is_pregnant
        ),
        validation: () => hasValue('is_pregnant')
    },
    is_breastfeeding: {
        label: "Is patient breastfeeding?",
        required: () => !formData.value.isNonePatientVisit && !formData.value.hasPregnancyObsToday && formModel.pregnancyModel.pregnancyEligible(),
        buildObs: () => consultationService.buildValueCoded(
            'Is patient breast feeding',
            formData.value.is_breastfeeding
        ),
        validation: () => hasValue('is_breastfeeding')
    },
    current_fp_methods: {
        label: "What method are you currently on?",
        init: () => {
            formData.value.current_fp_methods = consultationService.getFamilyPlanningMethods().map((method: string) => ({
                label: method,
                value: method,
                checked: false
            }))
        },
        onFormUpdate: (field: string, val: any) => {
            if (field === 'current_fp_methods') {
                if (val.checked && val.label === "NONE") {
                    formData.value.current_fp_methods = formData.value.current_fp_methods.map((i: any) => {
                        if (i.label != "NONE") {
                            i.checked = false;
                            i.disabled = false;
                        }
                        return i;
                    });
                } else if (val.label != "NONE" && val.checked) {
                    if (val.label.match(/condom/gi)) infoAlert("Combine with other modern methods of family planning")
                    const noneIndex = findIndex(formData.value.current_fp_methods, { label: "NONE" });
                    formData.value.current_fp_methods[noneIndex].checked = false;
                    formData.value.current_fp_methods = consultationService.familyPlanningMethods(
                        val.label,
                        formData.value.current_fp_methods
                    );
                    const currentIndex = findIndex(formData.value.current_fp_methods, { label: val.label });
                    formData.value.current_fp_methods[currentIndex].checked = true;
                } else {
                    formData.value.current_fp_methods = formData.value.current_fp_methods.map((i: any) => {
                        i.disabled = false;
                        return i;
                    })
                }
            }
        },
        required: () => {
            return !formData.value.isNonePatientVisit && formModel.pregnancyModel.pregnancyEligible() &&
                !formModel.pregnancyModel.isPregnant() &&
                !formData.value.patientHitMenopause
        },
        buildObs: () => formData.value.current_fp_methods.filter((d: any) => d.checked)
            .map((d: any) => consultationService.buildValueCoded('Family planning method', d.value)
            ),
        validation: () => {
            const required = hasValue('current_fp_methods')
            if (required) {
                if ((formData.value.current_fp_methods ?? []).every((d: any) => !d.checked)) {
                    validationMessages.value['current_fp_methods'] = 'Please select a family planning method'
                    return false
                }
            }
            return required
        }
    },
    fp_methods: {
        label: "What method are you providing today?",
        init: () => {
            formData.value.fp_methods = consultationService.getFamilyPlanningMethods().map((method: string) => ({
                label: method,
                value: method,
                checked: false
            }))
        },
        onFormUpdate: (field: string, val: any) => {
            if (field === 'fp_methods') {
                if (val.checked && val.label === "NONE") {
                    formData.value.fp_methods = formData.value.fp_methods.map((i: any) => {
                        if (i.label != "NONE") {
                            i.checked = false;
                            i.disabled = false;
                        }
                        return i;
                    });
                } else if (val.label != "NONE" && val.checked) {
                    if (val.label.match(/condom/gi)) infoAlert("Combine with other modern methods of family planning")
                    const noneIndex = findIndex(formData.value.fp_methods, { label: "NONE" });
                    formData.value.fp_methods[noneIndex].checked = false;
                    formData.value.fp_methods = consultationService.familyPlanningMethods(
                        val.label,
                        formData.value.fp_methods
                    );
                    const currentIndex = findIndex(formData.value.fp_methods, { label: val.label });
                    formData.value.fp_methods[currentIndex].checked = true;
                } else {
                    formData.value.fp_methods = formData.value.fp_methods.map((i: any) => {
                        i.disabled = false;
                        return i;
                    })
                }
            }
        },
        required: () => {
            return !formData.value.isNonePatientVisit && formModel.pregnancyModel.pregnancyEligible() &&
                !formModel.pregnancyModel.isPregnant() &&
                !formData.value.patientHitMenopause
        },
        buildObs: () => formData.value.fp_methods.filter((d: any) => d.checked).map((d: any) =>
            consultationService.buildValueCoded('Family planning, action to take', d.value)
        ),
        validation: () => {
            const required = hasValue('fp_methods')
            if (required) {
                if ((formData.value.fp_methods ?? []).every((d: any) => !d.checked)) {
                    validationMessages.value['fp_methods'] = 'Please select a family planning method'
                    return false
                }
            }
            return required
        }
    },
    reason_for_no_fpm: {
        label: "Main reason for not using family planning methods",
        validation: () => hasValue('reason_for_no_fpm'),
        required: () => {
            if (formData.value.isNonePatientVisit) {
                return false
            }
            const fpRequired = formModel.fp_methods.required()
            const currentFpRequired = formModel.current_fp_methods.required()
            const isNone = (methods: any[]) => (methods ?? []).some((d: any) => d.checked && /none/i.test(d.value))
            if (fpRequired && currentFpRequired) {
                return isNone(formData.value.fp_methods) && isNone(formData.value.current_fp_methods)
            }
            return false
        },
        buildObs: () => consultationService.buildValueText(
            'Why does the woman not use birth control',
            formData.value.reason_for_no_fpm
        ),
        options: () => {
            return [
                "Not Sexually active",
                "Patient want to get pregnant",
                "Not needed for medical reasons",
                "At risk of unplanned pregnancy",
                "Menopause"
            ].map((o) => ({ label: o, value: o }))
        }
    },
    specific_reason_for_no_fpm: {
        label: "Specific reason for not using family planning methods",
        validation: () => hasValue('specific_reason_for_no_fpm'),
        required: () => {
            return !formData.value.isNonePatientVisit && formModel.reason_for_no_fpm.required() &&
                formData.value.reason_for_no_fpm === "At risk of unplanned pregnancy"
        },
        buildObs: () => consultationService.buildValueText(
            'Reason for not using contraceptives',
            formData.value.specific_reason_for_no_fpm
        ),
        options: () => [
            "Following wishes of spouse",
            "Religious reasons",
            "Afraid of side effects",
            "Never though about it",
            "Indifferent (does not mind getting pregnant)"
        ].map((o) => ({ label: o, value: o }))
    },
    offer_contraceptives: {
        label: "Offer contraceptives?",
        required: () => {
            return !formData.value.isNonePatientVisit && formModel.reason_for_no_fpm.required() &&
                formData.value.reason_for_no_fpm === "At risk of unplanned pregnancy"
        },
        buildObs: () => consultationService.buildValueCoded(
            'Family planning, action to take',
            formData.value.offer_contraceptives
        ),
        validation: () => hasValue('offer_contraceptives'),
        options: () => [
            { label: "Accepted", value: "Yes" },
            { label: "Declined", value: "No" },
            { label: "Discuss with spouse", value: "Discuss with spouse" },
        ]
    },
    offered_intervention: {
        label: "Offered intervention",
        init: () => {
            formData.value.offered_intervention = consultationService.getFamilyPlanningMethods().map((method: string) => ({
                label: method,
                value: method,
                checked: false,
                disabled: /none/i.test(method)
            }))
        },
        required: () => {
            return !formData.value.isNonePatientVisit && formModel.offer_contraceptives.required() &&
                formData.value.offer_contraceptives === "Yes"
        },
        buildObs: () => formData.value.offered_intervention.filter((d: any) => d.checked)
            .map((d: any) => consultationService.buildValueCoded(d.label, d.value)
            ),
        validation: () => {
            const required = formModel.offered_intervention.required()
            if (required) {
                if ((formData.value.offered_intervention ?? []).every((d: any) => !d.checked)) {
                    validationMessages.value['offered_intervention'] = 'Please select an offered intervention'
                    return false
                }
            }
            return required
        }
    },
    cxcaModel: {
        init: async () => {
            if (!patientService.isFemale()) return
            formData.value.cxcaEnabled = await ART_PROP.cervicalCancerScreeningEnabled()
            if (!formData.value.cxcaEnabled) return
            const { start, end } = await ART_PROP.cervicalCancerScreeningAgeBounds()
            formData.value.CxCaMaxAge = end
            formData.value.CxCaStartAge = start
            formData.value.DueForCxCa = await consultationService.clientDueForCxCa()
            formData.value.clientHadAHysterectomy = await consultationService.clientHasHadAHysterectomy();
            try {
                formData.value.CxCaAppointDate = await patientService.nextAppointment(24)
            } catch (e) {
                // Avoid crashing the app
                formData.value.CxCaAppointDate = null
            }
        },
        isDueForAppointment() {
            if (!formData.value.CxCaAppointDate) return false
            // Show notification alert in the UI is woman is due
            const ONE_MONTH = 30
            return ONE_MONTH > HisDate.dateDiffInDays(formData.value.CxCaAppointDate, consultationService.date)
        },
        canScreenCxCa() {
            const age = patientService.getAge()
            return patientService.isFemale()
                && formData.value.DueForCxCa
                && formData.value.cxcaEnabled
                && age >= parseInt(formData.value.CxCaStartAge)
                && age <= parseInt(formData.value.CxCaMaxAge)
                && !formData.value.clientHadAHysterectomy
        }
    },
    offer_cxca: {
        label: "Refer client for CxCa screening",
        required: () => {
            return !formData.value.isNonePatientVisit && formModel.cxcaModel.canScreenCxCa() && !formModel.pregnancyModel.isPregnant()
        },
        buildObs: () => consultationService.buildValueCoded(
            'Offer CxCa',
            formData.value.offer_cxca
        ),
        validation: () => hasValue('offer_cxca')
    },
    reason_for_no_cxca: {
        label: "Reason for NOT offering CxCa",
        required: () => {
            return !formData.value.isNonePatientVisit && formModel.offer_cxca.required() && formData.value.offer_cxca === 'No'
        },
        buildObs: () => consultationService.buildValueCoded(
            'Reason for NOT offering CxCa',
            formData.value.reason_for_no_cxca
        ),
        validation: () => hasValue('reason_for_no_cxca'),
        options: () => ConceptService.getConceptsByCategory("reason_for_no_cxca").map((c: any) => ({
            label: c.name,
            value: c.name,
            other: {
                c
            }
        }))
    },
    previous_cxca_test_date: {
        label: "Previous CxCa test",
        required: () => {
            return !formData.value.isNonePatientVisit && formModel.reason_for_no_cxca.required() && formData.value.reason_for_no_cxca === 'Not due for screening'
        },
        buildObs: () => consultationService.buildValueDate(
            'CxCa test date',
            formData.value.previous_cxca_test_date
        ),
        validation: () => {
            const required = hasValue('previous_cxca_test_date')
            if (required) {
                if (dayjs(formData.value.previous_cxca_test_date).isBefore(dayjs(patientService.getBirthdate()))) {
                    validationMessages.value['previous_cxca_test_date'] = 'Date cannot be before Patient birthdate'
                    return false
                }
                if (dayjs(formData.value.previous_cxca_test_date).isAfter(dayjs(consultationService.date))) {
                    validationMessages.value['previous_cxca_test_date'] = 'Date cannot be in the future'
                    return false
                }
                return true
            }
            return required
        }
    },
    side_effects: {
        required: () => !formData.value.isNonePatientVisit,
        label: "Contraindications / Side effects (select either 'Yes' or 'No')",
        init: async () => {
            formData.value.side_effects = ConceptService.getConceptsByCategory("contraindication", true)
                .map((data) => ({
                    id: `side_effect_${data.name}`,
                    concept_id: data.concept_id,
                    label: data.name,
                    value: "",
                    contraIndication: {}
                }))
            formData.value.side_effects = [...formData.value.side_effects, {
                id: `side_effect_other`,
                concept_id: 0,
                label: "Other",
                value: ""
            }]
            const res = await consultationService.getDrugSideEffects()
            formData.value.prevSideEffectsRows = Object.keys(res)
                .map((k: string) =>
                    Object.values(res[k])
                        .filter((d: any) => !isEmpty(d.name))
                        .map((d: any) => ({
                            date: k,
                            condition: d.name,
                            drug_induced: d.drug_induced ? "Yes" : 'No',
                            drug: d.drug_induced ? d.drug : ''
                        }))
                        .reduce((accum, cur: any) => accum.concat(cur), [])).flat(1)
            return true
        },
        buildObs: () => {
            const sideEffects = formData.value.side_effects.map((async (d: any) => ({
                ...(await consultationService.buildValueCoded('Malawi ART side effects', d.label)),
                child: [await consultationService.buildValueCoded(d.label, d.value)]
            })))
            const contraIndications = formData.value.side_effects.filter((d: any) => d.contraIndication?.obs).map((d: any) => d.contraIndication.obs)
            return [...sideEffects, ...contraIndications]
        },
        onFormUpdate: (field: string, val: any) => {
            if (field === 'side_effects' && val.value === 'Yes' && 'contraIndication' in val.option) {
                modal.show(SideEffectInput, {
                    contraIndicationConceptName: val.option.label,
                    onReason: (data: any) => {
                        formData.value.side_effects = formData.value.side_effects.map((d: any) => {
                            if (val.option.label === d.label) {
                                d.contraIndication = data
                            }
                            return d
                        })
                        modal.hide()
                    }
                })
            }
            if (field === 'side_effects' && val.value === 'No' && 'contraIndication' in val.option) {
                formData.value.side_effects = formData.value.side_effects.map((d: any) => {
                    if (val.option.label === d.label) {
                        d.contraIndication = {}
                    }
                    return d
                })
            }
        },
        validation: () => {
            if (hasValue('side_effects')) {
                let isValid = true
                formData.value.side_effects.forEach((effect: any) => {
                    validationMessages.value[effect.id] = ''
                    if (!effect.value) {
                        validationMessages.value[effect.id] = 'Please select a value'
                        isValid = false
                    }
                })
                return isValid
            }
            return false
        },
    },
    other_side_effects: {
        label: "Other Contraindications / Side effects (select either 'Yes' or 'No')",
        init: async () => {
            formData.value.other_side_effects = ConceptService.getConceptsByCategory("side_effect", true)
                .map((data) => ({
                    id: `other_side_effect_${data.name}`,
                    concept_id: data.concept_id,
                    label: data.name,
                    value: "",
                    contraIndication: {}
                }))
            formData.value.other_side_effects = [...formData.value.other_side_effects, {
                id: `other_side_effect_other`,
                concept_id: 0,
                label: "Other (Specify)",
                value: ""
            }]
        },
        required: () => {
            return !formData.value.isNonePatientVisit && formModel.side_effects.required() &&
                formData.value.side_effects.some((d: any) => d.label === 'Other' && d.value === 'Yes')
        },
        onFormUpdate: (field: string, val: any) => {
            if (field === 'other_side_effects') {
                if (/malnutrition/i.test(val.label) && val.value === 'No' && formData.value.lostTenPercentBodyWeight) {
                    formModel.weightModel.showWeightlossConfirmation()
                        .then((res: string) => {
                            formData.value.other_side_effects = formData.value.other_side_effects.map((d: any) => {
                                if (val.label === d.label) {
                                    d.value = res
                                }
                                return d
                            })
                        })
                }
            }
            if (field === 'other_side_effects' && val.value === 'Yes' && 'contraIndication' in val) {
                modal.show(SideEffectInput, {
                    contraIndicationConceptName: val.label,
                    onReason: (data: any) => {
                        formData.value.other_side_effects = formData.value.other_side_effects.map((d: any) => {
                            if (val.label === d.label) {
                                d.contraIndication = data
                            }
                            return d
                        })
                        modal.hide()
                    }
                })
            }
            if (field === 'other_side_effects' && val.value === 'No' && 'contraIndication' in val) {
                formData.value.other_side_effects = formData.value.other_side_effects.map((d: any) => {
                    if (val.label === d.label) {
                        d.contraIndication = {}
                    }
                    return d
                })
            }
        },
        buildObs: () => {
            const sideEffects = formData.value.other_side_effects.filter((d: any) => d.label != 'Other (Specify)')
                .map(async (d: any) => ({
                    ...(await consultationService.buildValueCoded('Other side effect', d.label)),
                    child: [await consultationService.buildValueCoded(d.label, d.value)]
                }))
            const contraIndications = formData.value.other_side_effects.filter((d: any) => d.contraIndication?.obs)
                .map((d: any) => d.contraIndication.obs)
            return [...sideEffects, ...contraIndications]
        },
        validation: () => {
            const required = hasValue('other_side_effects')
            if (required) {
                let isValid = true
                formData.value.other_side_effects.forEach((effect: any) => {
                    validationMessages.value[effect.id] = ''
                    if (!effect.value) {
                        validationMessages.value[effect.id] = 'Please select a value'
                        isValid = false
                    }
                })
                return isValid
            }
            return required
        }
    },
    other_side_effect_specify: {
        label: "Other Contraindications / Side effects (specify)",
        required: () => {
            return !formData.value.isNonePatientVisit && formModel.other_side_effects.required() &&
                (formData.value.other_side_effects ?? []).some(
                    (d: any) => d.label === "Other (Specify)" && d.value === 'Yes'
                )
        },
        buildObs: async () => ({
            ...(await consultationService.buildValueCoded('Other side effect', 'Other (Specify)')),
            child: [await consultationService.buildValueText('Other (Specify)', formData.value.other_side_effect_specify)]
        }),
        validation: () => hasValue('other_side_effect_specify')
    },
    on_tb_treatment: {
        label: "On TB treatment?",
        required: () => !formData.value.isNonePatientVisit,
        buildObs: () => consultationService.buildValueCoded(
            'TB treatment',
            formData.value.on_tb_treatment
        ),
        validation: () => hasValue('on_tb_treatment')
    },
    tb_date_started_treatment_known: {
        label: "Is Date started treatment known?",
        init: async () => {
            formData.value.hasTbTreatmentDate = false
            const startDate = await ConsultationService.getFirstValueDatetime(
                patient.value.patientID, 'TB treatment start date'
            )
            const tbPeriod = await ConsultationService.getFirstValueNumber(
                patient.value.patientID, 'TB treatment period'
            )
            if (tbPeriod && startDate) {
                const timeElapse = dayjs(consultationService.date).diff(startDate, 'months')
                formData.value.hasTbTreatmentDate = timeElapse <= tbPeriod
            }
        },
        required: () => {
            return !formData.value.isNonePatientVisit && formModel.on_tb_treatment.required() &&
                !formData.value.hasTbTreatmentDate && formData.value.on_tb_treatment === 'Yes'
        },
        validation: () => hasValue('tb_date_started_treatment_known')
    },
    tb_start_date: {
        label: "Enter start date for treatment?",
        required: () => {
            return !formData.value.isNonePatientVisit && formModel.on_tb_treatment.required() &&
                formModel.tb_date_started_treatment_known.required() &&
                formData.value.on_tb_treatment === 'Yes' && formData.value.tb_date_started_treatment_known === 'Yes'
        },
        buildObs: () => consultationService.buildValueDate(
            'TB treatment start date',
            formData.value.tb_start_date
        ),
        validation: () => {
            const required = hasValue('tb_start_date')
            if (required) {
                if (dayjs(formData.value.tb_start_date).isBefore(dayjs(patientService.getBirthdate()))) {
                    validationMessages.value['tb_start_date'] = 'Date cannot be before Patient birthdate'
                    return false
                }
                if (dayjs(formData.value.tb_start_date).isAfter(dayjs(consultationService.date))) {
                    validationMessages.value['tb_start_date'] = 'Date cannot be in the future'
                    return false
                }
                return true
            }
            return required
        }
    },
    tb_treatment_period: {
        label: "Enter Full TB Treatment Period (In Months)",
        required: () => {
            return !formData.value.isNonePatientVisit && formModel.on_tb_treatment.required() &&
                formModel.tb_date_started_treatment_known.required() &&
                formData.value.on_tb_treatment === 'Yes' && formData.value.tb_date_started_treatment_known === 'Yes'
        },
        buildObs: () => consultationService.buildValueNumber(
            'TB treatment period',
            formData.value.tb_treatment_period
        ),
        validation: () => {
            const required = hasValue('tb_treatment_period')
            if (required) {
                if (/^d+$/i.test(formData.value.tb_treatment_period)) {
                    validationMessages.value['tb_treatment_period'] = 'Treatment period must be a number'
                    return false
                }
                if (Number(formData.value.tb_treatment_period) < 3) {
                    validationMessages.value['tb_treatment_period'] = 'Treatment period cannot be less than 3 months'
                    return false
                }
                if (Number(formData.value.tb_treatment_period) > 9) {
                    validationMessages.value['tb_treatment_period'] = 'Treatment period cannot be greater than 9 months'
                    return false
                }
                return true
            }
            return required
        }
    },
    tb_side_effects: {
        label: "TB Associated symptoms",
        required: () => {
            return !formData.value.isNonePatientVisit && formModel.on_tb_treatment.required() &&
                `${formData.value.on_tb_treatment}`.match(/no/i)
        },
        init: async () => {
            const contraIndications = ConceptService.getConceptsByCategory(
                "tb_symptom", true
            ).map((data) => data.name)
            formData.value.tb_side_effects = contraIndications.map((d: any) => ({
                id: `tb_side_effect_${d}`,
                label: d,
                value: ""
            }))
        },
        onFormUpdate: (field: string, val: any) => {
            if (field === 'tb_side_effects') {
                if (/malnutrition/i.test(val.label) && val.value === 'No' && formData.value.lostTenPercentBodyWeight) {
                    formModel.weightModel.showWeightlossConfirmation()
                        .then((res: string) => {
                            formData.value.tb_side_effects = formData.value.tb_side_effects.map((d: any) => {
                                if (val.label === d.label) {
                                    d.value = res
                                }
                                return d
                            })
                        })
                }
            }
        },
        buildObs: () => formData.value.tb_side_effects.map((async (d: any) => ({
            ...(await consultationService.buildValueCoded('Routine TB Screening', d.label)),
            child: [await consultationService.buildValueCoded(d.label, d.value)]
        }))),
        hasTbSymptoms: () => {
            return formData.value.tb_side_effects.some((d: any) => d.value === 'Yes')
        },
        validation: () => {
            if (hasValue('tb_side_effects')) {
                let isValid = true
                formData.value.tb_side_effects.forEach((effect: any) => {
                    validationMessages.value[effect.id] = ''
                    if (!effect.value) {
                        validationMessages.value[effect.id] = 'Please select a value'
                        isValid = false
                    }
                })
                return isValid
            }
            return false
        }
    },
    chestXrayScreeningDone: {
        label: "Chest X-ray",
        required: () => {
            return !formData.value.isNonePatientVisit && formModel.on_tb_treatment.required() &&
                `${formData.value.on_tb_treatment}`.match(/no/i)
        },
        validation: () => hasValue('chestXrayScreeningDone'),
    },
    molecularScreeningDone: {
        label: "Molecular Screening",
        required: () => {
            return !formData.value.isNonePatientVisit && formModel.on_tb_treatment.required() &&
                formData.value.on_tb_treatment &&
                `${formData.value.on_tb_treatment}`.match(/no/i)
        },
        validation: () => hasValue('molecularScreeningDone')
    },
    chestXrayScreeningResult: {
        label: "Chest X-ray Screening Result",
        required: () => {
            return !formData.value.isNonePatientVisit && formModel.chestXrayScreeningDone.required() && formData.value.chestXrayScreeningDone === 'Yes'
        },
        buildObs: async () => ({
            ...(await consultationService.buildValueCoded("TB screening method used", 'chest x-ray')),
            child: [await consultationService.buildValueCoded('chest x-ray', formData.value.chestXrayScreeningResult)]
        }),
        validation: () => hasValue('chestXrayScreeningResult'),
        option: () => {
            return [
                { label: "Abnormal", value: "Positive" },
                { label: "Normal", value: "Negative" }
            ]
        }
    },
    molecularScreeningResult: {
        label: "Molecular Screening Result",
        required: () => {
            return !formData.value.isNonePatientVisit && formModel.molecularScreeningDone.required() && formData.value.molecularScreeningDone === 'Yes'
        },
        validation: () => hasValue('molecularScreeningResult'),
        buildObs: async () => ({
            ...(await consultationService.buildValueCoded("TB screening method used", 'Molecular WHO Recommended Rapid Diagnostic test')),
            child: [await consultationService.buildValueCoded('Molecular WHO Recommended Rapid Diagnostic test', formData.value.molecularScreeningResult)]
        }),
        option: () => {
            return [
                { label: "Positive", value: "Positive" },
                { label: "Negative", value: "Negative" }
            ]
        }
    },
    tbStatusValueModel: {
        required: () => !formData.value.isNonePatientVisit,
        buildObs: () => {
            if (formModel.on_tb_treatment.required() && formData.value.on_tb_treatment === 'Yes') {
                return consultationService.buildValueCoded("TB Status", 'Confirmed TB on treatment')
            }
            if (formModel.tb_status.required()) {
                return consultationService.buildValueCoded("TB Status", formData.value.tb_status)
            }
            if (formModel.tbScreeningTestResultModel.required()) {
                if (formModel.tbScreeningTestResultModel.positiveViaRapidTest()) {
                    return consultationService.buildValueCoded("TB Status", 'TB Suspected')
                } else {
                    return consultationService.buildValueCoded("TB Status", 'TB NOT suspected')
                }
            }
            if (formModel.tb_side_effects.required() && !formModel.tb_side_effects.hasTbSymptoms()) {
                return consultationService.buildValueCoded("TB Status", 'TB NOT suspected')
            }
            return {}
        }
    },
    tbScreeningTestResultModel: {
        required: () => {
            if (formData.value.isNonePatientVisit) {
                return false
            }
            return formModel.chestXrayScreeningResult.required() ||
                formModel.molecularScreeningResult.required()
        },
        positiveViaRapidTest: () => {
            const molecurlarRequired = formModel.molecularScreeningResult.required()
            const chestXrayRequired = formModel.chestXrayScreeningResult.required()
            if (molecurlarRequired || chestXrayRequired) {
                if (molecurlarRequired && formData.value.molecularScreeningResult === 'Negative') {
                    return false
                }
                if ((chestXrayRequired && formData.value.chestXrayScreeningResult === 'Positive') ||
                    (molecurlarRequired && formData.value.molecularScreeningResult === 'Positive')) {
                    return true
                }
            }
            return false
        }
    },
    tb_status: {
        label: "TB Status",
        required: () => {
            if (formData.value.isNonePatientVisit) {
                return false
            }
            if (formModel.tbScreeningTestResultModel.required() && formModel.tbScreeningTestResultModel.positiveViaRapidTest()) {
                return true
            }
            if (formModel.tb_side_effects.required() && formModel.tb_side_effects.hasTbSymptoms()) {
                return true
            }
            return false
        },
        isTBSuspect: () => {
            return formModel.tb_status.required() && /t/i.test(`${formData.value.tb_status}`)
        },
        onFormUpdate: (field: string, val: any) => {
            if (field === 'tb_status' && /Yes|TB Suspected/i.test(val) && !formData.value.tbTestOrderedForSuspectCase) {
                infoActionSheet(
                    "Lab Order",
                    "The patient is a TB suspect. A lab order is required",
                    "Do you want to order test now?",
                    [
                        { name: "Cancel", slot: "start", color: "success" },
                        { name: "Order Lab", slot: "end", color: "success" }
                    ]
                ).then((action) => {
                    if (action === "Order Lab") {
                        modal.show(NewOrder, {
                            patientID: patient.value.patientID,
                            testFilters: [
                                'TB Microscopic Exam',
                                'GeneXpert',
                                'Culture & Sensitivity',
                                'TB Tests'
                            ],
                            onNewOrder: () => {
                                formData.value.tbTestOrderedForSuspectCase = true
                                formData.value.labKey++
                                toastSuccess("Lab order created successfully")
                            }
                        })
                    }
                })
            }
        },
        validation: () => hasValue('tb_status'),
        option: () => {
            return [
                { label: "TB NOT suspected", value: "TB NOT suspected" },
                { label: "TB Suspected", value: "TB Suspected" },
                { label: "Confirmed TB Not on treatment", value: "Confirmed TB Not on treatment" }
            ]
        }
    },
    routine_tb_therapy: {
        label: "TB preventive therapy (TPT) history",
        init: async () => {
            formData.value.hasTbHistoryObs = (await consultationService.hasTreatmentHistoryObs()) ? true : false
        },
        required: () => {
            return !formData.value.isNonePatientVisit && !formData.value.hasTbHistoryObs
        },
        buildObs: () => consultationService.buildValueText("Previous TB treatment history", formData.value.routine_tb_therapy),
        validation: () => hasValue('routine_tb_therapy'),
        onFormUpdate: (field: string, value: string) => {
            if (field === 'on_tb_treatment' && value === 'Yes') {
                formData.value.routine_tb_therapy = ''
            }
        },
        option: () => {
            let options: string[] = []
            if (/no/i.test(formData.value.on_tb_treatment)) {
                options = [
                    "Currently on IPT",
                    "Currently on 3HP (RFP + INH)",
                    "Currently on INH 300 / RFP 300 (3HP)"
                ]
            }
            options = options.concat([
                "Complete course of 3HP in the past (3 months RFP+INH)",
                "Complete course of IPT in the past (min. 6 months of INH)",
                "Aborted course of 3HP (RFP + INH) in the past",
                "Aborted course of INH 300 / RFP 300 (3HP) in the past",
                "Aborted course of IPT in the past",
                "Never taken IPT or 3HP"
            ])
            return options.map((o) => ({ label: o, value: o }))
        }
    },
    refer_to_clinician: {
        label: "Refer to clinician",
        required: () => {
            return !formData.value.isNonePatientVisit && UserService.isNurse()
        },
        buildObs: () => consultationService.buildValueCoded(
            'Refer to clinician',
            formData.value.refer_to_clinician
        ),
        validation: () => hasValue('refer_to_clinician')
    },
    dateStartedTpt: {
        label: "Date started TPT treatment",
        required: () => !formData.value.isNonePatientVisit && formModel.routine_tb_therapy.required() &&
            `${formData.value.routine_tb_therapy}`.match(/currently/i),
        validation: () => {
            if (hasValue('dateStartedTpt')) {
                if (dayjs(formData.value.dateStartedTpt).isAfter(dayjs(PatientService.getSessionDate()))) {
                    validationMessages.value['dateStartedTpt'] = 'Date cannot be in the future'
                    return false
                }
                if (dayjs(formData.value.dateStartedTpt).isBefore(dayjs(patientService.getBirthdate()))) {
                    validationMessages.value['dateStartedTpt'] = 'Date cannot be before birthdate'
                    return false
                }
                return true
            }
            return false
        }
    },
    transferFacility: {
        label: "Facility TPT last received from",
        required: () => !formData.value.isNonePatientVisit && formModel.routine_tb_therapy.required() &&
            `${formData.value.routine_tb_therapy}`.match(/currently/i),
        buildObs: () => consultationService.buildValueText(
            'Location TPT last received', formData.value.transferFacility
        ),
        validation: () => hasValue('transferFacility')
    },
    tptDrugQuantities: {
        label: "TPT Drug Quantities",
        required: () => !formData.value.isNonePatientVisit && formModel.routine_tb_therapy.required() &&
            `${formData.value.routine_tb_therapy}`.match(/currently/i),
        onFormUpdate: async (field: string, val: any) => {
            if (field === 'routine_tb_therapy') {
                if (val.match(/currently/i)) {
                    const filters = (() => {
                        const drugFilters: string[] = []
                        const tptHistory = formData.value.routine_tb_therapy
                        if (tptHistory.match(/ipt/i)) {
                            drugFilters.push("INH or H (Isoniazid 300mg tablet)")
                        } else if (tptHistory.includes("3HP (RFP + INH)")) {
                            drugFilters.push('INH or H (Isoniazid 300mg tablet)')
                            drugFilters.push('Rifapentine (150mg)')
                        } else if (tptHistory.includes("INH 300 / RFP 300 (3HP)")) {
                            drugFilters.push("INH 300 / RFP 300 (3HP)")
                        }
                        return drugFilters
                    })()
                    if (isEmpty(formData.value.regimens)) {
                        formData.value.regimens = await RegimenService.getCustomIngridients()
                    }
                    const drugs = formData.value.regimens.filter((drug: any) => filters.includes(drug.name))
                    formData.value.tptDrugQuantities = uniqueBy(drugs.map((drug: any) => ({
                        drug,
                        name: drug.name,
                        quantity: null
                    })), 'name')
                }
            }
        },
        buildObs: () => {
            return formData.value.tptDrugQuantities.map((drug: any) => {
                return consultationService.buildObs('TPT Drugs Received', {
                    'value_drug': drug.drug.drug_id,
                    'value_datetime': formData.value.dateStartedTpt,
                    'value_numeric': drug.quantity
                })
            })
        },
        validation: () => {
            if (hasValue('tptDrugQuantities')) {
                const allHaveValues = formData.value.tptDrugQuantities.every((drug: any) => (drug.quantity ?? 0) > 0)
                if (!allHaveValues) {
                    validationMessages.value['tptDrugQuantities'] = 'Please enter quantities for all drugs'
                    return false
                }
                return true
            }
        }
    },
    allergic_to_sulphur: {
        label: "Allergic to Cotrimoxazole?",
        required: () => !formData.value.isNonePatientVisit,
        buildObs: () => consultationService.buildValueCoded(
            'Allergic to sulphur',
            formData.value.allergic_to_sulphur
        ),
        validation: () => hasValue('allergic_to_sulphur'),
        options: () => [
            { label: "Yes", value: "Yes" },
            { label: "No", value: "No" },
            { label: "Unknown", value: "Unknown" }
        ]
    },
    medication_to_prescribe: {
        label: "Medication to prescribe during this visit",
        required: () => true,
        init: async () => {
            formData.value.currentWeight = await patientService.getRecentWeight()
            formData.value.tptStatus = await consultationService.getTptTreatmentStatus()
            formData.value.autoSelect3HP = await ART_PROP.threeHPAutoSelectEnabled()
            formModel.medication_to_prescribe.setMedications()
        },
        getTptRegimensNames: () => [
            '3HP (RFP + INH)',
            'INH 300 / RFP 300 (3HP)',
            'IPT'
        ],
        disableTptOption(regimen: string, status: string) {
            const option = formData.value.medication_to_prescribe.find((opt: any) => opt.label === regimen)
            option.state[status] = true
            option.checked = false
            option.disabled = true
            option.status = Object.keys(option.state).filter((k) => option.state[k])[0]
        },
        disableTptOptions(status: string) {
            formModel.medication_to_prescribe.getTptRegimensNames().forEach((regimen: string) => {
                formModel.medication_to_prescribe.disableTptOption(regimen, status)
            })
        },
        enableTptOptions(statusToEnable: string[]) {
            formModel.medication_to_prescribe.getTptRegimensNames().forEach((regimen: string, i: number) => {
                const option = formData.value.medication_to_prescribe.find((opt: any) => opt.label === regimen)
                statusToEnable.forEach((state) => { option.state[state] = false })
                option.disabled = Object.keys(option.state).some((k) => option.state[k])
                option.status = Object.keys(option.state).filter((k) => option.state[k])[0]
            })
        },
        setMedications: () => {
            formData.value.medication_to_prescribe = [
                {
                    label: 'ARVs',
                    value: 'ARVs',
                    disabled: false,
                    checked: formData.value.autoSelect3HP && !formModel.tb_status.isTBSuspect()
                },
                {
                    label: 'CPT',
                    value: 'CPT',
                    disabled: formData.value.allergic_to_sulphur === 'Yes',
                    checked: formData.value.autoSelect3HP && !formModel.tb_status.isTBSuspect()
                },
                ...(() => {
                    return formModel.medication_to_prescribe.getTptRegimensNames()
                        .map((regimen: string) => {
                            const onAnotherTreatment = formData.value.tptStatus.tpt && formData.value.tptStatus.tpt !== regimen && !formData.value.tptStatus?.completed
                            const state: Record<string, boolean> = {
                                'Completed/ on TB treatment': formData.value.tptStatus.tb_treatment,
                                'Weight below regulation': (regimen === '3HP (RFP + INH)' && formData.value.currentWeight < 20)
                                    || (regimen === 'INH 300 / RFP 300 (3HP)' && formData.value.currentWeight < 30),
                                'Not eligible to start or re-start TPT': !formData.value.tptStatus?.eligible?.['3HP'] && !formData.value.tptStatus?.eligible?.['6H'],
                                'On tb treatment': false,
                                'Confirmed TB Not on treatment': false,
                                'TB Suspect': false,
                                'Patient pregnant': formModel.pregnancyModel.isPregnant(),
                                'On 3HP (RFP + INH) treatment': onAnotherTreatment,
                                'On INH 300 / RFP 300 (3HP) treatment': onAnotherTreatment,
                                'On IPT treatment': onAnotherTreatment,
                                'Completed TPT treatment': formData.value.tptStatus.completed,
                            }
                            const disabled = Object.keys(state).some((k) => state[k])
                            return {
                                label: regimen,
                                value: regimen,
                                checked: (() => {
                                    if (disabled) return false
                                    const tptEligible = (() => {
                                        const type = regimen === 'IPT' ? '6H' : '3HP'
                                        return formData.value.tptStatus?.eligible?.[type]
                                    })()
                                    return formData.value.tptStatus?.tpt === regimen && !formData.value.tptStatus?.completed && tptEligible
                                })(),
                                status: Object.keys(state).filter((k) => state[k])[0],
                                disabled,
                                state
                            }
                        })
                })(),
                {
                    label: 'NONE OF THE ABOVE',
                    value: 'NONE OF THE ABOVE',
                    disabled: false,
                    checked: false
                }
            ]
        },
        requestReasonForDecliningTpt: () => {
            return optionsActionSheet(
                'Reasons for declining TPT',
                '',
                [
                    'Patient declined',
                    'Side-effects (previous or current)',
                    'Stock-out',
                    'Starting TB treatment',
                    'Other'
                ],
                [
                    { name: 'Done', slot: 'start', role: 'action' }
                ]
            ).then((reason) => formData.value.reasonForDecliningTpt = reason.selection)
        },
        onFormUpdate: (field: string, value: any) => {
            if (field === 'allergic_to_sulphur') {
                const option = formData.value.medication_to_prescribe.find((opt: any) => opt.label === 'CPT')
                if (value === "Yes") {
                    option.status = 'Allergic to CPT'
                    option.checked = false
                    option.disabled = true
                } else {
                    option.status = ''
                    option.disabled = false
                }
            }
            if (field === 'on_tb_treatment') {
                if (value === 'Yes') {
                    formModel.medication_to_prescribe.disableTptOptions('On tb treatment')
                } else {
                    formModel.medication_to_prescribe.enableTptOptions(['On tb treatment'])
                }
            }
            if (field === 'tb_side_effects') {
                if (!formModel.tb_side_effects.hasTbSymptoms()) {
                    formModel.medication_to_prescribe.enableTptOptions(['Confirmed TB Not on treatment', 'TB Suspect'])
                }
            }
            if (field === 'tb_status') {
                if (/Yes|TB Suspected/i.test(value)) {
                    formModel.medication_to_prescribe.disableTptOptions('TB Suspect')
                } else if (/confirmed tb/i.test(value)) {
                    formModel.medication_to_prescribe.disableTptOptions('Confirmed TB Not on treatment')
                } else {
                    formModel.medication_to_prescribe.enableTptOptions(['Confirmed TB Not on treatment', 'TB Suspect'])
                }
            }
            if (field === 'is_pregnant') {
                if (value === 'Yes') {
                    formModel.medication_to_prescribe.disableTptOptions('Pregnant patient')
                } else {
                    formModel.medication_to_prescribe.enableTptOptions(['Pregnant patient'])
                }
            }
            if (field === 'routine_tb_therapy') {
                if (/complete/i.test(value)) {
                    formModel.medication_to_prescribe.disableTptOptions('Completed TPT Treatment')
                } else {
                    formModel.medication_to_prescribe.enableTptOptions(['Completed TPT Treatment'])
                }
            }
            if (field === 'medication_to_prescribe') {
                // Unselect everything else if none option is selected
                if (/none/i.test(`${value?.label}`) && value.checked) {
                    formData.value.medication_to_prescribe = formData.value.medication_to_prescribe.map((opt: any) => ({
                        ...opt, checked: opt.label != value.label ? false : true
                    }))
                    return
                }
                if (value.checked) {
                    // Always uncheck the none button
                    formData.value.medication_to_prescribe.find((opt: any) => /none/i.test(opt.label)).checked = false

                    const threeHSelected = formData.value.medication_to_prescribe.find((opt: any) => {
                        return /3h|inh/i.test(opt.label) && opt.checked
                    })
                    const iptSelected = formData.value.medication_to_prescribe.find((opt: any) => {
                        return /ipt/i.test(opt.label) && opt.checked
                    })
                    // IPT and 3H can't be selected together
                    if ((/ipt/i.test(value.label) && threeHSelected) || (/3h|inh/i.test(value.label) && iptSelected)) {
                        infoActionSheet(
                            "IPT / 3HP conflict",
                            "IPT and 3HP can NOT be prescribed together",
                            "Please pick either one",
                            [
                                { name: "Prescribe 3HP", slot: "start", color: "primary" },
                                { name: "Prescribe IPT", slot: "end", color: "primary" },
                            ]
                        ).then((action) => {
                            if (action === 'Prescribe IPT') {
                                threeHSelected.checked = false
                            } else if (action === 'Prescribe 3HP') {
                                iptSelected.checked = false
                            }
                        })
                    }
                    // 3HP (RFP + INH) && INH 300 / RFP 300 (3HP) can't be checked at once. atleast one has to be selected
                    if (/3hp|inh/i.test(value.label)) {
                        const option = formData.value.medication_to_prescribe.find((opt: any) =>
                            /inh|3hp/i.test(opt.label) && value.label != opt.label
                        )
                        option.checked = false
                    }
                    // Reset reason for declining tpt
                    if (/3hp|ipt|inh/i.test(value.label)) formData.value.reasonForDecliningTpt = ''
                } else if (/3hp|ihn|tpt/i.test(`${value.label}`) && !value.checked) {
                    formModel.medication_to_prescribe.requestReasonForDecliningTpt()
                }
            }
        },
        buildObs: () => {
            if (formData.value.medication_to_prescribe.some((opt: any) => /none/i.test(opt.label) && opt.checked)) {
                return consultationService.buildValueCoded('Prescribe drugs', 'No')
            }
            return [
                consultationService.buildValueCoded('Prescribe drugs', 'Yes'),
                ...formData.value.medication_to_prescribe.filter((opt: any) => opt.checked)
                    .map((opt: any) => consultationService.buildValueCoded('Medication orders', opt.label)),
                ...(() => {
                    const eligibleForTpt = formData.value.medication_to_prescribe.every((opt: any) => /3hp|inh|ipt/i.test(opt.label) && !opt.disabled && !opt.checked)
                    if (eligibleForTpt && formData.value.reasonForDecliningTpt) {
                        return [consultationService.buildValueText('Other reason for not seeking services', formData.value.reasonForDecliningTpt)]
                    }
                    return []
                })()
            ]
        },
        validation: () => {
            if (hasValue('medication_to_prescribe')) {
                const nothingChecked = formData.value.medication_to_prescribe.every((m: any) => !m.checked)
                if (nothingChecked) {
                    validationMessages.value['medication_to_prescribe'] = "Please select one or more options"
                    return false
                }
                return true
            }
            return false
        }
    }
}

const isDigit = (val: any) => /^\d+$/i.test(`${val}`)

function handleAccordion(e: any) {
    if (e.srcElement.tagName === 'ION-ACCORDION-GROUP') {
        activeAccordion.value = e.detail.value
    }
}

async function init() {
    formData.value.canSubmit = false
    await Promise.all(
        Object.keys(formModel)
            .filter((k: string) => typeof formModel[k].init === 'function')
            .map((k: string) => formModel[k].init())
    )
    formData.value.canSubmit = true
}

function validateField(field: string) {
    validationMessages.value[field] = ''
    if (typeof formModel[field].validation === 'function') {
        return formModel[field].validation()
    }
    return true
}

function buildObs() {
    return Object.keys(formModel).reduce((a: any, c: any) => {
        if (c in formModel && typeof formModel[c].required === 'function' && formModel[c].required() && typeof formModel[c].buildObs === 'function') {
            const obs = formModel[c].buildObs()
            if (Array.isArray(obs)) {
                return [...a, ...obs]
            }
            return [...a, obs]
        }
        return a
    }, [])
}

function hasValue(field: string) {
    validationMessages.value[field] = ""
    const fieldMeta = formModel[field]
    const required = typeof fieldMeta?.required === 'function' ? fieldMeta.required() : false
    const empty = (() => {
        if (typeof formData.value[field] === 'object') {
            return isEmpty(formData.value[field])
        }
        if (Array.isArray(formData.value[field])) {
            return formData.value[field].length === 0
        }
        return `${formData.value[field]}`.length === 0
    })()
    if (required && empty) {
        validationMessages.value[field] = 'This field is required'
        return false
    }
    return required
}

function runOnSubmitFunctions() {
    const res = Object.keys(formModel)
        .filter((key: string) => typeof formModel[key].onSubmit === 'function')
        .map((key: string) => formModel[key].onSubmit && formModel[key].onSubmit())
    return Promise.all(res)
}

function onFormUpdate(field: string, value: any) {
    validateField(field)
    Object.keys(formModel).forEach((k) =>
        formModel[k]?.onFormUpdate && formModel[k].onFormUpdate(field, value)
    )
}

function validateAll() {
    let isValid: any = true
    Object.keys(formModel).forEach((key: string) => {
        if (typeof formModel[key].required === 'function' && formModel[key].required() && !validateField(key)) {
            isValid = false
        }
    })
    return isValid;
}

async function onSubmit() {
    if (!formData.value.canSubmit) {
        toastWarning("Please wait for all data to load before submitting")
        return false
    }
    if (!validateAll()) {
        toastWarning("Please review form for errors")
        return false
    }
    try {
        await runOnSubmitFunctions()
        if (!validateAll()) {
            toastWarning("Please review form for errors")
            return false
        }
        const obs = (await Promise.all(buildObs())).filter((o: any) => !isEmpty(o))
        await consultationService.createEncounter()
        await consultationService.saveObservationList(obs)
        toastSuccess("Consultation data saved successfully")
        return true
    } catch (e) {
        console.error(e)
        toastDanger("Error has occured while saving observations")
        return false
    }
}

onMounted(() => init())

defineExpose({
    onSubmit
})
</script>
<style scoped>
.modern-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.modern-table th {
    background-color: #f4f4f4;
    color: #333;
    font-weight: bold;
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.modern-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.modern-table tr:last-child td {
    border-bottom: none;
}

.modern-table tr:hover {
    background-color: #f9f9f9;
    transition: background-color 0.3s ease;
}
</style>