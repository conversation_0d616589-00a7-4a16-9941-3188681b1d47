<template>
  <ion-page>
    <Toolbar title="Duplicate Identifiers" />
    <ion-content>
      <ion-grid>
        <ion-row>
          <ion-col size="12" size-md="10" size-lg="8" offset-md="1" offset-lg="2">
            <ion-card>
              <ion-card-header>
                <ion-card-title>{{ title }}</ion-card-title>
              </ion-card-header>
              <ion-card-content>
                <ion-list>
                  <ion-item>
                    <ion-label position="stacked">Select Identifier Type</ion-label>
                    <ion-select v-model="selectedIdentifierType">
                      <ion-select-option v-for="type in identifierTypes" :key="type.value" :value="type.value">
                        {{ type.label }}
                      </ion-select-option>
                    </ion-select>
                  </ion-item>
                </ion-list>

                <div v-if="duplicates.length > 0" class="table-responsive">
                  <table class="appointments-table">
                    <thead>
                      <tr>
                        <th>Identifier</th>
                        <th>Count</th>
                        <th>View</th>
                        <th>Resolve</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="duplicate in duplicates" :key="duplicate.identifier">
                        <td>{{ duplicate.identifier }}</td>
                        <td>{{ duplicate.count }}</td>
                        <td>
                          <ion-button size="small" fill="outline" @click="viewDuplicateDetails(duplicate.identifier)">
                            View
                          </ion-button>
                        </td>
                        <td>
                          <ion-button size="small" color="danger" fill="outline"
                            @click="resolveDuplicate(duplicate.identifier)" :disabled="selectedIdentifierType !== 3">
                            Resolve
                          </ion-button>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>

                <div v-else-if="duplicates.length === 0 && hasSearched" class="ion-text-center ion-padding">
                  <p>No duplicate identifiers found</p>
                </div>
              </ion-card-content>
            </ion-card>
          </ion-col>
        </ion-row>
      </ion-grid>
    </ion-content>
  </ion-page>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from "vue";
import { useRouter } from "vue-router";
import {
  IonPage, IonContent, IonGrid, IonRow, IonCol, IonCard, IonCardHeader,
  IonCardTitle, IonCardContent, IonList, IonItem, IonLabel, IonButton,
  IonSelect, IonSelectOption
} from "@ionic/vue";
import Toolbar from "@/components/Toolbar.vue";
import { IdentifierService, DuplicateIdentifiersInterface } from "@/services/identifier_service";
import { toastWarning } from "@/utils/toasts";
import { loader } from "@/utils/loader";
import { toDisplayFmt } from "@/utils/his_date";
import { modalController } from '@ionic/vue';
import DuplicateDetailsModal from "@/components/DuplicateDetailsModal.vue";

const router = useRouter();
const title = ref("Duplicate Identifiers");
const identifierTypes = ref<Array<{ label: string, value: number }>>([]);
const selectedIdentifierType = ref<number | null>(null);
const duplicates = ref<DuplicateIdentifiersInterface[]>([]);
const hasSearched = ref(false);
const identifierService = ref(new IdentifierService());

onMounted(async () => {
  await loader.show();
  try {
    // Load identifier types
    const types = await IdentifierService.getIdentifierTypes();
    identifierTypes.value = types.map((i: any) => ({
      label: i.name,
      value: i.patient_identifier_type_id
    }));

    // Set default identifier type if available
    if (identifierTypes.value.length > 0) {
      selectedIdentifierType.value = identifierTypes.value[0].value;
    }
  } catch (error) {
    console.error("Error initializing:", error);
    toastWarning("Failed to load identifier types");
  } finally {
    await loader.hide();
  }
});

// Watch for changes in selectedIdentifierType
watch(selectedIdentifierType, async (newValue) => {
  if (newValue) {
    await fetchDuplicates();
  }
});

async function fetchDuplicates() {
  if (!selectedIdentifierType.value) return;

  await loader.show();
  try {
    // Update the type definition for identifierTypes
    const selectedType = identifierTypes.value.find(t => t.value === selectedIdentifierType.value) as { label: string, value: number };
    title.value = selectedType ? `${selectedType.label} Duplicates` : "Duplicate Identifiers";

    identifierService.value.setIdentifierType(selectedIdentifierType.value);
    const response = await identifierService.value.getDuplicateIndentifiers();

    duplicates.value = Array.isArray(response) ? response : [];
    hasSearched.value = true;
  } catch (error) {
    console.error("Error fetching duplicates:", error);
    toastWarning("Failed to fetch duplicate identifiers");
    duplicates.value = [];
  } finally {
    await loader.hide();
  }
}


async function viewDuplicateDetails(identifier: string) {
  await loader.show();
  try {
    const patients = await identifierService.value.getPatientsByIdentifier(identifier);
    const rows = patients.map((patient: any) => {
      try {
        return {
          person_id: patient.patient_id,
          given_name: patient.person.names[0].given_name,
          family_name: patient.person.names[0].family_name,
          gender: patient.person.gender,
          birthdate: toDisplayFmt(patient.person.birthdate),
          patient: patient,
        };
      } catch (e) {
        return {
          person_id: 0,
          given_name: 'N/A',
          family_name: 'N/A',
          gender: 'N/A',
          birthdate: 'N/A',
          patient: {},
        };
      }
    });

    const modal = await modalController.create({
      component: DuplicateDetailsModal,
      componentProps: {
        patients: rows,
        identifierLabel: identifier,
      },
      cssClass: 'large-modal',
    });

    await modal.present();
  } catch (error) {
    console.error("Error viewing duplicate details:", error);
    toastWarning("Failed to load patient details");
  } finally {
    await loader.hide();
  }
}

function resolveDuplicate(identifier: string) {
  router.push(`/npid/duplicates/${identifier}`);
}
</script>

<style scoped>
ion-card {
  margin: 16px;
}

.table-responsive {
  overflow-x: auto;
}

.appointments-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1rem;
}

.appointments-table th,
.appointments-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #dee2e6;
}

.appointments-table th {
  font-weight: 600;
  background-color: #f8f9fa;
}

.appointments-table tr:hover {
  background-color: rgba(0, 0, 0, 0.03);
}
</style>
