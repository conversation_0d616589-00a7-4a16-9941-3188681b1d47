<template>
    <div>
        <ion-progress-bar v-if="isLoading" type="indeterminate"></ion-progress-bar>
        <ion-card v-if="(orders ?? []).length <= 0" style="height: 40vh;">
            <ion-card-content>
                <div class="ion-text-center" style="margin-top: 8%;">
                    <ion-icon style="font-size: 5rem!important;" :icon="medkitOutline"></ion-icon>
                    <h1>No tests found</h1>
                </div>
            </ion-card-content>
        </ion-card>
        <div v-else>
            <ion-accordion-group @ion-change="(e) => formModel.result_indicators.load({ ...e.detail.value, order })"
                v-for="order in orders" :key="order.id" ref="accordionGroup" class="previousView">
                <ion-accordion :value="test" v-for="(test, index) in order.tests" :key="index" toggle-icon-slot="start"
                    class="custom_card">
                    <ion-item slot="header" color="light">
                        <ion-label class="ion-padding">
                            <div>
                                <b>{{ test.name }} <ion-badge color="success">{{ order.accession_number
                                }}</ion-badge></b>
                            </div>
                            <div> Specimen: <b>{{ order.specimen.name }}</b> </div>
                            <div> Result: <b> {{ isEmpty(test.result) ? 'N/A' : test.result }}</b> </div>
                        </ion-label>
                        <ion-chip style="background: green; color: white;">
                            <ion-icon color="light" :icon="calendarOutline"></ion-icon>
                            <b style="padding: 2px;">{{ dayjs(order.order_date).format('DD/MMM/YYYY ') }}</b>
                        </ion-chip>
                    </ion-item>
                    <div slot="content" style="margin-bottom: 125px">
                        <ion-card style="margin:0px auto;width: 80%;">
                            <ion-card-content>
                                <ion-grid>
                                    <ion-row>
                                        <ion-col>
                                            <ion-label>Result date</ion-label>
                                            <DatePicker
                                                @date-up-dated="(d) => { formData[order.id].result_date = d.value.standardDate; formModel.result_date.validation(order.id); }"
                                                place_holder="Enter result date" :date_prop="''" />
                                            <ion-note v-if="formData[order.id].error.result_date">
                                                {{ formData[order.id].error.result_date }}
                                            </ion-note>
                                        </ion-col>
                                        <ion-col v-if="formData[order.id].tests[test.id].allIndicators.length > 0">
                                            <ion-label>Result indicators</ion-label>
                                            <ion-select
                                                @ion-change="() => formModel.result_indicators.validation(order.id)"
                                                v-model="formData[order.id].tests[test.id].selectedIndicators"
                                                :multiple="true" fill="outline" interface="popover"
                                                placeholder="Select result indicator">
                                                <ion-select-option
                                                    v-for="indicator in formData[order.id].tests[test.id].allIndicators"
                                                    :key="indicator.id" :value="indicator">
                                                    {{ indicator.name }}
                                                </ion-select-option>
                                            </ion-select>
                                            <ion-note v-if="formData[order.id].error.result_indicators">
                                                {{ formData[order.id].error.result_indicators }}
                                            </ion-note>
                                        </ion-col>
                                    </ion-row>
                                </ion-grid>
                                <div class="ion-padding">
                                    <ion-card style="margin: 10px;"
                                        v-for="indicator in formData[order.id].tests[test.id].selectedIndicators"
                                        :key="indicator.concept_id">
                                        <ion-card-content>
                                            <ion-grid>
                                                <ion-row>
                                                    <ion-col>
                                                        <h2 style="font-weight:bold;">{{ indicator.name }} result:</h2>
                                                    </ion-col>
                                                    <ion-col>
                                                        <ion-item lines="none">
                                                            <ion-checkbox slot="start"
                                                                @ion-change="() => formModel.isNumericTest.onChange(indicator)"
                                                                v-model="indicator.input.isNumericTest"></ion-checkbox>
                                                            <ion-label style="margin-left: 10px;">
                                                                Numeric result?
                                                            </ion-label>
                                                        </ion-item>
                                                    </ion-col>
                                                </ion-row>
                                                <ion-row v-if="indicator.input.isNumericTest">
                                                    <ion-col size="4">
                                                        <ion-select
                                                            @ion-change="() => formModel.result_modifier.validation(indicator)"
                                                            v-model="indicator.input.result_modifier"
                                                            interface="popover" label-placement="stacked"
                                                            label="Result modifier" fill="outline"
                                                            placeholder="Select Modifier">
                                                            <ion-select-option v-for="modifier in resultModifiers"
                                                                :key="modifier" :value="modifier">
                                                                {{ modifier }}
                                                            </ion-select-option>
                                                        </ion-select>
                                                        <ion-note v-if="indicator.error.result_modifier">
                                                            {{ indicator.error.result_modifier }}
                                                        </ion-note>
                                                    </ion-col>
                                                    <ion-col size="8">
                                                        <ion-input type="number"
                                                            @ion-input="() => formModel.result.validation(indicator)"
                                                            v-model="indicator.input.result" label-placement="stacked"
                                                            label="Result Value" fill="outline"
                                                            placeholder="Enter result value"></ion-input>
                                                        <ion-note v-if="indicator.error.numeric_result">
                                                            {{ indicator.error.numeric_result }}
                                                        </ion-note>
                                                    </ion-col>
                                                </ion-row>
                                                <ion-row v-else>
                                                    <ion-col>
                                                        <ion-label>Alphanumeric result</ion-label>
                                                        <ion-input
                                                            @ion-input="() => formModel.result.validation(indicator)"
                                                            v-model="indicator.input.result" placeholder="Enter result"
                                                            fill="outline"></ion-input>

                                                        <ion-note v-if="indicator.error.text_result">{{
                                                            indicator.error.text_result }}</ion-note>
                                                    </ion-col>
                                                </ion-row>
                                            </ion-grid>
                                        </ion-card-content>
                                    </ion-card>
                                </div>
                                <ion-button @click="onSubmitOrder(order.id, test.id)" class="ion-padding">Save
                                    result(s)</ion-button>
                            </ion-card-content>
                        </ion-card>
                    </div>
                </ion-accordion>
            </ion-accordion-group>
        </div>
    </div>
</template>
<script lang="ts" setup>
import {
    IonNote,
    IonProgressBar,
    IonCheckbox,
    IonButton,
    IonInput,
    IonCard,
    IonCardContent,
    IonGrid,
    IonRow,
    IonCol,
    IonBadge,
    IonChip,
    IonIcon,
    IonAccordion,
    IonAccordionGroup,
    IonItem,
    IonLabel,
    IonSelect,
    IonSelectOption
} from "@ionic/vue"
import { calendarOutline, medkitOutline } from "ionicons/icons";
import { onMounted, ref } from "vue"
import DatePicker from "@/components/DatePicker.vue";
import { PatientService } from "@/services/patient_service";
import { toastDanger, toastSuccess } from "@/utils/Alerts";
import { isEmpty } from "lodash";
import dayjs from "dayjs";
import { LabOrderService } from "@/services/lab_order_service";
import HisDate from "@/utils/Date";

const isLoading = ref(false)
const orders = ref<any>([])
const formData = ref<any>({})
const patientService = new PatientService()
const labService = new LabOrderService(patientService.getID(), -1, "")
const resultModifiers = [
    "=", "<", ">"
]

const formModel: any = {
    result: {
        buildResult: (indicator: any) => {
            return {
                indicator: {
                    concept_id: indicator.concept_id
                },
                value: indicator.input.result,
                value_modifier: indicator.input.result_modifier ? indicator.input.result_modifier : "=",
                value_type: indicator.input.isNumericTest ? "numeric" : "text"
            }
        },
        validation: (indicator: any) => {
            indicator.error.text_result = ""
            indicator.error.numeric_result = ""
            if (indicator.input.isNumericTest) {
                if (!indicator.input.result) {
                    indicator.error.numeric_result = "Result is required"
                    return
                }
                if (isNaN(indicator.input.result)) {
                    indicator.error.numeric_result = "Result must be a number"
                    return
                }
            } else {
                if (!indicator.input.result) {
                    indicator.error.text_result = "Result is required"
                    return
                }
            }
        }
    },
    result_date: {
        formatDate: (date: string) => HisDate.toStandardHisFormat(date),
        validation: (order_id: number) => {
            formData.value[order_id].error.result_date = ""
            if (!formData.value[order_id].result_date) {
                formData.value[order_id].error.result_date = "Result date is required"
                return
            }
            const orderDate = orders.value.find((o: any) => o.id === order_id)?.order_date
            if (orderDate && dayjs(formData.value[order_id].result_date).isBefore(dayjs(orderDate))) {
                const formattedOrderDate = formModel.result_date.formatDate(orderDate)
                formData.value[order_id].error.result_date = `Result date cannot be before order date ${formattedOrderDate}`
                return
            }
            if (dayjs(formData.value[order_id].result_date).isAfter(dayjs(PatientService.getSessionDate()))) {
                formData.value[order_id].error.result_date = "Result date cannot be in the future"
                return
            }
        }
    },
    result_modifier: {
        validation: (indicator: any) => {
            indicator.error.result_modifier = ""
            if (indicator.input.isNumericTest && !indicator.input.result_modifier) {
                indicator.error.result_modifier = "Result modifier is required"
                return
            }
        }
    },
    result_indicators: {
        validation: (order_id: number, test_id: number) => {
            formData.value[order_id].error.result_indicators = ""
            const test = formData.value[order_id].tests[test_id]
            if (test.selectedIndicators.length === 0) {
                formData.value[order_id].error.result_indicators = "Please select at least one result indicator"
                return
            }
        },
        load: async (test: any) => {
            isLoading.value = true
            try {
                if (!test.concept_id) return
                const indicators = formData.value?.[test.order.id]?.tests?.[test.id]?.allIndicators

                if (!isEmpty(indicators)) return
                formData.value[test.order.id].tests[test.id].allIndicators = (await labService.getTestIndicators(test.concept_id))
                    .map((i: any) => ({
                        ...i,
                        input: {
                            result: "",
                            result_modifier: "",
                            isNumericTest: true,
                        },
                        error: {
                            numeric_result: "",
                            text_result: "",
                            result_modifier: ""
                        }
                    }))
            } catch (e) {
                console.log(e)
                toastDanger('unable to load indicators')
            } finally {
                isLoading.value = false
            }
        }
    },
    isNumericTest: {
        onChange: (indicator: any) => {
            indicator.error.numeric_result = ""
            indicator.error.text_result = ""
            indicator.error.result_modifier = ""
            indicator.input.result = ""
            indicator.input.result_modifier = ""
        }
    }
}

async function loadOrders() {
    isLoading.value = true
    try {
        formData.value = {}
        const req = await labService.getTestsWithoutResults()
        req.forEach((order: any) => {
            formData.value[order.id] = {
                result_date: "",
                tests: {},
                error: {
                    result_indicators: "",
                    result_date: ""
                }
            }
            order.tests.forEach((t: any) => {
                formData.value[order.id].tests[t.id] = {
                    selectedIndicators: [],
                    allIndicators: []
                }
            })
        })
        orders.value = req.filter((o: any) => o.tests.some((t: any) => isEmpty(t.result)))
    } catch (e) {
        toastDanger("Error loading orders")
    } finally {
        isLoading.value = false
    }
}

function validateAll(order_id: number, testId: number) {
    let isValid = true
    formModel.result_date.validation(order_id)
    formModel.result_indicators.validation(order_id, testId)
    isValid = Object.keys(formData.value[order_id].error).every((key) => !formData.value[order_id].error[key])

    formData.value[order_id].tests[testId].selectedIndicators.forEach((indicator: any) => {
        formModel.result.validation(indicator)
        formModel.result_modifier.validation(indicator)
        isValid = Object.keys(indicator.error).every((key) => !indicator.error[key])
    })
    return isValid
}

function buildResults(orderId: number, testId: number) {
    return formData.value[orderId].tests[testId].selectedIndicators.map((indicator: any) => formModel.result.buildResult(indicator))
}

async function onSubmitOrder(orderId: number, testId: number) {
    if (!validateAll(orderId, testId)) {
        toastDanger("Please fix the validation errors before submitting")
        return false
    }
    const measures = buildResults(orderId, testId)
    try {
        await labService.createEncounter()
        await labService.createLabResult(measures, testId, formData.value[orderId].result_date)
        orders.value = orders.value.filter((o: any) => o.id !== orderId)
        toastSuccess("Lab results saved successfully")
    } catch (e) {
        console.error(e)
        toastDanger("Error saving lab results")
        return false
    }
    return true
}

onMounted(() => {
    loadOrders()
})
</script>
<style scoped>
ion-label {
    font-weight: bold;
}

ion-note {
    margin: 10px;
    color: rgb(205, 3, 3);
    font-style: italic;
    font-weight: bold;
}
</style>