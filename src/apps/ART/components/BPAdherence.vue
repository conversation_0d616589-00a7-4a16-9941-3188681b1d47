<template>
    <ion-item>
        <h4><b>BP Adherence</b></h4>
    </ion-item>
    <ion-grid class="ion-padding">
        <ion-row>
            <ion-col v-for="(drug, index) in currentDrugs" :key="index">
                <BasicInput required :label="drug.name" v-model="drug.pillsBrought" placeholder="Enter pills remaining"
                    :error-message="!`${drug.value}`.length ? 'Dont forget to update this field' : ''" />
            </ion-col>
        </ion-row>
        <ion-row class="ion-padding">
            <ion-col>
                <ion-button @click="saveAdherence" :disabled="dataSaved || !canSave">Save Adherence</ion-button>
            </ion-col>
        </ion-row>
    </ion-grid>
    <div>
        <ion-item slot="header">
            <ion-label style="font-weight:bold;">Adherence summary</ion-label>
        </ion-item>
        <ion-grid class="ion-padding" v-if="adherenceSummary.length > 0">
            <ion-row class="table-header">
                <ion-col size="3">
                    <ion-label><b>Drug</b></ion-label>
                </ion-col>
                <ion-col size="3">
                    <ion-label><b>Pills Dispensed During Last Visit</b></ion-label>
                </ion-col>
                <ion-col size="3">
                    <ion-label><b>Expected Pills</b></ion-label>
                </ion-col>
                <ion-col size="3">
                    <ion-label><b>Adherence for Drug</b></ion-label>
                </ion-col>
            </ion-row>
            <ion-row v-for="drug in adherenceSummary" :key="drug.name" class="table-row">
                <ion-col size="3">
                    <ion-label>{{ drug.name }}</ion-label>
                </ion-col>
                <ion-col size="3">
                    <ion-label>{{ drug.pillsDispensed || 'N/A' }}</ion-label>
                </ion-col>
                <ion-col size="3">
                    <ion-label>{{ drug.expectedPills || 'N/A' }}</ion-label>
                </ion-col>
                <ion-col size="3">
                    <ion-label :color="getAdherenceColor(drug.adherence)">
                        {{ drug.adherence ? drug.adherence.toFixed(1) + '%' : 'N/A' }}
                    </ion-label>
                </ion-col>
            </ion-row>
        </ion-grid>
        <div v-else>
            <ion-item>
                <ion-label class="ion-text-center">
                    <p>No adherence data available.</p>
                    <p><small>Please enter pill counts above to calculate adherence.</small></p>
                </ion-label>
            </ion-item>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue'
import BasicInput from "@/components/Forms/BasicFormFields/BasicInput.vue";
import { BPManagementService } from "../services/htn_service";
import { useDemographicsStore } from "@/stores/DemographicStore";
import { storeToRefs } from "pinia";
import { toastSuccess } from "@/utils/Alerts";
import {
    IonButton,
    IonLabel,
    IonItem,
    IonGrid,
    IonRow,
    IonCol
} from "@ionic/vue"

interface DrugAdherence {
    name: string;
    pillsDispensed: number | null;
    expectedPills: number | null;
    adherence: number | null;
}
const demographicsStore = useDemographicsStore();
const { patient } = storeToRefs(demographicsStore) as any;

const patientId = patient.value.patientID
const bpManagementService = new BPManagementService(patientId, -1)
const dataSaved = ref(false)
const currentDrugs = ref<any>([])
const adherenceSummary = ref<DrugAdherence[]>([])
const canSave = computed(() => currentDrugs.value.every((data: any) => data.pillsBrought !== null))

const emit = defineEmits(['adherenceSaved'])

const loadCurrentDrugs = async () => {
    const res = await bpManagementService.getCurrentDrugs()
    if (!res) return
    currentDrugs.value = res.drugs.map((drug: any) => ({
        pillsBrought: null,
        name: drug.name,
        obj: drug
    }))
}

const loadAdherenceSummary = async () => {
    adherenceSummary.value = []
    const res = await bpManagementService.getLastDrugs()
    if (!res) return
    Object.keys(res).forEach((name: string) => {
        const data = res[name]
        adherenceSummary.value.push({
            name,
            pillsDispensed: data.value_numeric,
            expectedPills: data.remaining,
            adherence: null
        })
    })
}

const saveAdherence = async () => {
    try {
        const req = currentDrugs.value.map((d: any) => bpManagementService.getAdherence(d.obj.drug_id, Number(d.pillsBrought)))
        await Promise.all(req)
        dataSaved.value = true
        toastSuccess("Adherence has been saved")
        loadAdherenceSummary()
        emit('adherenceSaved')
    } catch (e) {
        dataSaved.value = false
    }
}

const getAdherenceColor = (adherence: number | null): string => {
    const num = Number(adherence)
    if (!num) return 'medium'
    if (num >= 95) return 'success'
    if (num >= 80) return 'warning'
    return 'danger'
}

onMounted(() => {
    loadCurrentDrugs()
    loadAdherenceSummary()
})
</script>
<style scoped>
.table-header {
    background-color: var(--ion-color-light);
    border-bottom: 2px solid var(--ion-color-medium);
}

.table-row {
    border-bottom: 1px solid var(--ion-color-light);
}

.table-row:hover {
    background-color: var(--ion-color-light-tint);
}

.table-header ion-col,
.table-row ion-col {
    padding: 8px;
    text-align: center;
}

.table-header ion-label {
    font-size: 14px;
}

.table-row ion-label {
    font-size: 13px;
}

/* Custom adherence color styling for better visibility */
.table-row ion-label[color="success"] {
    color: #2dd36f !important;
    font-weight: bold;
}

.table-row ion-label[color="warning"] {
    color: #ffc409 !important;
    font-weight: bold;
}

.table-row ion-label[color="danger"] {
    color: #eb445a !important;
    font-weight: bold;
}

.table-row ion-label[color="medium"] {
    color: #92949c !important;
    font-style: italic;
}
</style>