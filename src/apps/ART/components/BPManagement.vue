<template>
    <div class="bp-management-container">
        <ion-item v-if="formData.startedBpDrugs">
            <ion-icon :icon="medicalOutline" slot="start"></ion-icon>
            <ion-label style="padding: 10px;"> Patient already on BP
                drugs
            </ion-label>
        </ion-item>
        <ion-item v-if="formData.hasHypertensionObs && !formData.enrolledIntoProgram">
            <ion-icon :icon="warningOutline" slot="start"></ion-icon>
            <ion-label style="padding: 10px;">
                Client not enrolled in HTN program.
            </ion-label>
        </ion-item>

        <ion-item v-if="!formData.hasHypertensionObs">
            <ion-icon :icon="warningOutline" slot="start"></ion-icon>
            <ion-label style="padding: 10px;">
                BP Diagnosis not done
            </ion-label>
        </ion-item>

        <ion-accordion-group :value="activeAccordion" @ion-change="handleAccordion">
            <!-- Previous BP Readings Section -->
            <ion-accordion value="previousReadingsForm">
                <ion-item slot="header" color="light">
                    <ion-label class="previousLabel">BP Readings</ion-label>
                </ion-item>
                <ion-card class="ion-padding" slot="content">
                    <ion-card-content>
                        <div v-if="previousReadings.length === 0" class="no-data-message">
                            <ion-text>No BP readings available</ion-text>
                        </div>
                        <div v-else class="table-responsive">
                            <table class="bp-readings-table">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Systolic</th>
                                        <th>Diastolic</th>
                                        <th>BP Drugs</th>
                                        <th>Action/Note</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="reading in previousReadings" :key="reading.id">
                                        <td>{{ formatDate(reading.date) }}</td>
                                        <td>
                                            <ion-chip :style="{ backgroundColor: reading.color }">
                                                {{ reading.systolic }} mmHg
                                            </ion-chip>
                                        </td>
                                        <td>
                                            <ion-chip :style="{ backgroundColor: reading.color }">
                                                {{ reading.diastolic }} mmHg
                                            </ion-chip>
                                        </td>
                                        <td>{{ reading.bpDrugs || 'None' }}</td>
                                        <td>{{ reading.actionNote || 'None' }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </ion-card-content>
                </ion-card>
            </ion-accordion>
            <!-- HTN Enrollment Section -->
            <ion-accordion value="htnEnrollmentForm"
                :disabled="!formData.hasHypertensionObs || formData.enrolledIntoProgram || savedSections.htnEnrollment">
                <ion-item slot="header" color="light">
                    <ion-label class="previousLabel">
                        HTN Enrollment
                        <ion-icon v-if="savedSections.htnEnrollment" :icon="checkmarkCircleOutline"
                            class="saved-icon"></ion-icon>
                    </ion-label>
                </ion-item>
                <ion-card class="ion-padding" slot="content">
                    <ion-card-content>
                        <ion-grid>
                            <ion-row>
                                <ion-col size="12">
                                    <BasicDateInput required label="Date of HTN Enrollment"
                                        v-model="formData.htnEnrollmentDate"
                                        @ionChange="formModel.htnEnrollment.onFormUpdate"
                                        :error-message="validationMessages.htnEnrollmentDate"
                                        :disabled="savedSections.htnEnrollment" />
                                </ion-col>
                            </ion-row>
                            <ion-row class="section-save-btn" v-if="!savedSections.htnEnrollment">
                                <ion-col size="12">
                                    <ion-button expand="full" @click="formModel.htnEnrollment.save()"
                                        :disabled="formModel.htnEnrollment.isSaving.value" color="primary" size="small">
                                        <ion-icon :icon="saveOutline" slot="start"></ion-icon>
                                        {{ formModel.htnEnrollment.isSaving.value ? 'Saving...' : 'Save HTN Enrollment'
                                        }}
                                    </ion-button>
                                </ion-col>
                            </ion-row>
                            <ion-row class="section-saved-msg" v-else>
                                <ion-col size="12">
                                    <ion-text>
                                        <ion-icon :icon="checkmarkCircleOutline" slot="start"></ion-icon>
                                        HTN enrollment saved successfully
                                    </ion-text>
                                </ion-col>
                            </ion-row>
                        </ion-grid>
                    </ion-card-content>
                </ion-card>
            </ion-accordion>

            <!-- Actions Taken Section -->
            <ion-accordion value="actionsForm"
                :disabled="!formData.enrolledIntoProgram || !formData.hasHypertensionObs">
                <ion-item slot="header" color="light">
                    <ion-label class="previousLabel">
                        Actions Taken
                        <ion-icon v-if="savedSections.actionsTaken" :icon="checkmarkCircleOutline"
                            class="saved-icon"></ion-icon>
                    </ion-label>
                </ion-item>
                <ion-card class="ion-padding" slot="content">
                    <ion-card-content>
                        <ion-grid>
                            <ion-row v-if="!savedSections.actionsTaken">
                                <ion-col size="12">
                                    <BasicMultiSelect :label="formModel.actionsTaken.label"
                                        @onChange="formModel.actionsTaken.onFormUpdate" v-model="formData.actionsTaken"
                                        :error-message="validationMessages.actionsTaken"
                                        :disabled="savedSections.actionsTaken" />
                                </ion-col>
                            </ion-row>
                            <ion-row class="section-save-btn" v-if="!savedSections.actionsTaken">
                                <ion-col size="12">
                                    <ion-button expand="full" @click="formModel.actionsTaken.save()"
                                        :disabled="formModel.actionsTaken.isSaving.value" color="primary" size="small">
                                        <ion-icon :icon="saveOutline" slot="start"></ion-icon>
                                        {{ formModel.actionsTaken.isSaving.value ? 'Saving...' : 'Save Actions Taken' }}
                                    </ion-button>
                                </ion-col>
                            </ion-row>
                            <ion-row class="section-saved-msg" v-else>
                                <ion-col size="12">
                                    <ion-text>
                                        <ion-icon :icon="checkmarkCircleOutline" slot="start"></ion-icon>
                                        Actions taken saved successfully
                                    </ion-text>
                                </ion-col>
                            </ion-row>
                        </ion-grid>
                    </ion-card-content>
                    <BpPrescription
                        v-if="savedSections.actionsTaken && formData.actionsTaken.some((action) => action.checked && (action.label === 'Start anti hypertensives' || action.label === 'Change drugs'))" />

                    <BPAdherence
                        v-if="savedSections.actionsTaken && formData.actionsTaken.some((action) => action.checked && action.flags?.includes('on_treatment'))" />
                </ion-card>
            </ion-accordion>

            <!-- BP Diagnosis Section -->
            <ion-accordion value="bpDiagnosisForm" :disabled="savedSections.bpDiagnosis">
                <ion-item slot="header" color="light">
                    <ion-label class="previousLabel">
                        BP Diagnosis
                        <ion-icon v-if="savedSections.bpDiagnosis" :icon="checkmarkCircleOutline"
                            class="saved-icon"></ion-icon>
                    </ion-label>
                </ion-item>
                <ion-card class="ion-padding" slot="content">
                    <ion-card-content>
                        <ion-grid>
                            <ion-row>
                                <ion-col size="12">
                                    <BasicYesNoSelect required label="Has BP diagnosis?"
                                        v-model="formData.hasBPDiagnosis"
                                        :error-message="validationMessages.hasBPDiagnosis"
                                        :disabled="savedSections.bpDiagnosis" />
                                </ion-col>
                            </ion-row>
                            <ion-row v-if="formData.hasBPDiagnosis === 'Yes'">
                                <ion-col size="12">
                                    <BasicDateInput required label="Date of BP diagnosis"
                                        v-model="formData.bpDiagnosisDate"
                                        :error-message="validationMessages.bpDiagnosisDate"
                                        :disabled="savedSections.bpDiagnosis" />
                                </ion-col>
                            </ion-row>
                            <ion-row class="section-save-btn" v-if="!savedSections.bpDiagnosis">
                                <ion-col size="12">
                                    <ion-button expand="full" @click="formModel.bpDiagnosis.save()"
                                        :disabled="formModel.bpDiagnosis.isSaving.value" color="primary" size="small">
                                        <ion-icon :icon="saveOutline" slot="start"></ion-icon>
                                        {{ formModel.bpDiagnosis.isSaving.value ? 'Saving...' : 'Save BP Diagnosis' }}
                                    </ion-button>
                                </ion-col>
                            </ion-row>
                            <ion-row class="section-saved-msg" v-else>
                                <ion-col size="12">
                                    <ion-text>
                                        <ion-icon :icon="checkmarkCircleOutline" slot="start"></ion-icon>
                                        BP diagnosis saved successfully
                                    </ion-text>
                                </ion-col>
                            </ion-row>
                        </ion-grid>
                    </ion-card-content>
                </ion-card>
            </ion-accordion>

            <!-- Risk Factors Section -->
            <ion-accordion value="riskFactorsForm" :disabled="savedSections.riskFactors">
                <ion-item slot="header" color="light">
                    <ion-label class="previousLabel">
                        Risk Factors Assessment
                        <ion-icon v-if="savedSections.riskFactors" :icon="checkmarkCircleOutline"
                            class="saved-icon"></ion-icon>
                    </ion-label>
                </ion-item>
                <ion-card class="ion-padding" slot="content">
                    <ion-card-content>
                        <ion-grid>
                            <ion-row>
                                <ion-col size="12">
                                    <BasicMultiSelect :label="formModel.riskFactors.label"
                                        v-model="formData.riskFactors"
                                        @on-change="() => formModel.riskFactors.onFormUpdate()"
                                        :error-message="validationMessages.riskFactors"
                                        :disabled="savedSections.riskFactors" />
                                </ion-col>
                            </ion-row>
                            <ion-row class="section-save-btn" v-if="!savedSections.riskFactors">
                                <ion-col size="12">
                                    <ion-button expand="full" @click="formModel.riskFactors.save()"
                                        :disabled="formModel.riskFactors.isSaving.value" color="primary" size="small">
                                        <ion-icon :icon="saveOutline" slot="start"></ion-icon>
                                        {{ formModel.riskFactors.isSaving.value ? 'Saving...' : 'Save Risk Factors' }}
                                    </ion-button>
                                </ion-col>
                            </ion-row>
                            <ion-row class="section-saved-msg" v-else>
                                <ion-col size="12">
                                    <ion-text>
                                        <ion-icon :icon="checkmarkCircleOutline" slot="start"></ion-icon>
                                        Risk factors saved successfully
                                    </ion-text>
                                </ion-col>
                            </ion-row>
                        </ion-grid>
                    </ion-card-content>
                </ion-card>
            </ion-accordion>

            <!-- Refer to Clinician Section -->
            <ion-accordion value="referralForm" :disabled="!formData.canReferClient || savedSections.referToClinician">
                <ion-item slot="header" color="light">
                    <ion-label class="previousLabel">
                        Referral Decision
                        <ion-icon v-if="savedSections.referToClinician" :icon="checkmarkCircleOutline"
                            class="saved-icon"></ion-icon>
                    </ion-label>
                </ion-item>
                <ion-card class="ion-padding" slot="content">
                    <ion-card-content>
                        <ion-grid>
                            <ion-row>
                                <ion-col size="12">
                                    <BasicYesNoSelect required label="Refer to Clinician?"
                                        v-model="formData.referToClinician"
                                        :error-message="validationMessages.referToClinician"
                                        :disabled="!formData.referToClinician || savedSections.referToClinician" />
                                </ion-col>
                            </ion-row>
                            <ion-row class="section-save-btn" v-if="!savedSections.referToClinician">
                                <ion-col size="12">
                                    <ion-button expand="full" @click="formModel.referToClinician.save()"
                                        :disabled="!formData.referToClinician || formModel.referToClinician.isSaving.value"
                                        color="primary" size="small">
                                        <ion-icon :icon="saveOutline" slot="start"></ion-icon>
                                        {{ formModel.referToClinician.isSaving.value ? 'Saving...'
                                            : 'Save Referral Decision' }}
                                    </ion-button>
                                </ion-col>
                            </ion-row>
                            <ion-row class="section-saved-msg" v-else>
                                <ion-col size="12">
                                    <ion-text>
                                        <ion-icon :icon="checkmarkCircleOutline" slot="start"></ion-icon>
                                        Referral decision saved successfully
                                    </ion-text>
                                </ion-col>
                            </ion-row>
                        </ion-grid>
                    </ion-card-content>
                </ion-card>
            </ion-accordion>
        </ion-accordion-group>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue';
import {
    IonChip,
    IonAccordion,
    IonAccordionGroup,
    IonButton,
    IonCard,
    IonCardContent,
    IonCol,
    IonGrid,
    IonIcon,
    IonItem,
    IonLabel,
    IonRow,
    IonText
} from '@ionic/vue';
import { saveOutline, checkmarkCircleOutline, medicalOutline, warningOutline } from 'ionicons/icons';
import { AppEncounterService } from '@/services/app_encounter_service';
import { toastSuccess, toastDanger, alertConfirmation } from '@/utils/Alerts';
import HisDate from '@/utils/Date';
import { useDemographicsStore } from '@/stores/DemographicStore';
import BasicYesNoSelect from '@/components/Forms/BasicFormFields/BasicYesNoSelect.vue';
import BasicMultiSelect from '@/components/Forms/BasicFormFields/BasicMultiSelect.vue';
import BasicDateInput from '@/components/Forms/BasicFormFields/BasicDateInput.vue';
import { BPManagementService } from '../services/htn_service';
import { storeToRefs } from 'pinia';
import { ConceptService } from '@/services/concept_service';
import { ObservationService } from '@/services/observation_service';
import router from '@/router';
import { modal } from '@/utils/modal';
import { ConsultationService } from '../services/consultation_service';
import { UserService } from '@/services/user_service';
import { ProgramService } from '@/services/program_service';
import { PatientProgramService } from '@/services/patient_program_service';
import BpPrescription from './BpPrescription.vue';
import BPAdherence from './BPAdherence.vue';
import { find } from 'lodash';

const demographicsStore = useDemographicsStore();
const { patient } = storeToRefs(demographicsStore) as any;

const patientId = patient.value.patientID
const bpManagementService = new BPManagementService(patientId, -1)
const consultationService = new ConsultationService(patientId, -1)

// Reactive data
const activeAccordion = ref('previousReadingsForm');

// Track which sections have been saved successfully
const savedSections = reactive({
    riskFactors: false,
    actionsTaken: false,
    referToClinician: false,
    bpDiagnosis: false,
    htnEnrollment: false
});

// Fake BP readings data
const previousReadings = ref<any[]>([]);

// Form data
interface MultiSelectOption {
    label: string;
    value: any;
    checked: boolean;
    disabled?: boolean;
    flags?: string[];
}

const formData = reactive<{
    referToClinician: string;
    riskFactors: MultiSelectOption[];
    actionsTaken: MultiSelectOption[];
    hasBPDiagnosis: string;
    bpDiagnosisDate: string;
    htnEnrollmentDate: string;
    bpDrugs: any[];
    isNormatensive: boolean;
    hasHypertensionObs: boolean;
    canReferClient: boolean;
    startedBpDrugs: boolean;
    enrolledIntoProgram: boolean;
}>({
    enrolledIntoProgram: false,
    referToClinician: '',
    riskFactors: [],
    actionsTaken: [
        {
            label: "Lifestyle advice given",
            value: "Lifestyle changes only",
            checked: false,
            flags: ['action_plan']
        },
        {
            label: "Patient declining BP drugs ",
            value: "Symptomatic but not in treatment",
            checked: false,
            disabled: false,
            flags: ['action_plan']
        },
        {
            label: "Start anti hypertensives",
            value: "On treatment",
            checked: false,
            disabled: false,
            flags: ['action_plan']
        },
        {
            label: "Return to annual screening",
            value: "Alive",
            checked: false,
            disabled: false,
            flags: ['action_plan', 'normatensive']
        },
        {
            label: "Continue current BP drugs",
            value: "on treatment",
            checked: false,
            disabled: false,
            flags: ['on_treatment']
        },
        {
            label: 'Change drugs',
            value: 'on treatment',
            checked: false,
            disabled: false,
            flags: ['on_treatment']
        },
        {
            label: 'Continue without BP Drugs',
            value: 'on treatment',
            checked: false,
            disabled: false,
            flags: ['on_treatment']
        },
        {
            label: "Medication not available",
            value: "on treatment",
            checked: false,
            disabled: false,
            flags: ['on_treatment']
        },
        {
            label: "Review drugs",
            value: "on treatment",
            checked: false,
            disabled: false,
            flags: ['on_treatment']
        }
    ],
    hasBPDiagnosis: '',
    bpDiagnosisDate: '',
    htnEnrollmentDate: '',
    isNormatensive: false,
    bpDrugs: [],
    hasHypertensionObs: false,
    canReferClient: false,
    startedBpDrugs: false
});

// Validation messages
const validationMessages = reactive<Record<string, string>>({
    referToClinician: '',
    riskFactors: '',
    actionsTaken: '',
    hasBPDiagnosis: '',
    bpDiagnosisDate: '',
    htnEnrollmentDate: ''
});

// Form model for validation
const formModel: any = {
    actionsTaken: {
        label: "Actions Taken",
        isSaving: ref(false),
        init: async () => {
            const trail = await bpManagementService.getBPTrail()
            formData.isNormatensive = BPManagementService.isBpNormotensive(trail);
            const colorMap: Record<string, string> = {
                'grade 1': '#FFC3CE',
                'grade 2': '#F20056',
                'grade 3': '#FF3333'
            }
            previousReadings.value = Object.values(trail).map((item: any, id) => {
                formData.bpDrugs = [...formData.bpDrugs, ...item.drugs];
                const grade = BPManagementService.getBpGrade(item.sbp, item.dbp)
                return {
                    id,
                    date: formatDate(item.date),
                    diastolic: item.dbp,
                    systolic: item.sbp,
                    actionNote: item.note,
                    bpDrugs: item.drugs.join(', '),
                    color: colorMap[grade] ?? '#90EE90'
                }
            })
            formModel.actionsTaken.updateActionsToTake()
        },
        onFormUpdate: (value: any) => {
            validationMessages.actionsTaken = '';
            // Check one thing at a time only for people on treatment
            if (formData.bpDrugs.length > 0 && value.checked) {
                formData.actionsTaken = formData.actionsTaken.map((action) => {
                    return { ...action, checked: value.label === action.label };
                });
            }
        },
        updateActionsToTake: () => {
            formData.actionsTaken.forEach((action, index) => {
                const flags = action?.flags ?? []
                if (flags.includes('on_treatment')) {
                    formData.actionsTaken[index].disabled = formData.bpDrugs.length <= 0;
                }
                if (flags.includes('action_plan')) {
                    formData.actionsTaken[index].disabled = formData.bpDrugs.length >= 1;
                }
                if (flags.includes('normatensive')) {
                    formData.actionsTaken[index].disabled = !formData.isNormatensive || formData.bpDrugs.length > 0;
                }
            })
            formData.actionsTaken = formData.actionsTaken.sort((a, b) => {
                if (a.disabled === b.disabled) return 0;
                return a.disabled ? 1 : -1;
            });
        },
        continueCurrentBpDrugs: async () => {
            const res = await bpManagementService.getCurrentDrugs()
            sessionStorage.setItem('BP_DRUGS_PRESCRIBED', JSON.stringify({
                [patientId]: res.drugs.map((selected: any) =>
                    find(BPManagementService.htnDrugReferences(), {
                        'drug_id': selected.drug_id
                    }))
            }))
        },
        save: async () => {
            const checkedItems = formData.actionsTaken.filter((opt) => opt.checked)
            validationMessages.actionsTaken = '';
            if (checkedItems.length <= 0) {
                const message = 'Please select at least one action taken';
                validationMessages.actionsTaken = message;
                toastDanger(message);
                return;
            }
            try {
                formModel.actionsTaken.isSaving.value = true;
                const obs = checkedItems.map((option) => bpManagementService.buildValueText('plan', option.label))
                await bpManagementService.createEncounter()
                await bpManagementService.saveObservationList((await Promise.all(obs)))
                if (formData.bpDrugs.length) {
                    await bpManagementService.enrollPatient({
                        state: checkedItems[0].value
                    })
                }
                if (formData.actionsTaken.some((action) => action.checked && action.label === 'Continue without BP Drugs')) {
                    sessionStorage.removeItem('BP_DRUGS_PRESCRIBED')
                }
                if (formData.actionsTaken.some((action) => action.checked && action.label === 'Continue current BP drugs')) {
                    await formModel.actionsTaken.continueCurrentBpDrugs()
                }
                savedSections.actionsTaken = true;
                toastSuccess("Actions taken have been saved successfully");
            } catch (e) {
                toastDanger("An error has occured while saving observations")
            } finally {
                formModel.actionsTaken.isSaving.value = false;
            }
        }
    },
    referToClinician: {
        label: "Refer to Clinician",
        isSaving: ref(false),
        init: () => {
            formData.canReferClient = !(UserService.isClinician() && UserService.isDoctor())
        },
        save: async () => {
            validationMessages.referToClinician = '';
            if (!formData.referToClinician) {
                const message = 'Please select whether to refer to clinician'
                toastDanger(message);
                validationMessages.referToClinician = message;
                return;
            }
            formModel.referToClinician.isSaving.value = true;
            try {
                const encounter = await bpManagementService.createEncounter()
                if (encounter) {
                    await bpManagementService.saveValueCodedObs("Refer patient to clinician", formData.referToClinician)
                }
                toastSuccess('Referral decision saved successfully');
                savedSections.referToClinician = true;
                if (formData.referToClinician === 'Yes') {
                    modal.hide()
                    router.push('/patientProfile')
                }
            } catch (error) {
                console.error('Error saving referral decision:', error);
                toastDanger('Failed to save referral decision');
            } finally {
                formModel.referToClinician.isSaving.value = false;
            }
        }
    },
    riskFactors: {
        label: "Risk Factors",
        isSaving: ref(false),
        onFormUpdate: () => validationMessages.riskFactors = '',
        init: async () => {
            const concepts = ConceptService.getConceptsByCategory("risk factors");
            const risks = concepts.map(async (concept) => {
                const val = await ObservationService.getFirstValueCoded(patientId, concept.name);
                return {
                    label: concept.name,
                    value: concept.concept_id,
                    checked: `${val}` === 'Yes'
                }
            })
            formData.riskFactors = (await Promise.all(risks)) as any;
        },
        save: async () => {
            formModel.riskFactors.isSaving.value = true;
            validationMessages.riskFactors = ''
            try {
                if (formData.riskFactors.length === 0) {
                    const message = 'Please select altleast one risk factor'
                    validationMessages.riskFactors = message
                    toastDanger(message);
                    return;
                }
                const history = new AppEncounterService(patientId, 30, -1)
                const encounter = await history.createEncounter();
                if (encounter) {
                    const obs: any = formData.riskFactors.map(async (r: any) => {
                        const val = r.checked === true ? 'Yes' : 'No';
                        return {
                            'concept_id': r.value,
                            'value_coded': await ConceptService.getConceptID(val)
                        }
                    });
                    await history.saveObservationList(await Promise.all(obs));
                }
                toastSuccess("Risk assessment has been saved successfully")
                savedSections.riskFactors = true;
            } catch (error) {
                console.error('Error saving risk factors:', error);
                toastDanger('Failed to save risk factors');
            } finally {
                formModel.riskFactors.isSaving.value = false;
            }
        }
    },
    bpDiagnosis: {
        label: "BP Diagnosis",
        isSaving: ref(false),
        init: async () => {
            formData.hasHypertensionObs = await (async () => {
                const val = await ObservationService.getFirstValueCoded(patientId, "Patient has hypertension");
                return /yes|no/i.test(val);
            })()
            formData.startedBpDrugs = await (async () => {
                const val = await ObservationService.getFirstValueText(patientId, "Treatment status");
                return /BP Drugs started/i.test(val);
            })()
        },
        validation: () => {
            if (!formData.hasBPDiagnosis) {
                validationMessages.hasBPDiagnosis = 'Please select if the patient has BP diagnosis';
                return false;
            }
            if (formData.hasBPDiagnosis === 'Yes' && !formData.bpDiagnosisDate) {
                validationMessages.bpDiagnosisDate = 'Please select the date of BP diagnosis';
                return false;
            }
            validationMessages.hasBPDiagnosis = '';
            validationMessages.bpDiagnosisDate = '';
            return true;
        },
        buildDateObs: () => consultationService.buildValueDate("Hypertension diagnosis date", `${formData.bpDiagnosisDate}`),
        buildDiagnosisObs: () => consultationService.buildValueCoded("Patient has hypertension", `${formData.hasBPDiagnosis}`),
        save: async () => {
            if (!formModel.bpDiagnosis.validation()) {
                toastDanger('Please fill in all required BP diagnosis fields');
                return;
            }
            formModel.bpDiagnosis.isSaving.value = true;
            try {
                const encounter = await consultationService.createEncounter();
                if (encounter) {
                    const obs = [formModel.bpDiagnosis.buildDiagnosisObs()]
                    if (formData.hasBPDiagnosis === 'Yes') {
                        obs.push(formModel.bpDiagnosis.buildDateObs())
                    }
                    await consultationService.saveObservationList(await Promise.all(obs));
                    toastSuccess('BP diagnosis saved successfully');
                    savedSections.bpDiagnosis = true;
                    formData.hasHypertensionObs = true;
                }
            } catch (error) {
                console.error('Error saving BP diagnosis:', error);
                toastDanger('Failed to save BP diagnosis');
            } finally {
                formModel.bpDiagnosis.isSaving.value = false;
            }
        }
    },
    htnEnrollment: {
        label: "HTN Enrollment",
        isSaving: ref(false),
        init: async () => {
            const programs: any[] = await ProgramService.getPatientPrograms(patientId);
            formData.enrolledIntoProgram = programs.some((program) => program.program.name === "HYPERTENSION PROGRAM");
        },
        onFormUpdate: () => {
            validationMessages.htnEnrollmentDate = ''
        },
        validation: () => {
            if (!formData.htnEnrollmentDate) {
                validationMessages.htnEnrollmentDate = 'Please select the date of HTN enrollment';
                return false;
            }
            validationMessages.htnEnrollmentDate = '';
            return true;
        },
        save: async () => {
            if (!formModel.htnEnrollment.validation()) {
                toastDanger('Please fill in the HTN enrollment date');
                return;
            }
            formModel.htnEnrollment.isSaving.value = true;
            try {
                const program = new PatientProgramService(patientId)
                program.setProgramId(20) // HTN program ID
                program.setProgramDate(`${formData.htnEnrollmentDate}`)
                program.setStateDate(`${formData.htnEnrollmentDate}`)
                program.setStateId(160) // Alive state
                await program.enrollProgram()
                await program.updateState()
                formData.enrolledIntoProgram = true
            } catch (error) {
                console.error('Error saving HTN enrollment:', error);
                toastDanger('Failed to save HTN enrollment');
            } finally {
                formModel.htnEnrollment.isSaving.value = false;
            }
        }
    }
};

// Methods
const handleAccordion = (e: any) => {
    if (e.srcElement.tagName === 'ION-ACCORDION-GROUP') {
        activeAccordion.value = e.detail.value;
    }
};

function fieldInit() {
    Object.keys(formModel).forEach((i) => {
        if (typeof formModel?.[i]?.init === 'function') {
            formModel[i].init()
        }
    })
}

async function onSubmit() {
    if (!savedSections.actionsTaken) {
        return await alertConfirmation('Are you sure you want to proceed without any action?')
    }
    return true
}

const formatDate = (dateStr: string) => HisDate.toStandardHisDisplayFormat(dateStr);

// Lifecycle
onMounted(() => {
    fieldInit()
});

defineExpose({
    onSubmit
})
</script>

<style scoped>
.bp-management-container {
    padding: 10px;
}

.previousLabel {
    font-weight: bold;
    color: #2c3e50;
}

.no-data-message {
    text-align: center;
    padding: 20px;
    color: #666;
}

.bp-readings-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.bp-readings-table th,
.bp-readings-table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

.bp-readings-table th {
    background-color: #f2f2f2;
    font-weight: bold;
}

.bp-readings-table tr:nth-child(even) {
    background-color: #f9f9f9;
}

.bp-readings-table tr:hover {
    background-color: #f5f5f5;
}

.table-responsive {
    overflow-x: auto;
}

ion-button[disabled] {
    opacity: 0.6;
}

.ion-padding-top {
    padding-top: 20px !important;
}

.section-save-btn {
    margin-top: 15px;
}

.section-saved-msg {
    margin-top: 15px;
    text-align: center;
}

.saved-icon {
    margin-left: 10px;
}

ion-accordion[disabled] {
    opacity: 0.6;
}

ion-accordion[disabled] ion-item {
    pointer-events: none;
}
</style>
