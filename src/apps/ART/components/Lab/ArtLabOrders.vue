<template>
    <div>
        <ion-item lines="none">
            <ion-button :disabled="showOrderForm" @click="createNew">
                <ion-icon :icon="addOutline" slot="start"></ion-icon>
                Order Test
            </ion-button>
            <ion-searchbar v-model="searchFilter" color="light" style="width:45%;" slot="end"></ion-searchbar>
        </ion-item>
        <div style="background:white!important;" v-if="showOrderForm">
            <NewOrder :patientID="patient.patientID" :can-order="canOrder" :onNewOrder="onNewOrder"
                :onClose="() => showOrderForm = false"/>
        </div>
        <div v-if="orders.length === 0" style="border: 1px dashed #ccc; padding: 20px;">
            <h1 class="ion-text-center">
                Lab orders not available at the moment
            </h1>
        </div>
        <table v-else class="modern-table">
            <thead>
                <tr>
                    <th>Lab Test</th>
                    <th>Specimen</th>
                    <th>Accession Number</th>
                    <th>Order Date</th>
                    <th>Result</th>
                    <th>Action</th>
                </tr>
            </thead>
            <tbody>
                <tr v-for="(order, index) in filteredOrders" :key="index">
                    <td>{{ order.test_name }}</td>
                    <td>{{ order.specimen }}</td>
                    <td>{{ order.accession_number }}</td>
                    <td>{{ toDate(order.ordered) }}</td>
                    <td>
                        <span v-for="(d, i) in order.result" :key="i"> {{ d }} <br /></span>
                    </td>
                    <td>
                        <ion-button @click="printOrder(order)" color="light">
                            <ion-icon color="primary" :icon="printOutline" slot="start"></ion-icon>
                            Print
                        </ion-button>
                        <ion-button @click="voidOrder(order)" color="light" class="ion-margin-start">
                            <ion-icon style="color:red;" :icon="trashOutline" slot="start"></ion-icon>
                            Void
                        </ion-button>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</template>
<script lang="ts" setup>
import {
    IonIcon,
    IonItem,
    IonSearchbar,
    IonButton
} from "@ionic/vue"
import { addOutline, printOutline, trashOutline } from "ionicons/icons"
import { computed, ref, watch } from "vue"
import { OrderService } from "@/services/order_service"
import { Order } from "@/interfaces/order"
import { ObservationService } from "@/services/observation_service"
import { ConceptService } from "@/services/concept_service"
import HisDate from "@/utils/Date"
import { Service } from "@/services/service"
import { alertConfirmation, toastDanger, toastSuccess, toastWarning } from "@/utils/Alerts"
import { PatientPrintoutService } from "@/services/patient_printout_service"
import popVoidReason from "@/utils/ActionSheetHelpers/VoidReason"
import { PatientLabService } from "@/services/lab/patient_lab_service"
import { modal } from "@/utils/modal"
import NewOrder from "./NewOrder.vue"
import { useDemographicsStore } from "@/stores/DemographicStore"
import { storeToRefs } from "pinia"

const demographicsStore = useDemographicsStore();
const { patient } = storeToRefs(demographicsStore) as any;

interface TableOrder {
    encounter_id: number;
    order_id: number;
    result_given: string;
    accession_number: string;
    test_name: string;
    specimen: string;
    ordered: string;
    result: any[]; // Use a more specific type if the structure of the result array is known
    resultIds: number[]; // Assuming resultIds is an array of numbers
    released: string;
}
const showOrderForm = ref(false)

const searchFilter = ref()

const orders = ref<TableOrder[]>([])

const filteredOrders = computed(() => {
    if (searchFilter.value) {
        return orders.value.filter((order: any) => {
            return new RegExp(searchFilter.value, 'i').test(JSON.stringify(order))
        })
    }
    return orders.value
})

function toDate(date: string) {
    return HisDate.toStandardHisDisplayFormat(date)
}

async function printOrder(order: TableOrder) {
    if (toDate(order.ordered) != toDate(Service.getSessionDate())) {
        return toastWarning(`Printing order #${order.accession_number} is restricted to today. Consider using BDE mode/set session date to the order date`)
    }
    if ((await alertConfirmation(`Do you want to print order with accession number ${order.accession_number}?`))) {
        const print = new PatientPrintoutService(patient.value.patientID)
        return print.printLabOrderLbl(order.order_id)
    }
}

function voidOrder(order: TableOrder) {
    popVoidReason(async (reason: string) => {
        (new PatientLabService(-1)).voidOrder(order.order_id, reason)
            .then((res: any) => {
                if (!res) {
                    orders.value = orders.value.filter((rdata: any) => rdata.order_id != order.order_id)
                    toastSuccess("Record has been voided successfully!")
                } else {
                    toastDanger("An error has occured while voiding record")
                }
            }).catch((e: any) => {
                toastDanger(`An error has occured while deleting order: ${e}`)
            })
    })
}

const canOrder = (order: any) => {
    const todaysOrders = orders.value.filter((o: any) => toDate(o.ordered) === toDate(Service.getSessionDate()))
    if (todaysOrders.some((o: any) => o.test_name === order.test_name.label)) {
        toastWarning(`You have already ordered ${order.test_name} today`)
        return false
    }
    return true
}

const onNewOrder = (res: any[]) => {
    orders.value.push(...OrderService.formatLabs(res))
}

function createNew() {
    showOrderForm.value = true
}

async function fetchOrders(patientID: number) {
    const res = await OrderService.getOrders(patientID, { patientID: patient.value.patientID })
    const data = res.map(async (order: Order) => {
        const remappedOrder: any = { ...order, 'result_given': false }
        const obsResultID = order.tests.filter(order => order.result != null)
            .map((tests: any) => tests.result)
            .reduce((results: any, result: any) => [...results, ...result], [])
            .reduce((_: any, result: any) => result.id, null)
        try {
            remappedOrder['result_given'] = !obsResultID ? 'N/A'
                : await (await ObservationService.get(obsResultID as number))
                    .children.reduce(async (status: string, obs: any) => {
                        return (await ConceptService.getConceptID('Result Given to Client')) === obs.concept_id
                            && (await ConceptService.getConceptName(obs.value_coded)) === 'Yes'
                            ? 'Yes'
                            : status
                    }, 'No')
        } catch (e) {
            console.error(e)
        }
        return remappedOrder
    })
    orders.value = OrderService.formatLabs((await Promise.all(data)))
}
watch(() => patient.value.patientID, (id: number) => fetchOrders(id), { immediate: true })
</script>
<style scoped>
.modern-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.modern-table th {
    background-color: #f4f4f4;
    color: #333;
    font-weight: bold;
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.modern-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.modern-table tr:last-child td {
    border-bottom: none;
}

.modern-table tr:hover {
    background-color: #f9f9f9;
    transition: background-color 0.3s ease;
}
</style>