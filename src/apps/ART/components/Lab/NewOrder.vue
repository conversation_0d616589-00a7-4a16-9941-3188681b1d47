    <template>
        <ion-grid>
            <ion-row>
                <ion-col>
                    <BasicVueSelect v-model="formData.test_name" required label="Select Test"
                        @on-change="(val) => onTestType(val.label)" :options="testTypes" />
                </ion-col>
                <ion-col>
                    <BasicVueSelect required v-model="formData.specimen" label="Select Specimen"
                        :options="specimenTypes" />
                </ion-col>
                <ion-col>
                    <BasicVueSelect v-model="formData.reason_for_test" required label="Reason for Test"
                        :options="reasonsForTest.map((reason) => ({ label: reason, value: reason }))" />
                </ion-col>
            </ion-row>
            <ion-row>
                <ion-col>
                    <Barcode label="Scan accession number" :status="barcodeStatus" :disabled="disabledBarcode"
                        @code-captured="onScan" />
                </ion-col>
            </ion-row>
            <ion-row>
                <ion-col>
                    <ion-button
                        :disabled="!formData.test_name || !formData.specimen || !formData.reason_for_test || !formData.accessionNumber && !disabledBarcode"
                        @click="saveLabInvestigation">
                        <ion-icon :icon="addOutline" slot="start"></ion-icon>
                        Save
                    </ion-button>
                    <ion-button fill="clear" @click="onClose">
                        <ion-icon :icon="closeCircleOutline" slot="start"></ion-icon>
                        Cancel
                    </ion-button>
                </ion-col>
                <ion-col>
                </ion-col>
            </ion-row>
        </ion-grid>
    </template>
<script lang="ts" setup>
import { IonGrid, IonRow, IonCol, IonIcon, IonButton } from "@ionic/vue"
import { computed, onMounted, ref } from "vue";
import { OrderService } from "@/services/order_service";
import { addOutline, closeCircleOutline } from "ionicons/icons"
import BasicVueSelect from "@/components/Forms/BasicFormFields/BasicVueSelect.vue";
import { modal } from "@/utils/modal";
import { PatientLabService } from "@/services/lab/patient_lab_service";
import { toastDanger, toastSuccess, toastWarning } from "@/utils/Alerts";
import { PropType } from "vue";
import { ConceptService } from "@/services/concept_service";
import Barcode from "@/components/Barcode.vue"
import art_global_props from "../../art_global_props";

const emit = defineEmits(['onNewOrder'])

const props = defineProps({
    patientID: {
        type: Number,
        required: true
    },
    onNewOrder: {
        type: Object as PropType<(order: any) => void>,
        required: false
    },
    canOrder: {
        type: Object as PropType<(order: any) => boolean>,
        required: false
    },
    testFilters: {
        type: Object as PropType<string[]>,
        default: () => []
    },
    onClose: {
        type: Object as PropType<() => void>
    }
})

interface TestType {
    name: string;
    concept_id: number;
}

interface Option {
    label: string;
    value: any;
}

const formData = ref<any>({
    test_name: null as Option | null,
    reason_for_test: '' as string,
    specimen: null as Option | null,
    accessionNumber: null as string | null
})

const disabledBarcode = computed(() => {
    if (canScanAccessionNumber.value) {
        return !(/hiv viral load/i.test(`${formData.value.test_name?.label}`)
            && /dbs|plasma/i.test(`${formData.value.specimen?.label}`)); // Disable for HIV Viral Load with DBS or Plasma    
    }
    return true
})

const canScanAccessionNumber = ref(true)
const barcodeStatus = ref('')
const testTypes = ref<Option[]>([])
const specimenTypes = ref<Option[]>([])
const reasonsForTest = ref<string[]>(['Routine', 'Targeted', 'Confirmatory', 'Stat', 'Repeat / Missing']);
const specimenCache = ref<any>({})

const generalReasonForTests: Record<string, string[]> = {
    general: [
        'Routine',
        'Targeted',
        'Confirmatory',
        'Stat',
        'Repeat / Missing'
    ],
    "HIV viral load": [
        'Routine',
        'Targeted',
        'Confirmatory',
        'Stat',
        'Repeat / Missing',
        'Follow up after Low Level Viremia',
        'Follow up after High Viral Load'
    ]
}

function onTestType(name: string) {
    formData.value.specimen = ''
    updateTestReasons(name)
    setSpecimens(name)
}

function updateTestReasons(testName: string) {
    reasonsForTest.value = generalReasonForTests[testName] ?? generalReasonForTests.general
}

function fetchTestTypes() {
    OrderService.getTestTypes().then((res: TestType[]) => {
        testTypes.value = res.filter(test => props.testFilters.length === 0 || props.testFilters.includes(test.name))
            .map((test: TestType) => ({
                label: test.name,
                value: test.concept_id
            }))
    })
}

async function setSpecimens(testName: string) {
    specimenTypes.value = []
    if (!specimenCache.value[testName]) {
        specimenCache.value[testName] = await OrderService.getSpecimens(testName).then((res: any[]) => {
            return res.map((specimen: any) => ({
                label: specimen.name,
                value: specimen.concept_id
            }))
        })
    }
    specimenTypes.value = specimenCache.value[testName]
}

async function saveLabInvestigation() {
    if (typeof props.canOrder === 'function' && !props.canOrder(formData.value)) {
        return;
    }
    const patientLabService = new PatientLabService(props.patientID)
    try {
        const payload: any = {
            target_lab: PatientLabService.getLocationName(),
            reason_for_test_id: ConceptService.getCachedConceptID(formData.value.reason_for_test, true),
            specimen: {
                concept_id: formData.value.specimen.value
            },
            tests: [
                {
                    concept_id: formData.value.test_name.value
                }
            ],
            combine_tests: true
        }
        if (!disabledBarcode.value && formData.value.accessionNumber) {
            payload.accession_number = formData.value.accessionNumber
        } else {
            payload.accession_number = null // Ensure it's null if not provided

        }
        const res = await patientLabService.placeOrder(payload)
        if (res) {
            if (props.onNewOrder) {
                props.onNewOrder(res)
            }
            emit('onNewOrder', res)
            toastSuccess("Lab investigation saved successfully")
            onClose()
        } else {
            toastDanger("Failed to save lab investigation")
        }
    } catch (e) {
        toastDanger(`Failed to save lab investigation: ${e}`)
    }
}

async function onScan(text: string) {
    barcodeStatus.value = ''
    formData.value.accessionNumber = ''
    /**
       * Verify with API if barcode was already used:
    */
    try {
        if (!(await OrderService.accessionNumExists(text))) {
            formData.value.accessionNumber = text
            barcodeStatus.value = 'Accession# validated successfully'
        } else {
            const status = `Barcode ${formData.value.accessionNumber} was already used`
            toastWarning(status)
            barcodeStatus.value = status

        }
    } catch (e) {
        toastDanger("Failed to confirm barcode " + text + ", Please try again later", 8000)
        barcodeStatus.value = 'Unable to validate barcode'
    }
}

function onClose() {
    if (typeof props.onClose === 'function') {
        props.onClose()
    } else {
        modal.hide()
    }
}

onMounted(() => {
    art_global_props.canScanDBS()
        .then((res) => {
            canScanAccessionNumber.value = res;
        })
    fetchTestTypes()
})
</script>
