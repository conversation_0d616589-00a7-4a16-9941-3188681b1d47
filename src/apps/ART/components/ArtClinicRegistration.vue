<template>
    <ion-card style="margin: 0px auto; width: 80%">
        <ion-card-content>
            <ion-grid>
                <!-- Phone and Home Visit Followup -->
                <ion-row>
                    <ion-col size="12" size-md="6">
                        <BasicYesNoSelect
                            required
                            label="Phone followup?"
                            v-model="formData.phone_followup"
                            @on-change="(val: any) => dataHandler('phone_followup', val)"
                            :error-message="validationMessages['phone_followup']"
                        />
                    </ion-col>
                    <ion-col size="12" size-md="6">
                        <BasicYesNoSelect
                            required
                            label="Home visit followup?"
                            v-model="formData.home_visit_followup"
                            @on-change="(val: any) => dataHandler('home_visit_followup', val)"
                            :error-message="validationMessages['home_visit_followup']"
                        />
                    </ion-col>
                </ion-row>
                <ion-row v-if="formModel.phone_number.required()">
                    <ion-col>
                        <ion-label class="form-label">{{ formModel.phone_number.label }} <span
                                style="color: red">*</span></ion-label>
                        <BasicPhoneInputField :inputHeader="''" :sectionHeaderFontWeight="'20'" :bold="''" :unit="''"
                            :input="'input'" :disabled="false" :icon="phone_properties.icon"
                            :placeholder="'Enter phone'" :iconRight="''" :leftText="''" :inputWidth="'100%'"
                            :inputValue="formData.phone_number" :eventType="'input'" :p_country="currentCountryObj"
                            @update:phone="(e: any) => { formData.phone_number = e; dataHandler('phone_number', e) }"
                            @countryChanged="() => null" :popOverData="phone_properties.popOverData"
                            @handleInnerActionBtnPropetiesFn="$emit('click:innerBtn', phone_properties)"
                            :InnerActionBtnPropeties="phone_properties.InnerBtn" />
                        <ion-note v-if="validationMessages.phone_number" color="danger">
                            {{ validationMessages.phone_number }}
                        </ion-note>
                    </ion-col>
                </ion-row>

                <!-- Has Linkage -->
                <ion-row v-if="formModel.has_linkage.required()">
                    <ion-col>
                        <BasicYesNoSelect
                            required
                            label="Has linkage number?"
                            v-model="formData.has_linkage"
                            @on-change="(val: any) => dataHandler('has_linkage', val)"
                            :error-message="validationMessages['has_linkage']"
                        />
                    </ion-col>
                </ion-row>

                <ion-row v-if="formModel.linkage_number.required()">
                    <ion-col>
                        <ion-label class="form-label">{{ formModel.linkage_number.label }} <span
                                style="color: red">*</span></ion-label>
                        <BasicInputField inputHeader="" placeholder="e.g 1234-002-01-A"
                            :inputValue="formData.linkage_number" @update:inputValue="
                                (e:any) => {
                                    formData.linkage_number = e.target.value;
                                    dataHandler('linkage_number', e.target.value);
                                }
                            " :error="!!validationMessages.linkage_number">
                            <template #end>
                                <ion-label>set_site_prefix</ion-label>
                            </template>
                        </BasicInputField>
                        <ion-note v-if="validationMessages.linkage_number" color="danger">
                            {{ validationMessages.linkage_number }}
                        </ion-note>
                    </ion-col>
                </ion-row>
                <!-- Received ARVs -->
                <ion-row v-if="formModel.received_arvs.required()">
                    <ion-col>
                        <BasicYesNoSelect
                            required
                            label="Received ARVs?"
                            v-model="formData.received_arvs"
                            @on-change="(val: any) => dataHandler('received_arvs', val)"
                            :error-message="validationMessages['received_arvs']"
                        />
                    </ion-col>
                </ion-row>

                <!-- Date last taken ARVs -->
                <ion-row v-if="formModel.date_last_taken_arvs.required()">
                    <ion-col size="12" size-md="6">
                        <ion-label class="form-label">
                            {{ formModel.date_last_taken_arvs.label }}
                            <span style="color: red">*</span>
                        </ion-label>
                        <DatePicker place_holder="" :date_prop="formData.date_last_taken_arvs"
                            @date-up-dated="(value: any) => { formData.date_last_taken_arvs = value; dataHandler('date_last_taken_arvs', value); }"
                            :error="!!validationMessages.date_last_taken_arvs" />
                        <ion-note v-if="validationMessages.date_last_taken_arvs" color="danger">
                            {{ validationMessages.date_last_taken_arvs }}
                        </ion-note>
                    </ion-col>
                </ion-row>

                <!-- Ever registered at ART clinic -->
                <ion-row v-if="formModel.ever_registered_at_art_clinic.required()">
                    <ion-col>
                        <BasicYesNoSelect
                            required
                            label="Ever registered at ART clinic?"
                            v-model="formData.ever_registered_at_art_clinic"
                            @on-change="(val: any) => dataHandler('ever_registered_at_art_clinic', val)"
                            :error-message="validationMessages['ever_registered_at_art_clinic']"
                        />
                    </ion-col>
                </ion-row>

                <!-- Location of ART Initiation -->
                <ion-row v-if="formModel.location_of_art_initialization.required()">
                    <ion-col size="12" size-md="6">
                        <ion-label class="form-label">{{ formModel.location_of_art_initialization.label }} <span
                                style="color: red">*</span></ion-label>
                        <SelectFacility :show_error="!!validationMessages.location_of_art_initialization"
                            :selected_district_ids="[]" :selected_location="formData.location_of_art_initialization"
                            @facilitySelected="(value: any) => { formData.location_of_art_initialization = value.selected_location; dataHandler('location_of_art_initialization', value.selected_location); }" />
                        <ion-note v-if="validationMessages.location_of_art_initialization" color="danger">
                            {{ validationMessages.location_of_art_initialization }}
                        </ion-note>
                    </ion-col>
                    <ion-col v-if="formModel.art_number_at_previous_location.required()" size="12" size-md="6">
                        <ion-label class="form-label">{{ formModel.art_number_at_previous_location.label }} <span
                                style="color: red">*</span></ion-label>
                        <BasicInputField inputHeader="" placeholder="Enter previous ART number"
                            :inputValue="formData.art_number_at_previous_location" @update:inputValue="
                                (e:any) => {
                                    formData.art_number_at_previous_location = e.target.value;
                                    dataHandler('art_number_at_previous_location', e.target.value);
                                }
                            " :error="!!validationMessages.art_number_at_previous_location" />
                        <ion-note v-if="validationMessages.art_number_at_previous_location" color="danger">
                            {{ validationMessages.art_number_at_previous_location }}
                        </ion-note>
                    </ion-col>
                </ion-row>

                <!-- ART Start Date -->
                <ion-row v-if="formModel.art_start_date.required()">
                    <ion-col size="12" size-md="6">
                        <ion-label class="form-label">{{ formModel.art_start_date.label }} <span
                                style="color: red">*</span></ion-label>
                        <DatePicker
                            :place_holder="formModel.art_start_date.label + (formModel.art_start_date.required() ? '*' : '')"
                            :date_prop="formData.art_start_date"
                            @date-up-dated="(value: any) => { formData.art_start_date = value; dataHandler('art_start_date', value); }"
                            :error="!!validationMessages.art_start_date" />
                        <ion-note v-if="validationMessages.art_start_date" color="danger">
                            {{ validationMessages.art_start_date }}
                        </ion-note>
                    </ion-col>
                </ion-row>

                <!-- Has Transfer Letter -->
                <ion-row v-if="formModel.has_transfer_letter.required()">
                    <ion-col>
                        <BasicYesNoSelect
                            required
                            label="Has transfer letter?"
                            v-model="formData.has_transfer_letter"
                            @on-change="(val: any) => dataHandler('has_transfer_letter', val)"
                            :error-message="validationMessages['has_transfer_letter']"
                        />
                    </ion-col>
                </ion-row>

                <!-- Initial Weight and Height -->
                <ion-row>
                    <ion-col size="12" size-md="6" v-if="formModel.initial_weight.required()">
                        <ion-label class="form-label">{{ formModel.initial_weight.label }} <span
                                style="color: red">*</span></ion-label>
                        <BasicInputField inputHeader="" placeholder="1" inputType="number"
                            :inputValue="formData.initial_weight" @update:inputValue="
                                (e:any) => {
                                    formData.initial_weight = e.target.value;
                                    dataHandler('initial_weight', e.target.value);
                                }
                            " :error="!!validationMessages.initial_weight" unit="KG" />
                        <ion-note v-if="validationMessages.initial_weight" color="danger">
                            {{ validationMessages.initial_weight }}
                        </ion-note>
                    </ion-col>
                    <ion-col size="12" size-md="6" v-if="formModel.initial_height.required()">
                        <ion-label class="form-label">{{ formModel.initial_height.label }} <span
                                style="color: red">*</span></ion-label>
                        <BasicInputField inputHeader="" placeholder="1" inputType="number"
                            :inputValue="formData.initial_height" @update:inputValue="
                                (e:any) => {
                                    formData.initial_height = e.target.value;
                                    dataHandler('initial_height', e.target.value);
                                }
                            " :error="!!validationMessages.initial_height" unit="CM" />
                        <ion-note v-if="validationMessages.initial_height" color="danger">
                            {{ validationMessages.initial_height }}
                        </ion-note>
                    </ion-col>
                </ion-row>

                <ion-row v-if="formData.has_transfer_letter === 'Yes'">
                    <ion-col>
                        <Staging :key="formData.art_start_date" :date="formData.art_start_date" ref="stagingRef" />
                    </ion-col>
                </ion-row>

                <!-- CD4 Available -->
                <ion-row v-if="formModel.cd4_available.required()">
                    <ion-col>
                        <BasicYesNoSelect
                            required
                            label="CD4 available?"
                            v-model="formData.cd4_available"
                            @on-change="(val: any) => dataHandler('cd4_available', val)"
                            :error-message="validationMessages['cd4_available']"
                        />
                    </ion-col>
                </ion-row>

                <!-- CD4 Percent -->
                <ion-row v-if="formModel.cd4_percent.required()">
                    <ion-col>
                        <ion-label class="form-label">{{ formModel.cd4_percent.label }} <span
                                style="color: red">*</span></ion-label>
                        <BasicInputField inputHeader="" placeholder="Enter CD4 percent" inputType="number"
                            :inputValue="formData.cd4_percent" @update:inputValue="
                                (e:any) => {
                                    formData.cd4_percent = e.target.value;
                                    dataHandler('cd4_percent', e.target.value);
                                }
                            " :error="!!validationMessages.cd4_percent" unit="%" />
                        <ion-note v-if="validationMessages.cd4_percent" color="danger">
                            {{ validationMessages.cd4_percent }}
                        </ion-note>
                    </ion-col>
                </ion-row>

                <!-- Confirmatory HIV Test Type -->
                <ion-row v-if="formModel.confirmatory_hiv_test_type.required()">
                    <ion-col>
                        <BasicRadioSelect
                            required
                            label="Confirmatory hiv test type"
                            v-model="formData.confirmatory_hiv_test_type"
                            :options="formModel.confirmatory_hiv_test_type.options()"
                            :disable-value="formModel.confirmatory_hiv_test_type.disableOption()"
                            @update:modelValue="(val: any) => dataHandler('confirmatory_hiv_test_type', val)"
                            :error-message="validationMessages['confirmatory_hiv_test_type']"
                        />
                    </ion-col>
                </ion-row>

                <!-- Confirmatory HIV Test Location -->
                <ion-row v-if="formModel.confirmatory_hiv_test_location.required()">
                    <ion-col size="12" size-md="6">
                        <ion-label class="form-label">{{ formModel.confirmatory_hiv_test_location.label }} <span
                                style="color: red">*</span></ion-label>
                        <SelectFacility :show_error="!!validationMessages.confirmatory_hiv_test_location"
                            :selected_district_ids="[]" :selected_location="formData.confirmatory_hiv_test_location"
                            @facilitySelected="(value: any) => { formData.confirmatory_hiv_test_location = value.selected_location; dataHandler('confirmatory_hiv_test_location', value.selected_location); }" />
                        <ion-note v-if="validationMessages.confirmatory_hiv_test_location" color="danger">
                            {{ validationMessages.confirmatory_hiv_test_location }}
                        </ion-note>
                    </ion-col>
                    <ion-col v-if="formModel.confirmatory_hiv_test_date.required()" size="12" size-md="6">
                        <ion-label class="form-label">{{ formModel.confirmatory_hiv_test_date.label }} <span
                                style="color: red">*</span></ion-label>
                        <DatePicker
                            :place_holder="formModel.confirmatory_hiv_test_date.label + (formModel.confirmatory_hiv_test_date.required() ? '*' : '')"
                            :date_prop="formData.confirmatory_hiv_test_date"
                            @date-up-dated="(value: any) => { formData.confirmatory_hiv_test_date = value; dataHandler('confirmatory_hiv_test_date', value); }"
                            :error="!!validationMessages.confirmatory_hiv_test_date" />
                        <ion-note v-if="validationMessages.confirmatory_hiv_test_date" color="danger">
                            {{ validationMessages.confirmatory_hiv_test_date }}
                        </ion-note>
                    </ion-col>
                </ion-row>
            </ion-grid>
        </ion-card-content>
    </ion-card>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import { ARTClinicRegistrationService } from "../services/art_clinic_registration_service";
import { PatientService } from "@/services/patient_service";
import { validateScanFormLinkageCode } from "@/utils/Damm";
import { getFacilities } from "@/utils/HisFormHelpers/LocationFieldOptions";
import { toastDanger, toastSuccess, toastWarning } from "@/utils/Alerts";
import BasicInputField from "@/components/BasicInputField.vue";
import DatePicker from "@/components/DatePicker.vue";
import SelectFacility from "@/apps/OPD/components/SelectFacility.vue";
import { IonCard, IonCardContent, IonGrid, IonRow, IonCol, IonLabel, IonRadioGroup, IonRadio, IonItem, IonNote } from "@ionic/vue";
import Staging from "./Staging.vue";
import StandardValidations from "@/validations/StandardValidations";
import { icons } from "@/utils/svg";
import BasicPhoneInputField from "@/components/BasicPhoneInputField.vue";
import { PatientRegistrationService } from "@/services/patient_registration_service";
import { useDemographicsStore } from "@/stores/DemographicStore";
import { savePatientRecord } from "@/services/offline_service";
import dayjs from "dayjs";
import { infoActionSheet } from "@/utils/ActionSheets";
import { VitalsService } from "@/services/vitals_service";
import BasicYesNoSelect from "@/components/Forms/BasicFormFields/BasicYesNoSelect.vue";
import BasicRadioSelect from "@/components/Forms/BasicFormFields/BasicRadioSelect.vue";

const patient = new PatientService();
const service = new ARTClinicRegistrationService(patient.getID(), -1);
const stagingRef = ref();

const formData = ref<any>({
    phone_followup: "" as string,
    phone_number: "" as string,
    home_visit_followup: "" as string,
    has_linkage: "" as string,
    linkage_number: "" as string,
    received_arvs: "" as string,
    date_last_taken_arvs: "" as string,
    taken_last_two_months: "" as string,
    taken_last_two_weeks: "" as string,
    ever_registered_at_art_clinic: "" as string,
    location_of_art_initialization: "" as string,
    art_number_at_previous_location: "" as string,
    confirmatory_hiv_test_type: "" as string,
    confirmatory_hiv_test_location: "" as string,
    confirmatory_hiv_test_date: "" as string,
    has_transfer_letter: "" as string,
    art_start_date: "" as string,
    initial_weight: "" as string,
    initial_height: "" as string,
    cd4_available: "" as string,
    cd4_percent: "" as string,
});

const facilities = ref<any>([]);
const validationMessages = ref<any>({});
const currentCountryObj = ref<any>([{ dialCode: "265", iso2: "MW", name: "Malawi" }]) as any;
const phone_properties = ref({
    inputHeader: "Phone number *",
    icon: icons.phone,
    value: "",
    name: "phoneNumber",
    InnerBtn: "" as any,
    popOverData: {
        filterData: false,
        data: [],
    },
});

const formModel: any = {
    phone_followup: {
        label: "Phone followup",
        required: () => true,
        buildObs: () => [service.buildValueCoded("Phone", formData.value.phone_followup), service.buildValueCoded("Agrees to followup", "Phone")],
        validation: () => hasValue("phone_followup"),
    },
    phone_number: {
        required: () => formData.value.phone_followup === "Yes" && (!patient.getPhoneNumber() || /unknown|n\/a/i.test(`${patient.getPhoneNumber()}`)),
        label: "Phone number",
        onSubmit: async () => {
            try {
                // Format the phone number with country code
                const formattedPhoneNumber = `+${currentCountryObj.value[0].dialCode}${formData.value.phone_number}`;

                // Update via API
                const personService = new PatientRegistrationService();
                personService.setPersonID(patient.getID());
                await personService.updatePerson({
                    cell_phone_number: formattedPhoneNumber,
                });

                // Update local patient object for caching
                const demographicsStore = useDemographicsStore();
                const patientData = demographicsStore.getPatient();
                if (patientData && patientData.personInformation) {
                    patientData.personInformation.cell_phone_number = formattedPhoneNumber;

                    // Save to offline storage using the caching system
                    await savePatientRecord(patientData);
                }

                toastSuccess("Phone number updated successfully");
            } catch (error) {
                console.error("Error updating phone number:", error);
                toastDanger("Failed to update phone number");
            }
        },
        formatValue: () => `+${currentCountryObj.value[0].dialCode}${formData.value.phone_number}`,
        onFormUpdate: (field: string, value: any) => {
            if (field === "phone_followup" && value === "No") {
                formData.value.phone_number = "";
                validationMessages.value["phone_number"] = "";
            }
        },
        validation: () => {
            validationMessages.value["phone_number"] = "";
            const required = formModel.phone_number.required();
            if (required && !formData.value.phone_number) {
                validationMessages.value["phone_number"] = "Phone number is required";
                return;
            }
            const error = StandardValidations.isMWPhoneNumber(formModel.phone_number.formatValue());
            if (required && typeof error === "string" && error.length > 0) {
                validationMessages.value["phone_number"] = "Not a valid Malawi phone number";
            }
        },
    },
    home_visit_followup: {
        label: "Home visit followup",
        required: () => true,
        buildObs: () => [
            service.buildValueCoded("Home visit", formData.value.home_visit_followup),
            service.buildValueCoded("Agrees to followup", "Home visit"),
        ],
        validation: () => hasValue("home_visit_followup"),
    },
    has_linkage: {
        label: "Has linkage number",
        required: () => true,
        validation: () => hasValue("has_linkage"),
    },
    linkage_number: {
        label: "Linkage number",
        required: () => formData.value.has_linkage === "Yes",
        onFormUpdate: (field: string, value: any) => {
            if (field === "has_linkage" && value === "Yes") {
                formData.value.linkage_number = "";
                validationMessages.value["linkage_number"] = "";
            }
        },
        buildObs: () => service.buildValueText("HTC Serial number", formData.value.linkage_number),
        validation: () => {
            validationMessages.value["linkage_number"] = "";
            if (hasValue("linkage_number")) {
                try {
                    if (!validateScanFormLinkageCode(formData.value.linkage_number)) {
                        validationMessages.value["linkage_number"] = "Invalid linkage number";
                    }
                } catch (e) {
                    validationMessages.value["linkage_number"] = 'Invalid linkage number. please follow format "1234-002-01-A" ';
                }
            }
        },
    },
    received_arvs: {
        label: "Received ARVs",
        required: () => true,
        buildObs: () => service.buildValueCoded("Ever received ART", formData.value.received_arvs),
        validation: () => hasValue("received_arvs"),
    },
    date_last_taken_arvs: {
        label: "Date last taken ARVs",
        required: () => formData.value.received_arvs === "Yes",
        onFormUpdate: (field: string, value: any) => {
            if (field === "received_arvs" && value === "No") {
                formData.value.date_last_taken_arvs = "";
                validationMessages.value["date_last_taken_arvs"] = "";
            }
        },
        validation: () => {
            validationMessages.value["date_last_taken_arvs"] = "";
            if (hasValue("date_last_taken_arvs")) {
                if (!dateWithinRange("date_last_taken_arvs")) return;
                if (formData.value.art_start_date && formData.value.date_last_taken_arvs) {
                    if (
                        dayjs(formData.value.date_last_taken_arvs.standardDate).isBefore(dayjs(formData.value.art_start_date.standardDate))
                    ) {
                        validationMessages.value["date_last_taken_arvs"] = "Date last taken ARVs cannot be before ART start date";
                    }
                }
            }
        },
        buildObs: () => service.buildValueDate("Date ART last taken", formData.value.date_last_taken_arvs.standardDate),
    },
    ever_registered_at_art_clinic: {
        label: "Ever registered at ART clinic",
        required: () => formData.value.received_arvs === "Yes",
        onFormUpdate: (field: string, value: any) => {
            if (field === "received_arvs" && value === "No") {
                formData.value.ever_registered_at_art_clinic = "";
                validationMessages.value["ever_registered_at_art_clinic"] = "";
            }
        },
        validation: () => hasValue("ever_registered_at_art_clinic"),
        buildObs: () => service.buildValueCoded("Ever registered at ART clinic", formData.value.ever_registered_at_art_clinic),
    },
    location_of_art_initialization: {
        label: "Location of ART initialization",
        required: () => formData.value.ever_registered_at_art_clinic === "Yes",
        onFormUpdate: (field: string, value: any) => {
            if ((field === "received_arvs" && value === "No") || (field === "ever_registered_at_art_clinic" && value === "No")) {
                formData.value.location_of_art_initialization = "";
                validationMessages.value["location_of_art_initialization"] = "";
            }
        },
        buildObs: () =>
            service.buildValueText(
                "Location of ART initialization",
                formData.value.location_of_art_initialization?.name ?? formData.value.location_of_art_initialization
            ),
        validation: () => hasValue("location_of_art_initialization"),
        searchFacilities: (q: any) => getFacilities(q).then((res) => (facilities.value = res)),
    },
    art_start_date: {
        label: "ART start date",
        required: () => formData.value.ever_registered_at_art_clinic === "Yes",
        onFormUpdate: (field: string, value: any) => {
            if ((field === "received_arvs" && value === "No") || (field === "ever_registered_at_art_clinic" && value === "No")) {
                formData.value.art_start_date = "";
                validationMessages.value["art_start_date"] = "";
            }
        },
        buildObs: () => buildDateObs("Date ART started", formData.value.art_start_date.standardDate, false),
        validation: () => {
            validationMessages.value["art_start_date"] = "";
            if (hasValue("art_start_date")) {
                if (!dateWithinRange("art_start_date")) return;
            }
        },
    },
    art_number_at_previous_location: {
        label: "ART number at previous location",
        required: () => formData.value.ever_registered_at_art_clinic === "Yes",
        onFormUpdate: (field: string, value: any) => {
            if ((field === "received_arvs" && value === "No") || (field === "ever_registered_at_art_clinic" && value === "No")) {
                formData.value.art_number_at_previous_location = "";
                validationMessages.value["art_number_at_previous_location"] = "";
            }
        },
        validation: () => hasValue("art_number_at_previous_location"),
        buildObs: () =>
            service.buildValueText(
                "ART number at previous location",
                formData.value.art_number_at_previous_location?.name ?? formData.value.art_number_at_previous_location
            ),
    },
    has_transfer_letter: {
        required: () => formData.value.ever_registered_at_art_clinic === "Yes",
        onFormUpdate: (field: string, value: any) => {
            if ((field === "received_arvs" && value === "No") || (field === "ever_registered_at_art_clinic" && value === "No")) {
                formData.value.has_transfer_letter = "";
                validationMessages.value["has_transfer_letter"] = "";
            }
        },
        label: "Has stage information?",
        validation: () => hasValue("has_transfer_letter"),
        buildObs: () => service.buildValueCoded("Has transfer letter", formData.value.has_transfer_letter),
    },
    initial_height: {
        label: "Initial height",
        required: () => formData.value.has_transfer_letter === "Yes",
        onFormUpdate: (field: string, value: any) => {
            if ((field === "received_arvs" && value === "No") || (field === "has_transfer_letter" && value === "No")) {
                formData.value.initial_height = "";
                validationMessages.value["initial_height"] = "";
            }
        },
        buildVitalsObs: (vitalsService: VitalsService) => vitalsService.buildValueNumber("Height", formData.value.initial_height),
        validation: () => {
            validationMessages.value["initial_height"] = "";
            if (hasValue("initial_height")) {
                if (!/^\d+$/i.test(`${formData.value.initial_height}`)) {
                    validationMessages.value["initial_height"] = "Initial height must be a number";
                }
                if (parseInt(formData.value.initial_height) < 40) {
                    validationMessages.value["initial_height"] = "Initial height cannot be less than 40";
                }
                if (parseInt(formData.value.initial_height) > 220) {
                    validationMessages.value["initial_height"] = "Initial height cannot be greater than 220";
                }
            }
        },
    },
    initial_weight: {
        label: "Initial weight",
        required: () => formData.value.has_transfer_letter === "Yes",
        onFormUpdate: (field: string, value: any) => {
            if ((field === "received_arvs" && value === "No") || (field === "has_transfer_letter" && value === "No")) {
                formData.value.initial_weight = "";
                validationMessages.value["initial_weight"] = "";
            }
        },
        buildVitalsObs: (vitalsService: VitalsService) => vitalsService.buildValueNumber("Weight", formData.value.initial_weight),
        validation: () => {
            validationMessages.value["initial_weight"] = "";
            if (hasValue("initial_weight")) {
                if (!/^\d{1,3}\.\d{1}$/i.test(`${formData.value.initial_weight}`)) {
                    validationMessages.value["initial_weight"] = "Initial weight must be a valid decimal number.. i.e. 40.0";
                }
                if (parseInt(formData.value.initial_weight) < 0.5) {
                    validationMessages.value["initial_weight"] = "Initial weight cannot be less than 0.5";
                }
                if (parseInt(formData.value.initial_weight) > 250) {
                    validationMessages.value["initial_weight"] = "Initial weight cannot be greater than 250";
                }
            }
        },
    },
    vitals: {
        onSubmit: async () => {
            if (formModel.initial_height.required() && formModel.initial_weight.required()) {
                const vitalsService = new VitalsService(patient.getID(), -1);
                vitalsService.setDate(formData.value.art_start_date.standardDate);
                const obs = [formModel.initial_height.buildVitalsObs(vitalsService), formModel.initial_weight.buildVitalsObs(vitalsService)];
                await vitalsService.createEncounter();
                await vitalsService.saveObservationList(await Promise.all(obs));
            }
        },
    },
    cd4_available: {
        required: () => formData.value.has_transfer_letter === "Yes",
        onFormUpdate: (field: string, value: any) => {
            if ((field === "received_arvs" && value === "No") || (field === "has_transfer_letter" && value === "No")) {
                formData.value.cd4_available = "";
                validationMessages.value["cd4_available"] = "";
            }
        },
        label: "CD4 available",
        validation: () => hasValue("cd4_available"),
    },
    cd4_percent: {
        required: () => formData.value.cd4_available === "Yes",
        onFormUpdate: (field: string, value: any) => {
            if ((field === "received_arvs" && value === "No") || (field === "cd4_available" && value === "No")) {
                formData.value.cd4_percent = "";
                validationMessages.value["cd4_percent"] = "";
            }
        },
        label: "CD4 percent",
        buildObs: () => service.buildValueNumber("CD4 percent", parseInt(formData.value.cd4_percent.substring(1)), "%" as any),
        validation: () => {
            validationMessages.value["cd4_percent"] = "";
            if (hasValue("cd4_percent")) {
                if (!/^\d+$/i.test(`${formData.value.cd4_percent}`)) {
                    validationMessages.value["cd4_percent"] = "CD4 percent must be a number";
                }
                if (parseInt(formData.value.cd4_percent) < 0) {
                    validationMessages.value["cd4_percent"] = "CD4 percent cannot be less than 0";
                }
                if (parseInt(formData.value.cd4_percent) > 100) {
                    validationMessages.value["cd4_percent"] = "CD4 percent cannot be greater than 100";
                }
            }
        },
    },
    confirmatory_hiv_test_type: {
        label: "Confirmatory hiv test type",
        required: () => true,
        options: () => [
            { label: "Rapid antibody test", value: "HIV rapid test" },
            { label: "DNA PCR", value: "HIV DNA polymerase chain reaction" },
            { label: "Not done", value: "Not done" },
        ],
        disableOption: () => (formData.value.has_linkage === "Yes" ? "Not done" : ""),
        buildObs: () => service.buildValueCoded("Confirmatory hiv test type", formData.value.confirmatory_hiv_test_type),
        validation: () => {
            validationMessages.value["confirmatory_hiv_test_type"] = "";
            if (hasValue("confirmatory_hiv_test_type")) {
                if (formData.value.confirmatory_hiv_test_type === "Not done" && formData.value.has_linkage === "Yes") {
                    validationMessages.value["confirmatory_hiv_test_type"] = "Linkage number detected, please select a confirmatory hiv test type";
                }
            }
        },
    },
    confirmatory_hiv_test_location: {
        label: "Confirmatory hiv test location",
        required: () => formData.value.confirmatory_hiv_test_type && formData.value.confirmatory_hiv_test_type !== "Not done",
        onFormUpdate: (field: string, value: any) => {
            if (field === "confirmatory_hiv_test_type" && value === "Not done") {
                formData.value.confirmatory_hiv_test_location = "";
                validationMessages.value["confirmatory_hiv_test_location"] = "";
            }
        },
        validation: () => hasValue("confirmatory_hiv_test_location"),
        searchFacilities: (q: any) => getFacilities(q).then((res) => (facilities.value = res)),
        buildObs: () =>
            service.buildValueText(
                "Confirmatory hiv test location",
                formData.value.confirmatory_hiv_test_location?.name ?? formData.value.confirmatory_hiv_test_location
            ),
    },
    confirmatory_hiv_test_date: {
        label: "Confirmatory hiv test date",
        required: () => formData.value.confirmatory_hiv_test_type && formData.value.confirmatory_hiv_test_type !== "Not done",
        confirmFirmWithArtStartDateValue: () => {
            validationMessages.value["confirmatory_hiv_test_date"] = "";
            if (!formModel.art_start_date.required()) {
                return true;
            }
            if (
                formData.value.confirmatory_hiv_test_date?.standardDate &&
                dayjs(formData.value.confirmatory_hiv_test_date.standardDate).isAfter(dayjs(formData.value.art_start_date.standardDate))
            ) {
                validationMessages.value["confirmatory_hiv_test_date"] = "Confirmatory hiv test date cannot be after ART start date";
                return false;
            }
            return true;
        },
        onFormUpdate: (field: string, value: any) => {
            if (field === "confirmatory_hiv_test_type" && value === "Not done") {
                formData.value.confirmatory_hiv_test_date = "";
                validationMessages.value["confirmatory_hiv_test_date"] = "";
            }
            if (field === "confirmatory_hiv_test_date") {
                const dateInput = value?.value?.standardDate;
                if (dateInput) {
                    // Check if date is more than 20 days before art start date
                    const artStartDate = formData.value.art_start_date?.standardDate ?? service.getDate();
                    const timeElapsed = dayjs(artStartDate).diff(dateInput, "days");
                    if (timeElapsed >= 20) {
                        infoActionSheet(
                            "Data inconsistency warning",
                            `Confirmatory Date for newly initiated ART patient is ${timeElapsed} days ago`,
                            "Are you sure this is accurate?",
                            [
                                { name: "No, Re-enter date", slot: "start", color: "success" },
                                { name: "Yes, its accurate", slot: "end", color: "danger" },
                            ]
                        ).then((action) => {
                            if (action === "No, Re-enter date") {
                                formData.value.confirmatory_hiv_test_date = {};
                                validationMessages.value["confirmatory_hiv_test_date"] = "Re-enter a valid confirmatory date";
                            }
                        });
                    }
                }
            }
            if (field === "art_start_date" || field === "ever_registered_at_art_clinic") {
                formModel.confirmatory_hiv_test_date.confirmFirmWithArtStartDateValue();
            }
        },
        buildObs: () => buildDateObs("Confirmatory hiv test date", formData.value.confirmatory_hiv_test_date.standardDate, false),
        validation: () => {
            validationMessages.value["confirmatory_hiv_test_date"] = "";
            if (hasValue("confirmatory_hiv_test_date")) {
                if (!dateWithinRange("confirmatory_hiv_test_date")) return;
                if (!formModel.confirmatory_hiv_test_date.confirmFirmWithArtStartDateValue()) return;
            }
        },
    },
};

function buildObs() {
    return Object.keys(formModel).reduce((a: any, c: any) => {
        if (c in formModel && typeof formModel[c].required === "function" && formModel[c].required() && typeof formModel[c].buildObs === "function") {
            const obs = formModel[c].buildObs();
            if (Array.isArray(obs)) {
                return [...a, ...obs];
            }
            return [...a, obs];
        }
        return a;
    }, []);
}

function dataHandler(field: string, value: any) {
    runValidation(field);
    Object.keys(formModel).forEach((k) => formModel[k]?.onFormUpdate && formModel[k].onFormUpdate(field, value));
}

function hasValue(field: string) {
    validationMessages.value[field] = "";
    const fieldMeta = formModel[field];
    const required = fieldMeta?.required ? fieldMeta.required() : false;
    if (required && !formData.value[field]) {
        validationMessages.value[field] = "This field is required";
        return false;
    }
    return required;
}

function dateWithinRange(field: string) {
    validationMessages.value[field] = "";
    const dateValue = typeof formData.value[field] === "string" ? formData.value[field] : formData.value[field]?.standardDate;
    const date = dayjs(dateValue);
    const curDate = dayjs(PatientService.getSessionDate());

    if (date.isAfter(curDate)) {
        validationMessages.value[field] = `${formModel[field].label} cannot be in the future`;
        return false;
    }

    if (date.isBefore(dayjs(patient.getBirthdate()))) {
        validationMessages.value[field] = `${formModel[field].label} cannot be before birthdate`;
        return false;
    }

    return true;
}

function buildDateObs(conceptName: string, date: string, isEstimate: boolean) {
    let obs = {};
    if (date.match(/unknown/i)) {
        obs = service.buildValueText(conceptName, "Unknown");
    } else if (isEstimate) {
        obs = service.buildValueDateEstimated(conceptName, date);
    } else {
        obs = service.buildValueDate(conceptName, date);
    }
    return obs;
}

function runValidation(field: string) {
    validationMessages.value[field] = "";
    if (typeof formModel[field].required === "function") {
        const required = formModel[field].required();
        if (required && field in formData.value && !formData.value[field]) {
            validationMessages.value[field] = "This field is required";
            return;
        }
    }
    if (typeof formModel[field].validation === "function") {
        formModel[field].validation();
    }
}

function validateAll() {
    Object.keys(formModel).forEach((key: string) => runValidation(key));
    return Object.keys(validationMessages.value).every((key) => `${validationMessages.value[key]}`.length <= 0);
}

function runModelonSubmitFunctions() {
    const res = Object.keys(formModel)
        .filter((key: string) => typeof formModel[key].onSubmit === "function")
        .map((key: string) => formModel[key].onSubmit && formModel[key].onSubmit());
    return Promise.all(res);
}

async function onSubmit() {
    const stagingOk = (() => {
        if (formData.value.has_transfer_letter === "Yes") {
            return stagingRef.value.validateStagingForm();
        }
        return true;
    })();
    const registrationOk = validateAll();
    if (!stagingOk || !registrationOk) {
        toastWarning("Please review form for errors");
        return false;
    }
    try {
        await service.createEncounter();
        const obs = await Promise.all(buildObs());
        await service.onSubmit(obs);
        await runModelonSubmitFunctions();
        if (formData.value.has_transfer_letter === "Yes") {
            if (!(await stagingRef.value.onSubmit())) {
                toastDanger("Unable to save Staging information!");
                return false;
            }
        }
        toastSuccess("ART Clinic Registration saved successfully");
        return true;
    } catch (e) {
        console.error(e);
        toastDanger("Error has occured while saving observations");
        return false;
    }
}

defineExpose({
    onSubmit,
});
</script>

<style scoped>
.form-label {
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    display: block;
}

.form-label span {
    color: #eb445a;
}

ion-item {
    --padding-start: 0;
    --inner-padding-end: 0;
}

ion-radio {
    margin-right: 8px;
}

ion-note {
    padding: 12px;
    border-radius: 8px;
    margin-bottom: 16px;
    display: block;
    color: #eb445a !important;
    font-weight: bold;
    font-style: italic;
}
</style>
