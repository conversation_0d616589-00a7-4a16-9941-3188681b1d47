<template>
    <div class="ion-padding">
        <ion-accordion-group :value="activeAccordion">
            <ion-accordion value="change-outcome">
                <ion-item slot="header">
                    <ion-label style="font-weight: bold;font-size: 1.2em;">Change outcome</ion-label>
                </ion-item>
                <div slot="content">
                    <ion-card>
                        <ion-card-content>
                            <!-- Error Message -->
                            <ion-note v-if="errorMessage" style="color: red;font-weight: bold;font-style:italic;"
                                color="danger" class="ion-margin-bottom">
                                Error: {{ errorMessage }}
                            </ion-note>
                            <!-- Program State Form -->
                            <ion-grid>
                                <ion-row>
                                    <ion-col>
                                        <ion-item>
                                            <ion-label position="stacked">Program State *</ion-label>
                                            <ion-select @ion-change="() => validateAll" v-model="formData.programState"
                                                placeholder="Select program state" interface="popover"
                                                :disabled="isLoading">
                                                <ion-select-option v-for="state in displayOutcomeOptions"
                                                    :key="state.value" :value="state.label">
                                                    {{ state.label }}
                                                </ion-select-option>
                                            </ion-select>
                                        </ion-item>
                                    </ion-col>
                                </ion-row>
                                <ion-row v-if="formData.programState">
                                    <ion-col size="12">
                                        <ion-label position="stacked">Start Date</ion-label>
                                        <DatePicker place_holder="Start date" :date_prop="computedStartDate"
                                            @date-up-dated="(value: any) => { formData.startDate = value; validateAll(); }" />
                                    </ion-col>
                                </ion-row>
                                <ion-row v-if="formData.programState === 'Patient transferred out'">
                                    <ion-col size="12">
                                        <ion-item>
                                            <ion-label position="stacked">Reason (Optional)</ion-label>
                                            <ion-select v-model="formData.reason"
                                                placeholder="Select reason for transferout" interface="popover"
                                                @ion-change="formData.otherReason = ''; validateAll();"
                                                :disabled="isLoading">
                                                <ion-select-option v-for="reason in reasonForArtOptions" :key="reason"
                                                    :value="reason">
                                                    {{ reason }}
                                                </ion-select-option>
                                            </ion-select>
                                        </ion-item>
                                    </ion-col>
                                    <ion-col v-if="formData.reason === 'Other'" size="12">
                                        <ion-item>
                                            <ion-label position="stacked">Reason (Optional)</ion-label>
                                            <ion-textarea @ion-change="validateAll" v-model="formData.otherReason"
                                                placeholder="Enter reason for state change" :disabled="isLoading"
                                                :rows="3"></ion-textarea>
                                        </ion-item>
                                    </ion-col>
                                    <ion-col size="12">
                                        <SelectFacility :show_error="false" :selected_district_ids="[]"
                                            :selected_location="formData.transfer_location"
                                            @facilitySelected="(value: any) => { formData.transfer_location = value.selected_location }" />
                                    </ion-col>
                                </ion-row>

                            </ion-grid>
                            <div class="ion-padding">
                                <ion-button @click="saveOutcome" :disabled="isLoading" color="primary">
                                    <ion-spinner v-if="isLoading" name="crescent" slot="start"></ion-spinner>
                                    {{ isLoading ? 'Saving...' : 'Save Outcome' }}
                                </ion-button>
                            </div>
                        </ion-card-content>
                    </ion-card>
                </div>
            </ion-accordion>
            <ion-accordion value="outcome-history">
                <ion-item slot="header">
                    <ion-label style="font-weight: bold;font-size: 1.2em;">Outcome history</ion-label>
                </ion-item>
                <div slot="content">
                    <!-- Previous Outcomes Table -->
                    <ion-card class="ion-padding">
                        <ion-card-content>
                            <div v-if="previousOutcomes.length === 0" class="ion-text-center ion-padding">
                                <ion-note>No previous outcomes found</ion-note>
                            </div>
                            <div v-else class="table-container">
                                <table class="outcomes-table">
                                    <thead>
                                        <tr>
                                            <th>State</th>
                                            <th>Start Date</th>
                                            <th>End Date</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="outcome in previousOutcomes" :key="outcome.id">
                                            <td>{{ outcome.stateName }}</td>
                                            <td>{{ outcome.startDate }}</td>
                                            <td>{{ outcome.endDate }}</td>
                                            <td>
                                                <ion-button slot="start" @click="voidState(outcome.id)"
                                                    color="danger">Void</ion-button>
                                                <ion-button style="padding: 10px;"
                                                    v-if="outcome.stateName === 'Patient transferred out'"
                                                    @click="printTransferLabel(outcome._raw.start_date)">
                                                    Print
                                                </ion-button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </ion-card-content>
                    </ion-card>
                </div>
            </ion-accordion>
        </ion-accordion-group>
        <p></p>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import {
    IonAccordion,
    IonAccordionGroup,
    IonCard,
    IonCardContent,
    IonGrid,
    IonRow,
    IonCol,
    IonItem,
    IonLabel,
    IonSelect,
    IonSelectOption,
    IonTextarea,
    IonButton,
    IonNote,
    IonSpinner
} from '@ionic/vue';
import { PatientProgramService } from '@/services/patient_program_service';
import { useDemographicsStore } from '@/stores/DemographicStore';
import { toastWarning } from '@/utils/Alerts';
import { ProgramService } from '@/services/program_service';
import DatePicker from "@/components/DatePicker.vue";
import HisDate from '@/utils/Date';
import dayjs from 'dayjs';
import { isEmpty, isPlainObject } from 'lodash';
import popVoidReason from '@/utils/ActionSheetHelpers/VoidReason';
import SelectFacility from "@/apps/OPD/components/SelectFacility.vue";

const activeAccordion = ref<'change-outcome' | 'outcome-history' | ''>('');

// Store and services
const demographicsStore = useDemographicsStore();
const patientId = computed(() => demographicsStore.patient.patientID);

const programService = new PatientProgramService(patientId.value);
programService.setProgramId(1)

// Reactive data
const isLoading = ref(false);
const errorMessage = ref('');

const formData = ref({
    isEnrolled: false,
    programState: '',
    startDate: '' as any,
    reason: '',
    otherReason: '',
    transfer_location: {} as any,
    programEnrollmentDate: '',
    activeState: ''
});

const previousOutcomes = ref<any[]>([]);
const outcomeOptions = ref<any>([]);
const reasonForArtOptions = [
    'Workplace transfer/lost job-related reasons',
    'Relocation to another place/home village',
    'Transport due to long distance',
    'School',
    'Business',
    'Marriage',
    'Unknown',
    'Clinic not helping',
    'Other'
]
const computedStartDate = computed(() => {
    if (!isPlainObject(formData.value.startDate)) {
        return {
            day: dayjs(formData.value.startDate).day(),
            month: dayjs(formData.value.startDate).month() + 1,
            year: dayjs(formData.value.startDate).year(),
            formattedDate: dayjs(formData.value.startDate).format('DD/MMM/YYYY'),
            standardDate: dayjs(formData.value.startDate).format('YYYY-MM-DD')
        }
    }
    return formData.value.startDate
})

const displayOutcomeOptions = computed(() => {
    return outcomeOptions.value.filter((o: any) => o.label !== formData.value.activeState)
})

const loadCurrentProgram = () => {
    previousOutcomes.value = []
    return programService.getPrograms().then((programs) => {
        programs.forEach((p: any) => {
            if (p.program_id === 1) {
                formData.value.isEnrolled = true;
                activeAccordion.value = 'outcome-history'
                formData.value.programEnrollmentDate = HisDate.toStandardHisFormat(p.date_enrolled)
                if (p.patient_states.length === 0) {
                    activeAccordion.value = 'change-outcome'
                }
                p.patient_states.forEach((s: any) => {
                    previousOutcomes.value.push({
                        id: s.patient_state_id,
                        stateName: s.name,
                        startDate: s.start_date ? HisDate.toStandardHisDisplayFormat(s.start_date) : "N/A",
                        endDate: s.end_date ? HisDate.toStandardHisDisplayFormat(s.end_date) : 'N/A',
                        _raw: s
                    })
                    if (!s.end_date) {
                        formData.value.activeState = s.name
                        activeAccordion.value = 'outcome-history'
                    }
                })
            }
        })
    })
}

const validateAll = () => {
    errorMessage.value = '';
    if (!formData.value.programState) {
        errorMessage.value = 'Program state is required';
        return false;
    }
    if (formData.value.programState === "Patient transferred out") {
        if (!formData.value.reason) {
            errorMessage.value = 'Reason is required';
            return false;
        }
        if (formData.value.reason === 'Other' && !formData.value.otherReason) {
            errorMessage.value = 'Other reason is required';
            return false;
        }

        if (isEmpty(formData.value.transfer_location)) {
            errorMessage.value = "Transfer location is required"
            return false;
        }
    }
    if (!formData.value.startDate) {
        errorMessage.value = 'Start date is required';
        return false;
    }
    if (formData.value.programEnrollmentDate && new Date(formData.value.startDate) < new Date(formData.value.programEnrollmentDate)) {
        errorMessage.value = 'Start date cannot be before program enrollment date';
        return false;
    }
    return true
}

// Methods
const loadProgramStates = async () => {
    errorMessage.value = ""
    try {
        const res = await ProgramService.getProgramWorkflows(1);
        outcomeOptions.value = (res?.[0]?.states ?? [])
            .map((state: any) => ({
                value: state.program_workflow_state_id,
                label: state.name
            }))
    } catch (error) {
        console.error('Error loading program data:', error);
        errorMessage.value = 'Failed to load program data';
    }
};

const reloadStates = () => loadCurrentProgram().then(loadProgramStates)
const printTransferLabel = (date: string) => programService.printTransferout(date)

const voidState = (id: any) => popVoidReason(async (reason: any) => {
    programService.setStateId(id)
    await programService.voidState(reason)
    previousOutcomes.value = previousOutcomes.value.filter((o: any) => o.id !== id)
    await reloadStates()
})


const saveOutcome = async () => {
    errorMessage.value = '';
    if (!validateAll()) {
        toastWarning("An error has occured!")
        return;
    }
    isLoading.value = true;
    try {
        await programService.enrollProgram()
    } catch (e) {
        console.log(e)
        console.log("probably already enrolled role!")
    }
    try {
        const stateID = outcomeOptions.value.find((o: any) => o.label === formData.value.programState).value
        programService.setStateId(stateID)
        programService.setStateDate(formData.value.startDate.standardDate)
        await programService.updateState()
        if (formData.value.programState === 'Patient transferred out') {
            await programService.transferOutEncounter(
                formData.value.transfer_location,
                formData.value.otherReason || formData.value.reason)
        }
        activeAccordion.value = 'outcome-history'
        await reloadStates()
        isLoading.value = false
    } catch (error) {
        console.error('Error saving outcome:', error);
        errorMessage.value = 'Failed to save outcome. Please try again.';
        toastWarning('Failed to save outcome');
    } finally {
        isLoading.value = false;
    }
};

// Lifecycle
onMounted(() => reloadStates());
</script>

<style scoped>
.table-container {
    overflow-x: auto;
}

.outcomes-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.outcomes-table th,
.outcomes-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid var(--ion-color-light);
}

.outcomes-table th {
    background-color: var(--ion-color-light);
    font-weight: 600;
    color: var(--ion-color-dark);
}

.outcomes-table tr:hover {
    background-color: var(--ion-color-light-tint);
}

.outcomes-table td {
    color: var(--ion-color-dark);
}

@media (max-width: 768px) {
    .outcomes-table {
        font-size: 0.9rem;
    }

    .outcomes-table th,
    .outcomes-table td {
        padding: 8px;
    }
}
</style>