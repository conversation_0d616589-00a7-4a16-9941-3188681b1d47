<template>
    <div style="margin: 0px auto; width: 80%;">
        <BasicNote label="Compose ART clinical notes here" v-model="notes" required :errorMessage="errorMessage"
            placeholder="Enter your note here..." inputType="text" @onChange="onChange" />
    </div>
</template>
<script lang="ts" setup>
import BasicNote from '@/components/Forms/BasicFormFields/BasicNote.vue';
import { AppEncounterService } from '@/services/app_encounter_service';
import { ref } from 'vue';
import { useDemographicsStore } from "@/stores/DemographicStore";
import { storeToRefs } from 'pinia';

const notes = ref('');
const errorMessage = ref('');
const demographicsStore = useDemographicsStore();
const { patient } = storeToRefs(demographicsStore) as any;
const service = new AppEncounterService(patient.value.patientID, 105)

function onChange(value: string) {
    if (value) errorMessage.value = ''; // Reset error message on change
}

async function onSubmit() {
    errorMessage.value = ''; // Clear error on successful submission
    if (!notes.value.trim()) {
        errorMessage.value = 'Note cannot be empty';
        return false
    }
    await service.createEncounter()
    await service.saveValueTextObs('Clinician notes', notes.value);
    return true
}

defineExpose({
    onSubmit
})
</script>