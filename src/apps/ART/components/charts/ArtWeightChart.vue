<template>
    <div style="border: 1px dashed #ccc; padding: 20px;" v-if="!hasValueSet">
        <h1 class="ion-text-center">
            No weight data available for this patient
        </h1>
    </div>
    <ion-grid v-else>
        <ion-row>
            <ion-col size-md="8" size-sm="12">
                <ApexChart width="100%" height="560px" type="area" :options="chartOptions" :series="series" />
            </ion-col>
            <ion-col size-md="4" size-sm="12">
                <ion-list>
                    <ion-item>
                        <ion-label class='title'>Previous weight</ion-label>
                        <ion-chip slot="end" color="primary">
                            {{ stats.prevWeight }}
                        </ion-chip>
                    </ion-item>
                    <ion-item>
                        <ion-label class='title'>Latest weight</ion-label>
                        <ion-chip slot="end" color="primary">
                            {{ stats.curWeight }}
                        </ion-chip>
                    </ion-item>
                    <ion-item>
                        <ion-label class='title'>Latest weight change</ion-label>
                        <ion-icon color="primary" :icon="stats.weightState" v-show="stats.weightState" width="50" />
                        <ion-chip slot="end" color="primary">
                            {{ stats.curWeightChange }}
                        </ion-chip>
                    </ion-item>
                    <ion-item>
                        <ion-label class='title'>Patient Age</ion-label>
                        <ion-chip slot="end" color="primary">
                            {{ stats.age }}
                        </ion-chip>
                    </ion-item>
                    <ion-item>
                        <ion-label class='title'>Patient BMI</ion-label>
                        <ion-chip slot="end" color="primary">
                            {{ stats.bmi.index }}
                        </ion-chip>
                    </ion-item>
                    <ion-item>
                        <ion-label
                            :style="{ 'background-color': stats.bmi.color, color: 'white', padding: '10px', 'text-align': 'center' }">
                            {{ stats.bmi.result }}
                        </ion-label>
                    </ion-item>
                </ion-list>
            </ion-col>
        </ion-row>
    </ion-grid>
</template>
<script lang="ts" setup>
import ApexChart from "vue3-apexcharts";
import {
    IonIcon,
    IonChip,
    IonList,
    IonItem,
    IonLabel,
    IonGrid,
    IonRow,
    IonCol
} from "@ionic/vue"
import { onMounted, reactive, ref } from "vue";
import { PatientService } from "@/services/patient_service";
import dayjs from "dayjs";
import { arrowDown, arrowUp, remove } from "ionicons/icons";

const patientService = new PatientService()
const series = ref<any>([])
const hasValueSet = ref(false)

const stats = reactive({
    prevWeight: '-' as string,
    curWeight: '-' as string,
    curWeightChange: '-' as string,
    weightState: '' as string,
    age: '-' as string,
    bmi: {} as any
})

const chartOptions = {
    chart: {
        id: "weight_chart",
    },
    title: {
        text: 'Weight trail (2 year period)'
    },
    stroke: {
        curve: 'smooth',
    },
    yaxis: {
        title: { text: "Weight Kg(s)" },
        min: 0,
    },
    xaxis: {
        categories: []
    },
    dataLabels: {
        enabled: true,
        textAnchor: 'start',
        formatter: function (firstY: any, opt: any): any {
            const secondY = opt.w.config.series[0].data[opt.dataPointIndex - 1]?.y

            if (secondY && secondY > 0) {
                return (((firstY / secondY) * 100) - 100).toFixed(2) + ' %'
            }
            return '0.0%'
        }
    }
}

function setStats(data: any) {
    const prevWeight = data.values[data.values.length - 2]?.y || 0
    const curWeight = data.values[data.values.length - 1]?.y || 0
    stats.curWeight = curWeight || '-'
    stats.prevWeight = prevWeight || '-'
    stats.age = data.age
    stats.bmi = data.bmi

    if (curWeight > 0 && prevWeight > 0) {
        stats.curWeightChange = Math.abs((((curWeight / prevWeight) * 100) - 100)).toFixed(2) + ' %'
        stats.weightState = (() => {
            if (curWeight > prevWeight) {
                return arrowUp
            } else if (curWeight < prevWeight) {
                return arrowDown
            }
            return remove
        })()
    }
}

onMounted(async () => {
    const weightTrail = await patientService.getWeightHistory()
    const bmi = await patientService.getBMI()
    const data = weightTrail.map((d: any) => {
        return {
            x: dayjs(d.date).format('DD/MMM/YYYY'),
            y: d.weight
        }
    })
    hasValueSet.value = data.length > 0
    series.value = [
        {
            name: "Weight",
            data: data.sort((a: any, b: any) => {
                const dateA: any = new Date(a.x)
                const dateB: any = new Date(b.x)
                return dateA - dateB
            }).map((item: any) => {
                const [day, month] = item.x.split('/')
                return {
                    x: `${day}.${month}`,
                    y: item.y
                }
            })
        }
    ]
    setStats({
        bmi,
        age: patientService.getAge(),
        values: data
    })
})
</script>