<template>
    <ion-grid>
        <ion-row>
            <ion-col>
                <ion-label>Date started TPT treatment</ion-label>
                <DatePicker :place_holder="'Select date'"
                    @date-up-dated="(d: any) => { formData.dateStartedTpt = d.value.standardDate; dataHandler('dateStartedTpt', d.value.standardDate) }"
                    :date_prop="''" />
                <div class="error">{{ validationMessages['dateStartedTpt'] }}</div>
            </ion-col>
            <ion-col>
                <ion-label>Facility TPT last received from</ion-label>
                <SelectFacility :show_error="false" :selected_district_ids="[]" :selected_location="null"
                    @facilitySelected="(value: any) => { formData.transferFacility = value.selected_location.name; dataHandler('transferFacility', value.selected_location.name); }" />
                <div class="error">{{ validationMessages['transferFacility'] }}</div>
            </ion-col>
        </ion-row>
        <ion-row v-for="(order, index) in formData.tptDrugQuantities" :key="index">
            <ion-col>
                <ion-item lines="none">
                    <ion-label>
                        {{ order.name }}
                    </ion-label>
                </ion-item>
            </ion-col>
            <ion-col>
                <ion-input 
                    @ionInput="(e) => dataHandler('tptDrugQuantities', { index, field: 'quantity', value: e.detail.value })"
                    placeholder="Enter amount given" v-model="order.quantity" type="number" fill="outline" />
            </ion-col>
        </ion-row>
        <ion-row>
            <ion-col>
                <div class="error">{{ validationMessages['tptDrugQuantities'] }}</div>
            </ion-col>
        </ion-row>
    </ion-grid>
</template>
<script lang="ts" setup>
import {
    IonItem,
    IonInput,
    IonLabel,
    IonGrid,
    IonRow,
    IonCol
} from "@ionic/vue"
import { toastWarning } from '@/utils/Alerts';
import { isEmpty } from 'lodash';
import { onMounted, PropType, ref } from 'vue';
import { ConsultationService } from '../../services/consultation_service';
import { PatientService } from '@/services/patient_service';
import dayjs from 'dayjs';
import { RegimenService } from '@/services/regimen_service';
import SelectFacility from "@/apps/OPD/components/SelectFacility.vue";
import DatePicker from "@/components/DatePicker.vue";
import { uniqueBy } from "@/utils/Arrays";

const props = defineProps({
    patientId: {
        type: Number,
        required: true
    },
    drugNameFilters: {
        type: Object as PropType<string[]>,
        default: () => []
    }
})

const validationMessages = ref<any>({})
const patientService = new PatientService()
const consultationService = new ConsultationService(props.patientId, -1);

const formData = ref<any>({
    canSubmit: false,
    dateStartedTpt: '',
    tptDrugQuantities: [] as any,
    transferFacility: '' as string
})

const formModel: any = {
    dateStartedTpt: {
        label: "Date started TPT treatment",
        required: () => true,
        validation: () => {
            if (hasValue('dateStartedTpt')) {
                if (dayjs(formData.value.dateStartedTpt).isAfter(dayjs(PatientService.getSessionDate()))) {
                    validationMessages.value['dateStartedTpt'] = 'Date cannot be in the future'
                    return false
                }
                if (dayjs(formData.value.dateStartedTpt).isBefore(dayjs(patientService.getBirthdate()))) {
                    validationMessages.value['dateStartedTpt'] = 'Date cannot be before birthdate'
                    return false
                }
                return true
            }
            return false
        }
    },
    transferFacility: {
        label: "Facility TPT last received from",
        required: () => true,
        buildObs: () => consultationService.buildValueText(
            'Location TPT last received', formData.value.transferFacility
        ),
        validation: () => hasValue('transferFacility')
    },
    tptDrugQuantities: {
        label: "TPT Drug Quantities",
        required: () => true,
        init: async () => {
            const defaultFilters = [
                'INH or H (Isoniazid 300mg tablet)',
                '3HP (RFP + INH',
                'Rifapentine (150mg)',
                'INH 300 / RFP 300 (3HP)',
            ]
            const filters = props.drugNameFilters.length > 0 ? props.drugNameFilters : defaultFilters
            const customDrugs = await RegimenService.getCustomIngridients()
            const drugs = customDrugs.filter((drug: any) => filters.includes(drug.name))
            formData.value.tptDrugQuantities = uniqueBy(drugs.map((drug: any) => ({
                drug,
                name: drug.name,
                quantity: null
            })), 'name')
        },
        buildObs: () => {
            return formData.value.tptDrugQuantities.map((drug: any) => {
                return consultationService.buildObs('TPT Drugs Received', {
                    'value_drug': drug.drug.drug_id,
                    'value_datetime': formData.value.dateStartedTpt,
                    'value_numeric': drug.quantity
                })
            })
        },
        validation: () => {
            if (hasValue('tptDrugQuantities')) {
                const allHaveValues = formData.value.tptDrugQuantities.every((drug: any) => (drug.quantity ?? 0) > 0)
                if (!allHaveValues) {
                    validationMessages.value['tptDrugQuantities'] = 'Please enter quantities for all drugs'
                    return false
                }
                return true
            }
        }
    }
}

function hasValue(field: string) {
    validationMessages.value[field] = ""
    const fieldMeta = formModel[field]
    const required = typeof fieldMeta?.required === 'function' ? fieldMeta.required() : false
    const empty = (() => {
        if (typeof formData.value[field] === 'object') {
            return isEmpty(formData.value[field])
        }
        if (Array.isArray(formData.value[field])) {
            return formData.value[field].length === 0
        }
        return `${formData.value[field]}`.length === 0
    })()
    if (required && empty) {
        validationMessages.value[field] = 'This field is required'
        return false
    }
    return required
}

function buildObs() {
    return Object.keys(formModel).reduce((a: any, c: any) => {
        if (c in formModel && typeof formModel[c].required === 'function' && formModel[c].required() && typeof formModel[c].buildObs === 'function') {
            const obs = formModel[c].buildObs()
            if (Array.isArray(obs)) {
                return [...a, ...obs]
            }
            return [...a, obs]
        }
        return a
    }, [])
}

function validateAll() {
    if (!formData.value.canSubmit) {
        toastWarning("Please wait for all data to load before submitting")
        return false
    }
    return Object.keys(formModel).every((key: string) => runValidation(key))
}

function runValidation(field: string) {
    validationMessages.value[field] = ''
    if (typeof formModel[field].validation === 'function') {
        return formModel[field].validation()
    }
    return true
}

function dataHandler(field: string, value: any) {
    runValidation(field)
    Object.keys(formModel).forEach((k) => formModel[k]?.onFormUpdate && formModel[k].onFormUpdate(field, value))
}

async function init() {
    formData.value.canSubmit = false
    await Promise.all(
        Object.keys(formModel)
            .filter((k: string) => typeof formModel[k].init === 'function')
            .map((k: string) => formModel[k].init())
    )
    formData.value.canSubmit = true
}

onMounted(() => init())

defineExpose({
    buildObs,
    validateAll
})
</script>
<style scoped>
.error {
    color: red;
    font-weight: bold;
    font-style: italic;
    padding: 10px;
}
ion-label {
    font-weight: bold;
}
</style>