import { toastWarning } from "@/utils/Alerts";
import { computed, Ref } from 'vue';

export const useConsultationValidator = (
    weightChart: Ref<boolean>,
    sideEffects: Ref<boolean>,
    tbTherapy: Ref<boolean>,
) => {
    const stepsMap = computed(() => ({
        "weight-chart": weightChart.value,
        "side-effects": sideEffects.value,
        "tb-therapy": tbTherapy.value,
    }));

    const validate = async (): Promise<boolean> => {
        for (const step in stepsMap.value) {
            const key = step as keyof typeof stepsMap.value;
            if (!stepsMap.value[key]) {
                toastWarning(`Please fill out ${step.replaceAll('-', ' ')} section`);
                return false;
            }
        }
        return true;
    }

    return {
        validate,
        stepsMap,
    }
}