<template>
  <div class="cervical-cancer-container">
    <div v-if="loading" class="loading-container">
      <ion-spinner name="bubbles" />
      <p>Loading cervical cancer screening data...</p>
    </div>
    <div v-else>
      <div v-if="showReminder" class="reminder-banner">
        <ion-text color="warning">
          <p>
            <ion-icon :icon="alertCircleOutline"></ion-icon>
            Patient is due for Cervical Cancer Screening on {{ formatDate(cxcaAppointDate) }}
          </p>
        </ion-text>
      </div>

      <ion-row>
        <ion-col>
          <ion-label>Offer Cervical Cancer Screening</ion-label>
        </ion-col>
        <ion-col>
          <div class="radio-group">
            <ion-radio-group v-model="offerCxca">
              <ion-radio slot="end" value="Yes">Yes</ion-radio>
              <ion-radio slot="end" style="margin-left: 20px" value="No">No</ion-radio>
            </ion-radio-group>
          </div>
        </ion-col>
      </ion-row>

      <div v-if="offerCxca === 'No'">
        <ion-row>
          <ion-col>
            <ion-label>Reason for NOT offering CxCa</ion-label>
          </ion-col>
          <ion-col>
            <ion-select v-model="reasonForNoCxca" interface="popover" placeholder="Select reason">
              <ion-select-option v-for="option in reasonsForNoCxcaOptions" :key="option.value" :value="option.value">
                {{ option.label }}
              </ion-select-option>
            </ion-select>
          </ion-col>
        </ion-row>

        <div v-if="reasonForNoCxca === 'Not due for screening'">
          <ion-row>
            <ion-col>
              <ion-label>Previous CxCa test date</ion-label>
            </ion-col>
            <ion-col>
              <DatePicker :place_holder="'enter data'" @date-up-dated="() => { }" :date_prop="dateProp" @dateUpDated="((e: any) => {
                previousCxcaTestDate = e.formattedDate
              })" />
            </ion-col>
          </ion-row>
          <ion-row v-if="showEstimateFields">
            <ion-col>
              <ion-label>Estimated?</ion-label>
            </ion-col>
            <ion-col>
              <BasicInputChangeUnits bold="bold" :input-header="`Estimate number of ${selectedEstimatePeriod.name}`"
                v-model:input-value="estimatedValue" showAsterisk :placeholder="''" :unitsData="{
                  isSingleSelect: true,
                  multiSelectData: estimatePeriods.map((period: EstimatePeriod) => { return { name: period } }),
                  value: selectedEstimatePeriod,
                  trackBy: 'name'
                }" @update:inputValue="((e: any) => estimatedValue = e.target.value)"
                @update:units="((e: any) => selectedEstimatePeriod = e)" />
            </ion-col>
          </ion-row>
        </div>
      </div>

      <div class="save-button-container">
        <ion-button @click="saveCxcaData" :disabled="!isFormValid">
          Save and Continue
        </ion-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted, PropType, watch } from 'vue';
import {
  IonRow, IonCol, IonLabel, IonRadioGroup, IonRadio, IonButton, IonSpinner,
  IonSelect, IonSelectOption, IonText, IonDatetimeButton, IonDatetime, IonModal, IonCheckbox
} from '@ionic/vue';
import { alertCircleOutline } from 'ionicons/icons';
import { ConsultationService } from '../../services/consultation_service';
import dayjs from 'dayjs';
import HisDate from "@/utils/Date";
import ART_PROP from '../../art_global_props';
import { PatientService } from '@/services/patient_service';
import DatePicker from '@/components/DatePicker.vue';
import BasicInputChangeUnits from '@/components/BasicInputChangeUnits.vue';
import { EstimatePeriod, startDateEstimator } from './useTBTherapy';

interface OptionType {
  label: string;
  value: string;
}

interface Observation {
  concept_name: string;
  value_type: string;
  value: string | number | boolean;
}

export default defineComponent({
  name: 'CervicalCancerSection',
  components: {
    IonRow, IonCol, IonLabel, IonRadioGroup, IonRadio, IonButton, IonSpinner,
    IonSelect, IonSelectOption, IonText, IonDatetimeButton, IonDatetime, IonModal, IonCheckbox, DatePicker, BasicInputChangeUnits
  },
  props: {
    providerID: {
      type: Number,
      required: true
    },
    locationID: {
      type: String,
      required: true
    }
  },
  setup(props, { emit }) {
    const loading = ref<boolean>(true);
    const isCxCaSaved = ref<boolean>(false);
    const offerCxca = ref<string>('');
    const reasonForNoCxca = ref<string>('');
    const previousCxcaTestDate = ref<string>('');
    const isEstimated = ref<boolean>(false);
    const showEstimateFields = ref<boolean>(true);
    const cxcaEnabled = ref<boolean>(false);
    const cxcaMaxAge = ref<number>(0);
    const cxcaStartAge = ref<number>(0);
    const dueForCxca = ref<boolean>(false);
    const clientHadAHysterectomy = ref<boolean>(false);
    const cxcaAppointDate = ref<string>('');
    const estimatedValue = ref<string>('');
    const showReminder = ref<boolean>(false);
    const patientService = new PatientService();
    const consultationService = new ConsultationService(
      patientService.patient.patient_id,
      props.providerID,
      props.locationID
    );
    const selectedEstimatePeriod = ref<{ name: EstimatePeriod }>({ name: "Months" });
    const estimatePeriods = ref<EstimatePeriod[]>(["Months", "Weeks", "Days"]);
    const dateProp = ref<string | { day: string; month: string; year: string; }>("");

    const maxDate = computed<string>(() => {
      return dayjs().format('YYYY-MM-DD');
    });

    const minDate = computed<string>(() => {
      return '1900-01-01';
    });

    const reasonsForNoCxcaOptions = ref<OptionType[]>([
      { label: 'Not due for screening', value: 'Not due for screening' },
      { label: 'Hysterectomy', value: 'Hysterectomy' },
      { label: 'Patient refused', value: 'Patient refused' },
      { label: 'Other', value: 'Other' }
    ]);

    const isFormValid = computed<boolean>(() => {
      if (offerCxca.value === '') return false;
      if (offerCxca.value === 'No' && reasonForNoCxca.value === '') return false;
      if (reasonForNoCxca.value === 'Not due for screening' && previousCxcaTestDate.value === '') return false;
      return true;
    });

    const formatDate = (date: string): string => {
      return HisDate.toStandardHisDisplayFormat(date);
    };

    const initData = async (): Promise<void> => {
      try {
        loading.value = true;
        if (patientService.patient.gender === 'F') {
          cxcaEnabled.value = await ART_PROP.cervicalCancerScreeningEnabled();

          if (cxcaEnabled.value) {
            const { start, end } = await ART_PROP.cervicalCancerScreeningAgeBounds();
            cxcaMaxAge.value = end;
            cxcaStartAge.value = start;
            dueForCxca.value = await consultationService.clientDueForCxCa();
            clientHadAHysterectomy.value = await consultationService.clientHasHadAHysterectomy();
            cxcaAppointDate.value = await patientService.patient.nextAppointment(24);

            const ONE_MONTH = 30;
            showReminder.value = ONE_MONTH > HisDate.dateDiffInDays(cxcaAppointDate.value, consultationService.date);
          }
        }
      } catch (error) {
        console.error('Error initializing cervical cancer data:', error);
      } finally {
        loading.value = false;
      }
    };

    const saveCxcaData = async (): Promise<void> => {
      try {
        if (!isFormValid.value) return;
        const observations = [];
        observations.push(
          consultationService.buildValueCoded('Offer CxCa', offerCxca.value)
        );
        if (offerCxca.value === 'No') {
          observations.push(
            consultationService.buildValueCoded('Reason for NOT offering CxCa', reasonForNoCxca.value)
          );

          if (reasonForNoCxca.value === 'Not due for screening' && previousCxcaTestDate.value) {
            if (isEstimated.value) {
              observations.push(
                consultationService.buildValueDateEstimated('CxCa test date', previousCxcaTestDate.value)
              );
            } else {
              observations.push(
                consultationService.buildValueDate('CxCa test date', previousCxcaTestDate.value)
              );
            }
          }
        }

        isCxCaSaved.value = true;
        emit('onSuccess', {
          obs: observations,
          isCxCaSaved: isCxCaSaved.value
        });
      } catch (error) {
        console.error('Error saving cervical cancer data:', error);
      }
    };

    onMounted(() => {
      initData();
    });

    function createDate(day: any, month: any, year: any) {
      let date = new Date(year, month - 1, day);
      return date.toLocaleDateString('en-US', {
        weekday: 'long',
        month: 'short',
        day: 'numeric',
        year: 'numeric',
      });
    }

    watch([estimatedValue, selectedEstimatePeriod], ([newEstimate, newPeriod]) => {
      if (newEstimate || newPeriod) {
        const estimatedDate = startDateEstimator(Number(newEstimate), selectedEstimatePeriod.value.name);
        dateProp.value = estimatedDate;
        previousCxcaTestDate.value = createDate(Number(estimatedDate.day), Number(estimatedDate.month), Number(estimatedDate.year));
        isEstimated.value = true;
      }
    });

    return {
      loading,
      isCxCaSaved,
      offerCxca,
      reasonForNoCxca,
      previousCxcaTestDate,
      isEstimated,
      showEstimateFields,
      reasonsForNoCxcaOptions,
      isFormValid,
      cxcaAppointDate,
      showReminder,
      maxDate,
      minDate,
      alertCircleOutline,
      selectedEstimatePeriod,
      estimatePeriods,
      estimatedValue,
      dateProp,
      formatDate,
      saveCxcaData
    };
  }
});
</script>

<style scoped>
.cervical-cancer-container {
  padding: 10px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.reminder-banner {
  background-color: rgba(255, 206, 86, 0.2);
  border-left: 4px solid #ffce56;
  padding: 10px;
  margin-bottom: 15px;
  border-radius: 4px;
}

.save-button-container {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.radio-group {
  display: flex;
  align-items: center;
}
</style>