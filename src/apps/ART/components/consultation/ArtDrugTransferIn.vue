<template>
    <ion-card v-if="!formData.is_transfer_in">
        <ion-card-content>
            <h1 class="ion-text-center ion-padding">Client is not a transfer in</h1>
        </ion-card-content>
    </ion-card>
    <ion-card v-else>
        <ion-progress-bar v-if="formData.isLoading" type="indeterminate" />
        <ion-card-content>
            <ion-grid>
                <ion-row>
                    <ion-col>
                        <ion-label class="form-label">
                            {{ formModel.date_last_received_arvs.label }}
                            <span style="color: red">*</span>
                        </ion-label>
                        <DatePicker place_holder="" :date_prop="''"
                            @date-up-dated="(val: any) => { formData.date_last_received_arvs = val.value.standardDate; dataHandler('date_last_received_arvs', val.value.standardDate); }"
                            :error="!!validationMessages.date_last_received_arvs" />
                        <ion-note v-if="validationMessages.date_last_received_arvs" color="danger">
                            {{ validationMessages.date_last_received_arvs }}
                        </ion-note>
                    </ion-col>
                </ion-row>
                <ion-row>
                    <ion-col>
                        <ion-label class="form-label">
                            {{ formModel.selected_arvs.label }}
                            <span style="color: red">*</span>
                        </ion-label>
                        <ion-select multiple fill="outline" v-model="formData.selected_arvs"
                            @ionChange="dataHandler('selected_arvs', $event.detail.value)" interface="popover"
                            placeholder="Select ARVs">
                            <ion-select-option v-for="arv in formData.arv_drugs" :key="arv" :value="arv">
                                {{ arv.name }}
                            </ion-select-option>
                        </ion-select>
                        <ion-note class="ion-text-center ion-padding" v-if="validationMessages.selected_arvs"
                            color="danger">
                            {{ validationMessages.selected_arvs }}
                        </ion-note>
                    </ion-col>
                    <ion-col>
                        <ion-label class="form-label">
                            {{ formModel.drug_interval.label }}
                            <span style="color: red">*</span>
                        </ion-label>
                        <ion-select fill="outline" v-model="formData.drug_interval"
                            @ionChange="dataHandler('drug_interval', $event.detail.value)" interface="popover"
                            placeholder="Select duration">
                            <ion-select-option v-for="option in formModel.drug_interval.options()" :key="option.value"
                                :value="option.value">
                                {{ option.label }}
                            </ion-select-option>
                        </ion-select>
                        <ion-note v-if="validationMessages.drug_interval" color="danger">
                            {{ validationMessages.drug_interval }}
                        </ion-note>
                    </ion-col>
                </ion-row>
            </ion-grid>
            <ion-card v-if="formData.selected_arvs.length" class="ion-padding">
                <ion-card-content>
                    <ion-grid v-for="arv in formData.selected_arvs" :key="arv.drug.drug_id">
                        <ion-row>
                            <ion-col>
                                <ion-label class="form-label">
                                    {{ arv.name }}
                                    <span style="color: red">*</span>
                                </ion-label>
                            </ion-col>
                        </ion-row>
                        <ion-row>
                            <ion-col>
                                <ion-label class="form-label">
                                    Given Amount
                                    <span style="color: red">*</span>
                                </ion-label>
                                <ion-input @ion-input="dataHandler('selected_arvs', arv)"
                                    v-model="arv.dispensed_quantity" placeholder="Enter amount" fill="outline"
                                    type="number" />
                                <ion-note v-if="validationMessages[arv.dispensed_quantity_error_id]" color="danger">
                                    {{ validationMessages[arv.dispensed_quantity_error_id] }}
                                </ion-note>
                            </ion-col>
                            <ion-col>
                                <ion-label class="form-label">
                                    Pill brought
                                    <span style="color: red">*</span>
                                </ion-label>
                                <ion-input @ion-input="dataHandler('selected_arvs', arv)" v-model="arv.pill_brought"
                                    placeholder="Enter amount" type="number" fill="outline" />
                                <ion-note v-if="validationMessages[arv.pills_brought_error_id]" color="danger">
                                    {{ validationMessages[arv.pills_brought_error_id] }}
                                </ion-note>
                            </ion-col>
                        </ion-row>
                    </ion-grid>
                </ion-card-content>
            </ion-card>
        </ion-card-content>
    </ion-card>
</template>
<script lang="ts" setup>
import {
    IonProgressBar,
    IonInput,
    IonSelect,
    IonSelectOption,
    IonLabel,
    IonNote,
    IonCard,
    IonCardContent,
    IonGrid,
    IonCol,
    IonRow,
} from "@ionic/vue"
import { PatientService } from "@/services/patient_service";
import { onMounted, ref } from "vue";
import { ConsultationService } from "../../services/consultation_service";
import HisDate from "@/utils/Date";
import dayjs from "dayjs";
import { PrescriptionService } from "../../services/prescription_service";
import { isEmpty } from "lodash";
import DatePicker from "@/components/DatePicker.vue";
import { toastWarning } from "@/utils/Alerts";

const emit = defineEmits(['canEnterTransferDrugs'])

const patientService = new PatientService();
const validationMessages = ref<any>({})
const patientId = patientService.getID()
const consultationService = new ConsultationService(patientService.getID(), -1);
const prescriptionService = new PrescriptionService(patientService.getID(), -1);

const formData = ref<any>({
    isLoading: false,
    canSubmit: false,
    arv_drugs: [] as any,
    is_transfer_in: true,
    date_art_started: "",
    date_last_received_arvs: "",
    selected_arvs: [],
    drug_interval: ""
})

const formModel: any = {
    date_last_received_arvs: {
        label: "Last ARV Dispensation",
        required: () => formData.value.is_transfer_in,
        init: async () => {
            formData.value.is_transfer_in = await formModel.date_last_received_arvs.getTransferInStatus()
            formData.value.date_art_started = await formModel.date_last_received_arvs.getDateStartedArt()
            emit('canEnterTransferDrugs', formData.value.is_transfer_in)
        },
        buildObs: () => consultationService.buildValueDate(
            'Date drug received from previous facility', formData.value.date_last_received_arvs
        ),
        validation: () => {
            const required = hasValue('date_last_received_arvs')
            if (required) {
                const dateLastReceived = dayjs(formData.value.date_last_received_arvs)
                if (dateLastReceived.isBefore(dayjs(formData.value.date_art_started))) {
                    validationMessages.value['date_last_received_arvs'] = 'Date last received ARVs cannot be before ART start date'
                    return false
                }
                if (dateLastReceived.isAfter(dayjs(consultationService.getDate()))) {
                    validationMessages.value['date_last_received_arvs'] = 'Date last received ARVs cannot be after today'
                    return false
                }
                return true
            }
            return required
        },
        getDateStartedArt: async () => {
            const dateStarted = await ConsultationService.getFirstValueDatetime(patientId, 'Date ART started')
            return dateStarted ? HisDate.toStandardHisFormat(dateStarted) : ''
        },
        getTransferInStatus: async () => {
            const receivedArvs = await ConsultationService.getFirstValueCoded(
                patientId, 'Ever received ART'
            )
            const transferLetterObs = await ConsultationService.getFirstObs(
                patientId, 'Has transfer letter'
            )
            const date = transferLetterObs ? HisDate.toStandardHisFormat(transferLetterObs.obs_datetime) : ''
            return receivedArvs
                && receivedArvs.match(/yes/i)
                && transferLetterObs
                && `${transferLetterObs.value_coded}`.match(/yes/i)
                && date === consultationService.getDate()
        }
    },
    selected_arvs: {
        label: "Last ARV drugs dispensed",
        init: async () => {
            formData.value.arv_drugs = (await prescriptionService.getARVs()).map((d: any) => ({
                drug: d,
                name: d.name,
                pill_brought: null,
                dispensed_quantity: null,
                dispensed_quantity_error_id: `dispensed_quantity_error_${d.drug_id}`,
                pills_brought_error_id: `pills_brought_error_${d.drug_id}`,
            }))
        },
        required: () => formData.value.is_transfer_in,
        buildObs: () => {
            return formData.value.selected_arvs.map(async (arv: any) => {
                return {
                    ...(await consultationService.buildObs(
                        'Drug received from previous facility', {
                        'value_drug': arv.drug.drug_id,
                        'value_datetime': (() => {
                            prescriptionService.setNextVisitInterval(formData.value.drug_interval)
                            return prescriptionService.calculateDateFromInterval()
                        })(),
                        'value_numeric': arv.dispensed_quantity
                    }
                    )),
                    child: [
                        await consultationService.buildObs(
                            'Number of tablets brought to clinic', {
                            'value_drug': arv.drug.drug_id,
                            'value_numeric': arv.pill_brought,
                            'value_datetime': formData.value.date_last_received_arvs
                        })
                    ]
                }
            })
        },
        validation: () => {
            const required = hasValue('selected_arvs')
            let isValid = required
            if (required) {
                formData.value.selected_arvs.forEach((arv: any) => {
                    validationMessages.value[arv.dispensed_quantity_error_id] = ''
                    if (!isDigit(arv.dispensed_quantity)) {
                        validationMessages.value[arv.dispensed_quantity_error_id] = 'A valid digit is required'
                        isValid = false
                    }

                    validationMessages.value[arv.pills_brought_error_id] = ''
                    if (!isDigit(arv.pill_brought)) {
                        validationMessages.value[arv.pills_brought_error_id] = 'A valid digit is required'
                        isValid = false
                    }
                })
            }
            return isValid
        }
    },
    drug_interval: {
        label: "Duration period for last received ARVs",
        required: () => formData.value.is_transfer_in,
        validation: () => hasValue('drug_interval'),
        options: () => {
            return [
                { label: '2 weeks', value: 14 },
                { label: '1 month', value: 28 },
                { label: '2 months', value: 56 },
                { label: '3 months', value: 84 },
                { label: '4 months', value: 112 },
                { label: '5 months', value: 140 },
                { label: '6 months', value: 168 },
                { label: '7 months', value: 196 },
                { label: '8 months', value: 224 },
                { label: '9 months', value: 252 },
                { label: '10 months', value: 280 },
                { label: '11 months', value: 308 },
                { label: '12 months', value: 336 }
            ]
        }
    }
}

function isDigit(val: any) {
    return /^\d+$/i.test(`${val}`)
}

function hasValue(field: string) {
    validationMessages.value[field] = ""
    const fieldMeta = formModel[field]
    const required = typeof fieldMeta?.required === 'function' ? fieldMeta.required() : false
    const empty = (() => {
        if (typeof formData.value[field] === 'object') {
            return isEmpty(formData.value[field])
        }
        if (Array.isArray(formData.value[field])) {
            return formData.value[field].length === 0
        }
        return `${formData.value[field]}`.length === 0
    })()
    if (required && empty) {
        validationMessages.value[field] = 'This field is required'
        return false
    }
    return required
}

function buildObs() {
    return Object.keys(formModel).reduce((a: any, c: any) => {
        if (c in formModel && typeof formModel[c].required === 'function' && formModel[c].required() && typeof formModel[c].buildObs === 'function') {
            const obs = formModel[c].buildObs()
            if (Array.isArray(obs)) {
                return [...a, ...obs]
            }
            return [...a, obs]
        }
        return a
    }, [])
}

function validateAll() {
    if (!formData.value.canSubmit) {
        toastWarning("Please wait for all data to load before submitting")
        return false
    }
    return Object.keys(formModel).every((key: string) => runValidation(key))
}

function runValidation(field: string) {
    validationMessages.value[field] = ''
    if (typeof formModel[field].validation === 'function') {
        return formModel[field].validation()
    }
    return true
}

function dataHandler(field: string, value: any) {
    runValidation(field)
    Object.keys(formModel).forEach((k) => formModel[k]?.onFormUpdate && formModel[k].onFormUpdate(field, value))
}

async function init() {
    formData.value.isLoading = true
    formData.value.canSubmit = false
    await Promise.all(
        Object.keys(formModel)
            .filter((k: string) => typeof formModel[k].init === 'function')
            .map((k: string) => formModel[k].init())
    )
    formData.value.isLoading = false
    formData.value.canSubmit = true
}

onMounted(() => init())

defineExpose({
    buildObs,
    validateAll
})
</script>
<style scoped>
.form-label {
    font-weight: bold;
}

ion-note {
    padding: 10px;
    color: red;
    font-weight: bold;
    font-style: italic;
}
</style>