<template>
    <div v-if="isVlAlertEnabled">
        <div class="info-container" v-if="isVisible && vlData?.eligibile && !vlData.skip_milestone">
            <div class="info-card">
                <button class="close-button" @click="handleClose" aria-label="Close notification">
                    <svg class="close-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18" />
                        <line x1="6" y1="6" x2="18" y2="18" />
                    </svg>
                </button>

                <div class="main-message">
                    <svg class="info-icon" xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32">
                        <path fill="currentColor"
                            d="M24.586 6.586A2 2 0 0 0 23.17 6H16V2h-2v4H6a2.003 2.003 0 0 0-2 2v6a2.003 2.003 0 0 0 2 2h8v14h2V16h7.171a2 2 0 0 0 1.415-.586L29 11ZM23.17 14H6V8h17.172l3 3Z" />
                    </svg>

                    <div class="message-content">
                        <h2>VL milestone has been reached</h2>
                        <ul class="info-list">
                            <li>Patient has been on ART for <b>{{ vlData.period_on_art }}</b> months</li>
                            <li>Treatment started on <b>{{ formatDate(vlData.earliest_start_date) }}</b></li>
                            <li>Current regimen: <b>{{ vlData.current_regimen.name }}</b> since <b>{{
                                formatDate(vlData.current_regimen.date_started) }}</b></li>
                            <li>Last VL order date: <b>{{ formatDate(vlData?.last_order_date) }}</b></li>
                        </ul>
                        <div>
                            <ion-button class="ion-padding" @click="waitTillNextMilestone" color="primary" fill="outline"
                                size="small">
                                Wait till next milestone
                            </ion-button>
                            <ion-button class="ion-padding" @click="remindMeLater" color="primary" fill="outline"
                                size="small">
                                Remind me later
                            </ion-button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <ion-item v-else lines="none">
            <ion-icon style="font-size: 1.3rem;" slot="start" size="large" :icon="informationCircleOutline"></ion-icon>
            <ion-label style="font-size: 1.2rem;" slot="start">
                {{ vlData?.message }}
            </ion-label>
        </ion-item>
    </div>
</template>


<script setup lang="ts">
import { ProgramService } from '@/services/program_service'
import { alertConfirmation, toastWarning } from '@/utils/Alerts'
import { onMounted, ref } from 'vue'
import {
    IonLabel,
    IonIcon,
    IonItem,
    IonButton
} from "@ionic/vue"
import { informationCircleOutline } from "ionicons/icons"
import { ARTLabService } from '../../services/art_lab_service'
import dayjs from 'dayjs'
import artProp from '../../art_global_props'

interface VLResponse {
    eligibile: boolean
    milestone: string | null
    skip_milestone: boolean
    due_date: string
    last_order_date: string
    period_on_art: number
    earliest_start_date: string
    message: string | null
    current_regimen: {
        name: string
        date_started: string
    }
    previous_regimen: unknown
}

const props = defineProps<{ patientID: number }>()

const isVisible = ref(true)
const vlData = ref<VLResponse | null>(null)
const isVlAlertEnabled = ref(false)

const waitTillNextMilestone = async (): Promise<void> => {
    if (await alertConfirmation('Are you sure you want to wait till next milestone?')) {
        const orderService = new ARTLabService(props.patientID, -1);
        const encounter = await orderService.createEncounter();
        const observations = await orderService.buildDefferedOrder(null);
        if (!encounter) return toastWarning("Unable to create encounter");
        const res = await orderService.saveObservationList(observations);
        isVisible.value = res ? false : true
    }
}

const remindMeLater = async (): Promise<void> => {
    if (await alertConfirmation('Are you sure you want to remind me later?')) {
        isVisible.value = false
    }
}

const handleClose = async (): Promise<void> => {
    if (await alertConfirmation('Are you sure you want to close this milestone notification?')) {
        isVisible.value = false
    }
}

const fetchVLData = async (): Promise<void> => {
    try {
        isVlAlertEnabled.value = await artProp.VLEnabled()
        if (!isVlAlertEnabled.value) return
        const data = await ProgramService.getPatientVLInfo(props.patientID)
        vlData.value = data
    } catch (err) {
        console.error('Error fetching VL data:', err);
    }
}

const formatDate = (dateStr: string): string => {
    return dateStr ? dayjs(dateStr).format('DD MMM, YYYY') : 'N/A'
}

onMounted(() => {
    fetchVLData()
})
</script>


<style scoped>
.info-container {
    margin: 0 auto;
}

.info-card {
    background: #fafafa;
    border: 1px solid #ececec;
    border-radius: 5px;
    padding: 20px;
    margin-bottom: 16px;
    position: relative;
}

.close-button {
    position: absolute;
    top: 16px;
    right: 16px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: color 0.2s ease;
}

.close-icon {
    width: 20px;
    height: 20px;
}

.main-message {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;
    padding-right: 40px;
}

.info-icon {
    width: 40px;
    height: 40px;
    margin-right: 16px;
    margin-top: 4px;
    flex-shrink: 0;
}

.message-content h2 {
    color: #374151;
    font-size: 1.3rem;
    font-weight: 600;
    margin: 0 0 12px 0;
}

.info-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.info-list li {
    color: #6b7280;
    font-size: 0.95rem;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
}

.info-list li::before {
    content: "−";
    color: #ececec;
    font-weight: bold;
    margin-right: 8px;
    font-size: 1.2rem;
}

.data-rows {
    margin-top: 0;
}

.data-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #ececec;
}

.data-row:last-child {
    border-bottom: none;
}

.data-label {
    color: #374151;
    font-weight: 500;
    display: flex;
    align-items: center;
}

.data-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
}

.data-value {
    color: #111827;
    font-weight: 600;
}

.months-value {
    color: #ef4444;
    font-size: 1.2rem;
    font-weight: bold;
}

.na-value {
    color: #6b7280;
    font-style: italic;
}

.learn-more-section {
    margin-top: 16px;
}
</style>