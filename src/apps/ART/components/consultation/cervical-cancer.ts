import { ref, computed } from 'vue';
import { ConsultationService } from '../../services/consultation_service';
import ART_PROP from '../../art_global_props';

export function useCervicalCancer(consultation: ConsultationService, patient: any) {
  const CxCaEnabled = ref<boolean>(false);
  const CxCaMaxAge = ref<number>(0);
  const CxCaStartAge = ref<number>(0);
  const DueForCxCa = ref<boolean>(false);
  const clientHadAHysterectomy = ref<boolean>(false);
  const CxCaAppointDate = ref(null);
  const isPregnant = ref<boolean>(false);

  const canScreenCxCa = computed(() => {
    if (!patient.isFemale()) return false;
    if (!CxCaEnabled.value) return false;
    
    const patientAge = patient.getAge();
    if (patientAge < CxCaStartAge.value || patientAge > CxCaMaxAge.value) return false;
    if (clientHadAHysterectomy.value) return false;
    
    return true;
  });

  const getReasonsForNoCxcaOptions = () => {
    return [
      { label: 'Not due for screening', value: 'Not due for screening' },
      { label: 'Hysterectomy', value: 'Hysterectomy' },
      { label: 'Patient refused', value: 'Patient refused' },
      { label: 'Other', value: 'Other' }
    ];
  };

  const yesNoOptions = () => {
    return [
      { label: 'Yes', value: 'Yes' },
      { label: 'No', value: 'No' }
    ];
  };

  const mapStrToOptions = (options: string[]) => {
    return options.map(option => ({
      label: option,
      value: option
    }));
  };

  const initCxCaData = async (): Promise<void> => {
    if (patient.isFemale()) {
      CxCaEnabled.value = await ART_PROP.cervicalCancerScreeningEnabled();
      
      if (CxCaEnabled.value) {
        const { start, end } = await ART_PROP.cervicalCancerScreeningAgeBounds();
        CxCaMaxAge.value = end;
        CxCaStartAge.value = start;
        DueForCxCa.value = await consultation.clientDueForCxCa();
        clientHadAHysterectomy.value = await consultation.clientHasHadAHysterectomy();
        CxCaAppointDate.value = await patient.nextAppointment(24);
      }
    }
  };

  return {
    CxCaEnabled,
    CxCaMaxAge,
    CxCaStartAge,
    DueForCxCa,
    clientHadAHysterectomy,
    CxCaAppointDate,
    isPregnant,
    patient,
    canScreenCxCa,
    getReasonsForNoCxcaOptions,
    yesNoOptions,
    mapStrToOptions,
    initCxCaData
  };
}