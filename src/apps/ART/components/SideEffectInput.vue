<template>
    <ion-card>
        <ion-card-content>
            <ion-item lines="none">
                <ion-label> Side effect suspected causes </ion-label>
            </ion-item>
            <div>
                <div v-if="currentDrugs.length === 0" style="border: 1px dashed #ccc; padding: 20px;">
                    <h1 class="ion-text-center">
                        No previous dispensations found
                    </h1>
                </div>
                <div v-else>
                    <BasicRadioSelect label="Current Medication" v-model="selectedDrug"
                        @on-change="() => selectedOther = null" :options="currentDrugs" />
                </div>
            </div>
            <div>
                <BasicRadioSelect label="Previous Medication" v-model="selectedOther"
                    @on-change="() => selectedDrug = null" :options="otherOptions" />
            </div>
            <ion-button :disabled="!(selectedDrug || selectedOther)" class="ion-padding" @click="saveReason">
                Save
            </ion-button>
        </ion-card-content>
    </ion-card>
</template>
<script lang="ts" setup>
import {
    IonButton,
    IonItem,
    IonLabel,
    IonCard,
    IonCardContent
} from "@ionic/vue"
import BasicRadioSelect from "@/components/Forms/BasicFormFields/BasicRadioSelect.vue";
import { onMounted, PropType, ref } from "vue"
import { ConsultationService } from "../services/consultation_service";
import { useDemographicsStore } from "@/stores/DemographicStore";
import { storeToRefs } from "pinia";
import { ConceptService } from "@/services/concept_service";

const props = defineProps({
    contraIndicationConceptName: {
        type: String,
        required: true
    },
    onReason: {
        type: Object as PropType<(reason: any) => void>,
        required: false
    }
})
const selectedDrug = ref<any>(null)
const selectedOther = ref<any>(null)
const demographicsStore = useDemographicsStore();
const { patient } = storeToRefs(demographicsStore) as any;
const consultationService = new ConsultationService(patient.value.patientID, -1)

const currentDrugs = ref<any[]>([])
const otherOptions = ref<any[]>([
    { label: 'Other, not drug related', value: 'Other, not drug related' },
    { label: 'Drug side effect', value: 'Drug side effect' }
])

async function saveReason() {
    if (props.onReason && typeof props.onReason === 'function') {
        const value = {
            type: (() => {
                if (selectedDrug.value) {
                    return 'Drug induced'
                } else {
                    return selectedOther.value
                }
            })(),
            obs: {
                concept_id: (await (ConceptService.getCachedConceptID('Drug induced', true) as any))?.[0]?.concept_id,
                value_coded: (await (ConceptService.getCachedConceptID(props.contraIndicationConceptName, true) as any))?.[0]?.concept_id,
                value_text: selectedOther.value ? 'Past medication history' : null,
                value_drug: selectedDrug.value ? selectedDrug.value : null
            }
        }
        props.onReason(value)
    }
}

onMounted(() => {
    consultationService.getPreviousDrugs().then((drugs) => {
        currentDrugs.value = Object.keys(drugs).map((k: any) => ({
            label: drugs[k].drug.name,
            value: Number(k)
        }))
    })
})
</script>
<style scoped>
ion-label {
    font-size: 1.3rem;
    font-weight: bold;
}
</style>
