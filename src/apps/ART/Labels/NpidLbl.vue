<template>
    <print-label id="npid-label" @label-output="(data: any) => emit('label-out', data)">
        <div id="container">
            <div v-if="!useQrCode">
                <div>
                    <div>
                        <div>{{ name }}</div>
                        <div>{{ national_id }} {{ birthdate }} {{ sex }}</div>
                        <div>{{ address }}</div>
                    </div>
                    <svg id="patient-barcode"></svg>
                </div>
            </div>
            <div v-else>
                <div>
                    <div style="display: flex; align-items: center;">
                        <div>
                            <canvas id="patient-qrcode"></canvas>
                        </div>
                        <div>
                            <div>{{ name }}</div>
                            <div>{{ national_id }}</div>
                            <div>{{ birthdate }}</div>
                            <div>{{ sex }}</div>
                            <div>{{ address }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </print-label>
</template>

<script lang="ts" setup>
import PrintLabel from "@/components/LBL/PrintLabel.vue"
import JsBarcode from "jsbarcode";
import { onMounted } from "vue";
import QRCode from 'qrcode'
import { toastWarning } from "@/utils/Alerts";

const emit = defineEmits(['label-out'])

const props = defineProps({
    name: {
        type: String,
        default: "??"
    },
    national_id: {
        type: String,
        required: true,
        default: "??"
    },
    birthdate: {
        type: String,
        default: "??"
    },
    sex: {
        type: String,
        gender: "??"
    },
    address: {
        type: String,
        default: "??"
    },
    useQrCode: {
        type: Boolean
    },
    qr: {
        type: String
    }
})

onMounted(() => {
    if (!props.useQrCode) {
        JsBarcode("#patient-barcode", props.national_id, {
            height: 56,
            width: 2,
            displayValue: false,
            format: "CODE128"
        })
    } else {
        const canvas = document.getElementById("patient-qrcode")
        if (canvas && props.qr) {
            QRCode.toCanvas(canvas, props.qr, { width: 160 }, (error: any) => {
                if (error) {
                    console.log(error)
                    toastWarning("Unable to generate QR code")
                }
            })
        }
    }
})
</script>
<style scoped>
#container {
    max-width: 578px;
    max-height: 316px;
    font-size: 30px;
    font-weight: bold;
    overflow: hidden;
}
</style>