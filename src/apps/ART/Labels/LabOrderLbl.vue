<template>
    <print-label id="accession-number" @label-output="(data: any) => emit('label-out', data)">
        <div id="container">
            <ion-grid>
                <ion-row>
                    <ion-col size="4">
                        <div class="rotated">
                            <span style="margin-left:35%">{{ reason }}</span>
                        </div>
                    </ion-col>
                    <ion-col size="8">
                        <div>
                            <div>
                               <span>{{ fullName }}&nbsp;</span>
                               <span>{{ npid }}</span>
                            </div>
                            <div>{{ date }}</div>
                            <svg id="accession-barcode"></svg>
                            <div>
                                <span>{{ accession }} # {{ accession }}</span>
                            </div>
                            <div> col: {{ collectionDate }}</div>
                            <div> {{ testName }} </div>
                        </div>
                    </ion-col>
                </ion-row>
            </ion-grid>
        </div>
    </print-label>
</template>

<script lang="ts" setup>
import PrintLabel from "@/components/LBL/PrintLabel.vue"
import JsBarcode from "jsbarcode";
import { onMounted } from "vue";
import { IonGrid, IonRow, IonCol } from "@ionic/vue" 

const emit = defineEmits(['label-out'])

const props = defineProps({
    fullName: {
        type: String,
        required: true
    },
    npid: {
        type: String,
        required: true
    },
    date: {
        type: String,
        required: true
    },
    accession: {
        type: String,
        required: true
    },
    reason: {
        type: String,
        required: true
    },
    collectionDate: {
        type: String,
        required: true
    },
    testName: {
        type: String,
        required: true
    }
})
onMounted(() => {
    JsBarcode("#accession-barcode", `${props.accession}`, {
        height: 40,
        width: 2,
        displayValue: false,
        format: "CODE128"
    })
})
</script>
<style scoped>
#container {
    width: 578px;
    max-height: 316px;
    font-size: 18px;
    font-weight: bold;
    overflow: hidden
}
.rotated {
    width: 280px;
    height: 40px;
    font-weight: bold;
    color: white;
    font-size: 16px;
    background-color: black;
    transform: rotate(90deg); /* Rotate the div 45 degrees */
    transform-origin: bottom; /* Rotate around the center (default) */
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>