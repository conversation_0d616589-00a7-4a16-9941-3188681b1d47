<template>
    <print-label id="art_demographics" @label-output="(data: any) => emit('label-out', data)">
        <div id="container">
            <div class="label">
                <div style="font-weight: 600" class="row">
                    <div>PATIENT DETAILS</div>
                    <div>{{ arv_number }}</div>
                </div>
                <div>
                    <div>Name: {{ patient_details?.name }}</div>
                    <div>DOB: {{ patient_details?.dob  }}</div>
                    <div>Phone: {{ patient_details?.phone }}</div>
                    <div>Addr: {{ patient_details?.address }}</div>
                    <div>Guard: {{ patient_details?.guardian }}</div>
                    <div style="margin-top: 2%">
                        <div>TI: {{ patient_details?.transfer_in }}</div>
                        <div>FUP: {{ patient_details?.agrees_to_followup }}</div>
                    </div>
                </div>
            </div>
            <div style="margin:150px;"></div>
            <div style="max-height: 316px; overflow: hidden;">
                <div class="row">
                    <div><b>STATUS AT ART INITIATION</b></div>
                    <div>(DSA: {{ art_initiation_status?.art_start_date }})</div>
                    <div><b>{{ arv_number }}</b></div>
                </div>
                <div class="row">
                    <div><b>RFS: </b>{{ art_initiation_status?.reason_for_art_eligibility }}</div>
                    <div><b>TB: </b>{{ art_initiation_status?.tb_status }}</div>
                    <div><b>HEIGHT: </b>{{ art_initiation_status?.initial_height }}</div>
                </div>
                <div class="row">
                    <div><b>KS: </b>{{ art_initiation_status?.ks_status }}</div>
                    <div><b>WEIGHT: </b>{{ art_initiation_status?.initial_weight }}</div>
                </div>
                <div class="row">
                    <div><b>1st + Test:</b> {{ art_initiation_status?.hiv_test_date }}</div>
                    <div><b>Preg: </b>{{ art_initiation_status?.pregnant }}</div>
                    <div><b>Init Age: </b>{{ art_initiation_status?.age_at_initiation }}</div>
                </div>
                <div style="border-top: 3px solid black; width:100%"></div>
                <div style="font-weight:bold; display: flex; justify-content: right">
                    STAGE DEFINING CONDITIONS
                </div>
                <div style="margin-top: 14px;">
                    {{[
                        ...art_initiation_status?.first_line_drugs??[], 
                        ...art_initiation_status?.alt_first_line_drugs ?? [], 
                        ...art_initiation_status?.second_line_drugs ?? []
                    ].join(",")}}
                </div>
            </div>
        </div>
    </print-label>
</template>

<script lang="ts" setup>
import PrintLabel from "@/components/LBL/PrintLabel.vue"
const emit = defineEmits(['label-out'])

defineProps({
    arv_number: {
        type: String
    },
    patient_details: {
        type: Object
    },
    art_initiation_status: {
        type: Object
    }
})
</script>
<style scoped>
#container {
    font-size: 44px;
    overflow: hidden;
}
.label {
    overflow: hidden;
}
.row {
    display:flex; 
    justify-content: space-between;
}
</style>