<template>
    <print-label id="transferout" @label-output="(data: any) => emit('label-out', data)">
        <div id="container">
            <div class="label">
                <div>
                    <div class="title">{{ currentFacility ?? "???" }} transfer out label</div>
                    <div class="content">To {{ toFacility ?? "???" }}</div>
                </div>
                <div>
                    <div class="title">ARV number: {{ arv }}</div>
                    <div class="content">
                        <div>Name: {{ fullName ?? "??" }} ({{ gender ?? "??" }})</div>
                        <div>Age: {{ age ?? "??" }}</div>
                    </div>
                </div>
                <div>
                    <div class="title">Stage defining conditions:</div>
                    <div class="content">
                        <div>Reason for starting: {{ reason_for_starting ?? "??" }}</div>
                        <div>ART start date: {{ art_start_date ?? "??" }}</div>
                    </div>
                </div>
            </div>
            <div class="dynamic-label">
                <div>
                    <div class="title">Other diagnosis:</div>
                    <div class="content">{{ other_diagnosis }}</div>
                </div>
            </div>
            <div>
                <div class="title">Initial Height/Weight</div>
                <div class="content">
                    <span>Init HT: {{ init_height }}&nbsp;&nbsp;</span>
                    <span>Init WT: {{ init_weight }}</span>
                    <div> CD count {{ cd_count ?? "??" }}</div>
                </div>
            </div>
            <div>
                <div class="title">Current ART drugs</div>
                <span class="content">{{ drugs }}</span>
            </div>
            <div>
                <div class="title">Transfer out date:</div>
                <span class="content">{{ transfer_date ?? "??" }}</span>
            </div>
        </div>
    </print-label>
</template>

<script lang="ts" setup>
import PrintLabel from "@/components/LBL/PrintLabel.vue"

const emit = defineEmits(['label-out'])

defineProps({
    currentFacility: String,
    toFacility: String,
    gender: String,
    arv: String,
    fullName: String,
    age: Number,
    reason_for_starting: String,
    art_start_date: String,
    other_diagnosis: String,
    init_height: String,
    init_weight: String,
    cd_count: String,
    drugs: String,
    transfer_date: String
})
</script>
<style scoped>
#container {
    width: 1578px;
    font-size: 80px;
    overflow: hidden;
    font-weight: 400;
}
.label {
    height: 1010px;
    overflow: hidden;
}
.dynamic-label {
    max-height: 316px;
    overflow: hidden;
}
.title {
    font-size: 90px;
    font-weight: 600;
}
</style>