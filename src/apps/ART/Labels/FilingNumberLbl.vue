<template>
    <print-label id="art-filing-number" @label-output="(data: any) => emit('label-out', data)">
        <div id="container">
            <div class="header-text">
                {{ number ?? "??" }}
            </div>
            <p style="margin:15px;"></p>
            <div>
                <div>Filing area {{ file_type ?? "??" }}</div>
                <div>Version number: {{ version_number ?? "??" }}</div>
            </div>
        </div>
    </print-label>
</template>
<script setup lang="ts">
import PrintLabel from "@/components/LBL/PrintLabel.vue"

const emit = defineEmits(['label-out'])

defineProps({ 
    number: String,
    version_number: String,
    file_type: String
})
</script>
<style scoped>
#container {
    max-width: 578px;
    font-size: 38px;
}
.header-text {
    font-size: 68px;
}
</style>
