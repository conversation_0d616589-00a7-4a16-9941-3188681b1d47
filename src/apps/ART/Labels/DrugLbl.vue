<template>
    <print-label id="art-drug-label" @label-output="(data: any) => emit('label-out', data)">
        <div id="container">
            <div>{{ name }}</div>
            <div>Quantity: {{ quantity }}</div>
            <div>
                <svg id="drug-barcode"></svg>
            </div>
        </div>
    </print-label>
</template>
<script lang="ts" setup>
import { onMounted } from "vue"
import PrintLabel from '@/components/LBL/PrintLabel.vue';
import JsBarcode from "jsbarcode";

const emit = defineEmits(['label-out'])

const props = defineProps({
    name: String,
    quantity: Number,
    barcode: String
})

onMounted(() => {
    JsBarcode("#drug-barcode", props.barcode??"0", {
        height: 90,
        width: 3,
        displayValue: true,
        format: "CODE128"
    })
})
</script>
<style scoped>
#container {
    max-width: 575.5px;
    max-height: 316px;
    font-size: 30px;
    font-weight: bold;
    overflow: hidden;
}
</style>