<template>
    <print-label id="visit-summary" @label-output="(data: any) => emit('label-out', data)">
        <div id="container">
            <div style="display: flex; justify-content: space-between; width: 100%;">
                <span><b>{{ date }}</b></span>
                <span>({{ visit_by ?? "??" }})</span>
                <span style="background: black; font-weight:bold; color: white; padding: 1.7px;">{{ arv_number }}</span>
            </div>
            <div style="width: 100%">
                <span><b>{{ patient_details }}</b></span>
                <span><b>&nbsp;:Patient visit</b></span>
            </div>
            <div style="display: flex; justify-content: space-between; width: 100%;">
                <span>{{ weight }}kg</span>
                <span>{{ height }}cm</span>
                <span><b>BMI:</b>{{ bmi }}</span>
                <span><b>VL:</b>{{ viral_load }}</span>

            </div>
            <div style="display: flex; justify-content: space-between; border-bottom: 3px solid black; width: 100%;">
                <span><b>OUTC:</b>{{ outcome }}&nbsp;</span>
                <span>{{ toDate(outcome_date??"") }} </span>
            </div>
            <table style="width: 100%;text-align:center;">
                <thead>
                    <tr>
                        <th>SE</th>
                        <th>TB</th>
                        <th>Adh</th>
                        <th>DRUG(S) Given</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td></td>
                        <td>{{ tb_status }}</td>
                        <td>{{ adherence }}</td>
                        <td>
                            <ul>
                                <li v-for="(drug, index) in drugs" :key="index">
                                    {{  drug }}
                                </li>
                            </ul>
                        </td>
                    </tr>
                </tbody>
            </table>
            <p></p>
            <div style="text-align: right;">
                <div>
                    <div>Next: {{ next_appointment_date }}</div>
                    <div>{{ seen_by }}</div>
                </div>
            </div>
        </div>
    </print-label>
</template>

<script lang="ts" setup>
import PrintLabel from "@/components/LBL/PrintLabel.vue"
import { toDate } from "@/utils/Strs";
const emit = defineEmits(['label-out'])

defineProps({
    visit_by: {
        type: String
    },
    date: {
        type: String
    },
    arv_number: {
        type: String
    },
    patient_details: {
        type: String
    },
    height: {
        type: String
    },
    weight: {
        type: String
    },
    bmi: {
        type: String
    },
    viral_load: {
        type: String
    },
    outcome_date: {
        type: String
    },
    outcome: {
        type: String
    },
    next_appointment_date: {
        type: String
    },
    seen_by: {
        type: String
    },
    se: {
        type: String
    },
    tb_status: {
        type: String
    },
    adherence: {
        type: String
    },
    drugs: {
        type: Object
    }
})
</script>
<style scoped>
#container {
    width: 1078px;
    font-size: 58px;
    overflow: hidden;
}
ul, ol {
    list-style-type: none;  /* Removes bullet points or numbers */
    padding-left: 0;        /* Removes padding */
    margin: 0;              /* Removes margin */
}
</style>