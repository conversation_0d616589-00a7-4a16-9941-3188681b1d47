<template>
  <ion-modal :is-open="isOpen" :show-backdrop="true" @didDismiss="closeModal">
    <ion-header>
      <ion-title class="modalTitle">Enroll client in ANC program</ion-title>
    </ion-header>
    <ion-content :fullscreen="true" class="ion-padding" style="--background: #fff">
      <basic-form :contentData="ConfirmPregnancy"></basic-form>
      <hr class="dashed-hr" style="margin-bottom: 0px !important" />
      <div v-if="showUnderageWarning" class="underage-warning">
        <ion-icon :icon="warningIcon" style="color: #ffc107; margin-right: 8px;"></ion-icon>
        <span>Please note: The client you are enrolling is below 9 years of age.</span>
      </div>
    </ion-content>
    <ion-footer collapse="fade" class="ion-no-border">
      <ion-row>
        <ion-col>
          <ion-button id="cbtn" class="btnText cbtn" fill="solid" style="width: 130px" @click="handleCancel">
            Cancel
          </ion-button>
        </ion-col>
        <ion-col>
          <DynamicButton
              name="Submit"
              @click="onYes"
              fill="solid"
              style="float: right; margin: 2%; width: 130px"
          />
        </ion-col>
      </ion-row>
    </ion-footer>
  </ion-modal>
</template>

<script lang="ts">
import { defineComponent, PropType, ref, watch } from "vue";
import {
  IonModal,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonButtons,
  IonButton,
  IonContent,
  IonIcon,
  IonFooter,
  IonLabel,
  IonRow,
  IonCol,
  modalController,
} from "@ionic/vue";
import { closeCircleOutline, warning } from "ionicons/icons"; // Import the warning icon
import DynamicButton from "@/components/DynamicButton.vue";
import BasicForm from "@/components/BasicForm.vue";
import { mapState } from "pinia";
import { useANCEnrollmentStore } from "@/apps/ANC/store/enrollment/ANCEnrollment";
import HisDate from "@/utils/Date";
import { useDemographicsStore } from "@/stores/DemographicStore";
import {SetProgramService} from "@/services/set_program_service";

export default defineComponent({
  name: "AncEnrollmentModal",
  components: {
    BasicForm,
    IonCol,
    IonRow,
    IonLabel,
    IonFooter,
    DynamicButton,
    IonModal,
    IonHeader,
    IonToolbar,
    IonTitle,
    IonButtons,
    IonButton,
    IonContent,
    IonIcon,
  },
  props: {
    isOpen: {
      type: Boolean as PropType<boolean>,
      required: true,
      default: false,
    },
    closeModalFunc: {
      type: Function as PropType<() => void>,
      required: true,
    },
    onYes: {
      type: Function as PropType<() => void>,
      required: true,
    },
    onNo: {
      type: Function as PropType<() => void>,
      required: true,
    },
    title: {
      type: String as PropType<string>,
      required: true,
    },
    showUnderageWarning: {
      type: Boolean as PropType<boolean>,
      required: true,
      default: false,
    },
  },
  setup() {
    const warningIcon = warning;

    return {
      warningIcon,
    };
  },
  computed: {
    ...mapState(useANCEnrollmentStore, ["ConfirmPregnancy"]),
  },
  methods: {
    handleCancel() {
      modalController.dismiss();
    },
    closeCircleOutline() {
      return closeCircleOutline;
    },
    closeModal() {
      this.closeModalFunc();
    },
  },
});
</script>

<style scoped>
ion-header {
  --background: var(--ion-color-primary);
}

.custom-dropdown .vs__selected-options,
.custom-dropdown .vs__dropdown-option {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.custom-dropdown .vs__dropdown-menu {
  max-height: 150px;
  overflow-y: auto;
  background-color: red;
}
.PI-cls-1 {
  cursor: pointer;
}
.lbl-tl {
  min-width: 20px;
  color: #b3b3b3 !important;
  white-space: nowrap;
}
.form-row {
  display: flex;
  align-items: center;
}
.lbl-ct {
  white-space: nowrap;
  color: #636363;
  flex: 1;
}
pim-cls-1 {
  --background: #ffff;
}
.custom-toolbar-cls {
  --background: #ffff;
}
ion-footer {
  --ion-toolbar-background: #fff;
}
ion-modal {
  --height: 50%;
  --border-radius: 20px;
}
.dashed-hr {
  border: none;
  border-top: 1px dashed #b3b3b3;
  margin: 20px 0;
}
.modal_wrapper {
  padding: 0px 1px;
  background: #fff;
}
.PI-cls-1 {
  color: #1f2221d4;
}
.OtherVitalsTitle {
  font-style: normal;
  font-weight: 600;
  font-size: 20px;
  color: #00190e;
}
.OtherVitalsHeading {
  display: flex;
  justify-content: center;
  margin: 10px;
}
ion-accordion {
  margin-bottom: 15px;
}

.underage-warning {
  display: flex;
  align-items: center;
  padding: 12px;
  background-color: #fff3cd;
  border: 1px solid #ffeeba;
  border-radius: 4px;
  margin: 16px;
  color: #856404;
  font-size: 14px;
}
</style>