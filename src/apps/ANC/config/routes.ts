export default [
    {
        path: "/profile",
        name: "profile",
        component: () => import("../views/Profile.vue"),
    },
    {
        path: "/quickCheck",
        name: "quickCheck",
        component: () => import("@/apps/ANC/views/QuickCheck.vue"),
    },
    {
        path: "/headssAssessment",
        name: "headsAssessment",
        component: () => import("@/apps/ANC/views/headssAssessment.vue"),
    },
    {
        path: "/physicalExamination",
        name: "physicalExamination",
        component: () => import("@/apps/ANC/views/physicalExamination.vue"),
    },
    {
        path: "/symptomsFollowUp",
        name: "symptomsFollowUp",
        component: () => import("../views/symptomsFollowUp.vue"),
    },
    {
        path: "/counselling",
        name: "counselling",
        component: () => import("../views/counselling.vue"),
    },
    {
        path: "/referral",
        name: "referral",
        component: () => import("@/apps/ANC/views/referral.vue"),
    },
    {
        path: "/ancEnd",
        name: "ancEnd",
        component: () => import("../views/ancEnd.vue"),
    },
    // {
    //   path: '/treatment',
    //   name: 'treatment',
    //   component: Treatment
    // },
    {
        path: "/ANCtreatment",
        name: "ANCTreatment",
        component: () => import("@/apps/ANC/views/ANCTreatment.vue"),
    },
    {
        path: "/labTests",
        name: "labTests",
        component: () => import("../views/LabTests.vue"),
    },
    {
        path: "/ancReferral",
        name: "ancReferral",
        component: () => import("@/apps/ANC/views/ANCreferral.vue"),
    },
    {
        path: "/ANChome",
        name: "ANCHome",
        component: () => import("../views/ANCHome.vue"),
    },
    {
        path: "/contact",
        name: "Contacts",
        component: () => import("@/apps/ANC/components/home/<USER>"),
    },
    {
        path: "/contacts",
        name: "Appointments",
        component: () => import("@/apps/ANC/views/Appointments.vue"),
    },
];
