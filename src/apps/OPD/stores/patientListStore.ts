import { defineStore } from "pinia";
import { getOfflineRecords } from "@/services/offline_service";
import { PatientOpdList } from "@/services/patient_opd_list";
import { Service } from "@/services/service";
import { getPatientsList } from "@/apps/OPD/services/opd_dashboard";
import { getDailyAppointments, getTodaysVisits } from "../services/visits_service";
import HisDate from "@/utils/Date";

interface PatientStage {
    id: string | number;
    patient_id: string | number;
    visit_id: string | number;
    status: string | number | boolean;
    stage: "VITALS" | "CONSULTATION" | "LAB" | "DISPENSATION";
    location_id: string;
    arrivalTime?: string;
}

export const usePatientList = defineStore("patientList", {
    state: () => ({
        patientsWaitingForVitals: [] as PatientStage[],
        patientsVisits: [] as PatientStage[],
        patientsWaitingForConsultation: [] as PatientStage[],
        patientsWaitingForLab: [] as PatientStage[],
        patientsWaitingForDispensation: [] as PatientStage[],
        lastUpdated: null as Date | null,
        isLoading: false,
        error: null as string | null,
        counter: 0,
        todaysAppointmentsCount: [],
    }),
    actions: {
        async refresh(locationId: string) {
            this.isLoading = true;
            this.error = null;

            try {
                const apiStatus = Service.getAPIStatus();
                const useMODS = Service.getModsStatus();
                const useIndexDB = Service.getUseIndexDBStatus();

                const patientsVisits = await getTodaysVisits();
                this.todaysAppointmentsCount = await getDailyAppointments(HisDate.toStandardHisFormat(Service.getSessionDate()));
                this.patientsVisits = patientsVisits || [];
                if (useMODS || useIndexDB) {
                    const stages = ((await getOfflineRecords("stages")) as PatientStage[]) || [];
                    if (stages.length === 0) {
                        this.clearAll();
                        return;
                    }

                    this.patientsWaitingForVitals = this.filterByStage(stages, "VITALS", locationId);
                    this.patientsWaitingForConsultation = this.filterByStage(stages, "CONSULTATION", locationId);
                    this.patientsWaitingForLab = this.filterByStage(stages, "LAB", locationId);
                    this.patientsWaitingForDispensation = this.filterByStage(stages, "DISPENSATION", locationId);
                } else if (apiStatus) {
                    this.patientsWaitingForVitals = await PatientOpdList.getPatientList("VITALS", locationId);
                    this.patientsWaitingForConsultation = await PatientOpdList.getPatientList("CONSULTATION", locationId);
                    this.patientsWaitingForLab = await PatientOpdList.getPatientList("LAB", locationId);
                    this.patientsWaitingForDispensation = await PatientOpdList.getPatientList("DISPENSATION", locationId);
                    this.counter++;
                } else {
                    this.error = "Set the modes properly.";
                    this.clearAll();
                }

                this.lastUpdated = new Date();
            } catch (error) {
                this.error = error instanceof Error ? error.message : "Failed to load patient data";
                this.clearAll();
            } finally {
                this.isLoading = false;
            }
        },

        filterByStage(data: PatientStage[], stage: PatientStage["stage"], locationId: string | number): PatientStage[] {
            return data.filter((patient) => patient.stage === stage && patient.location_id === locationId);
        },

        clearAll() {
            this.patientsWaitingForVitals = [];
            this.patientsWaitingForConsultation = [];
            this.patientsWaitingForLab = [];
            this.patientsWaitingForDispensation = [];
            this.lastUpdated = null;
        },

        getCounts() {
            return {
                vitals: this.patientsWaitingForVitals.length,
                consultation: this.patientsWaitingForConsultation.length,
                lab: this.patientsWaitingForLab.length,
                dispensation: this.patientsWaitingForDispensation.length,
                lastUpdated: this.lastUpdated,
                isLoading: this.isLoading,
                error: this.error,
            };
        },
    },
});
