import { getOfflineRecords, saveDataOfflineAPI } from "@/services/offline_service";
import { Service } from "@/services/service";
import { useWorkerStore } from "@/stores/workerStore";
import { toastSuccess } from "@/utils/Alerts";

export class StagesService {
    static async addPatientToStage(patient: any, stage: string) {
        const response = await Service.postAPIOfflineJson(
            `/stages`,
            {
                stages: [
                    {
                        identifier: patient.ID,
                        stage,
                        arrivalTime: Service.getSessionTimeDate(),
                        sync_status: "pending",
                    },
                ],
                patient,
            },
            "stages"
        );
    }

    static addPatientToStageOffline = async (params: any) => {
        const data = params.stages[0];
        const locationId = Service.getUserLocationId();
        const timestamp = Service.getSessionTimeDate();
        const fullName = await this.getFullname(params.patient);
        if (!fullName) return;
        const stageData = {
            identifier: data.identifier,
            stage: data.stage,
            location_id: locationId,
            arrivalTime: timestamp,
            status: 1,
            fullName,
            sync_status: "pending",
        };
        await saveDataOfflineAPI("stages", stageData, { identifier: data.identifier });
        toastSuccess(`Patient queued for ${data.stage} (offline)`);
        return stageData;
    };

    static getFullname = async (patient: any) => {
        const { given_name, family_name } = patient?.personInformation || {};
        return [given_name, family_name].filter(Boolean).join(" ") || undefined;
    };
}
