import { Service } from "@/services/service";
import { toastSuccess, toastDanger } from "@/utils/Alerts";
import { getOfflineRecords } from "@/services/offline_service";
import { useWorkerStore } from "@/stores/workerStore";
import { checkLiveAPIHealthViaMODS } from "@/services/mods_service";

interface StageRecord {
    id?: number;
    patient_id: number;
    visit_id?: number;
    stage: string;
    arrivalTime: string;
    status: number;
    location_id: string;
    fullName?: string;
    sync_status?: "pending" | "failed" | "synced";
}

const STORE_NAME = "stages";
const UNSAVED_STORE_NAME = "unsavedStages";

export class StagesService {
    /**
     * Determines the operational mode based on system status
     */
    private static getOperationMode() {
        const apiStatus = Service.getAPIStatus();
        const modsStatus = Service.getModsStatus();
        const indexDBStatus = Service.getUseIndexDBStatus();

        if (apiStatus) return "API_ONLY";
        if (apiStatus && indexDBStatus) return "API_WITH_INDEXDB";
        if (indexDBStatus) return "INDEXDB";
        if (modsStatus && !indexDBStatus) return "MODS_ONLY";
        if (modsStatus && indexDBStatus) return "MODS_WITH_INDEXDB";
        return "OFFLINE";
    }

    /**
     * Moves a patient to a stage with automatic fallback
     */
    static async addPatientToStage(patientId: number, stage: string, locationId: string, visitId?: number): Promise<StageRecord> {
        const mode = this.getOperationMode();

        try {
            switch (mode) {
                case "API_ONLY":
                    return await this._handleApiOnly(patientId, stage, locationId, visitId);

                case "API_WITH_INDEXDB":
                    return await this._handleApiWithIndexDB(patientId, stage, locationId, visitId);

                case "INDEXDB":
                    return await this._handleApiWithIndexDB(patientId, stage, locationId, visitId);

                case "MODS_ONLY":
                    return await this._handleModsOnly(patientId, stage, locationId, visitId);

                case "MODS_WITH_INDEXDB":
                    return await this._handleModsWithIndexDB(patientId, stage, locationId, visitId);

                default:
                    throw new Error("Error adding patient mode");
            }
        } catch (error) {
            toastDanger(`Failed to move patient to ${stage}`);
            throw error;
        }
    }

    private static async _handleApiOnly(patientId: number, stage: string, locationId: string, visitId?: number): Promise<StageRecord> {
        const record = await this._addPatientToStageViaAPI(patientId, stage, locationId, visitId);
        return record;
    }

    private static async _handleApiWithIndexDB(patientId: number, stage: string, locationId: string, visitId?: number): Promise<StageRecord> {
        try {
            const record = await this._addPatientToStageViaAPI(patientId, stage, locationId, visitId);
            await this._storeInIndexDB(record);
            return record;
        } catch (apiError) {
            console.warn("API failed, falling back to IndexDB");
            return this._addPatientToStageOffline(patientId, stage, locationId, visitId);
        }
    }

    private static async _handleModsOnly(patientId: number, stage: string, locationId: string, visitId?: number): Promise<StageRecord> {
        const isLive = await checkLiveAPIHealthViaMODS();
        if (!isLive) throw new Error("MODS connection unavailable");

        const record = await this._addPatientToStageViaAPI(patientId, stage, locationId, visitId);
        toastSuccess(`Patient moved to ${stage} (MODS)`);
        return record;
    }

    private static async _handleModsWithIndexDB(patientId: number, stage: string, locationId: string, visitId?: number): Promise<StageRecord> {
        try {
            const isLive = await checkLiveAPIHealthViaMODS();
            if (isLive) {
                const record = await this._addPatientToStageViaAPI(patientId, stage, locationId, visitId);
                await this._storeInIndexDB(record);
                toastSuccess(`Patient moved to ${stage}`);
                return record;
            }
            throw new Error("MODS connection unavailable");
        } catch (error) {
            return this._addPatientToStageOffline(patientId, stage, locationId, visitId);
        }
    }

    /**
     * Moves patient to next stage (deactivates current stage)
     */
    static async movePatientToNextStage(
        patientId: number,
        currentStage: string,
        nextStage: string,
        locationId: string,
        visitId?: number
    ): Promise<StageRecord> {
        // Deactivate current stage first
        await this.safeDeactivateStages(patientId);
        return this.addPatientToStage(patientId, nextStage, locationId, visitId);
    }

    /**
     * Online: Add stage via API
     */
    private static async _addPatientToStageViaAPI(patientId: number, stage: string, locationId: string, visitId?: number): Promise<StageRecord> {
        const response = await Service.postJson(`/stages`, {
            stage: {
                patient_id: patientId,
                stage,
                location_id: locationId,
                visit_id: visitId,
                arrivalTime: Service.getSessionDate(),
            },
        });
        return response.data;
    }

    /**
     * Offline: Add stage to IndexedDB
     */
    private static async _addPatientToStageOffline(patientId: number, stage: string, locationId: string, visitId?: number): Promise<StageRecord> {
        const timestamp = Service.getSessionDate();
        const workerStore = useWorkerStore();
        const { fullName } = await this._resolvePatientDetails(patientId, locationId);

        const stageData: StageRecord = {
            patient_id: patientId,
            visit_id: visitId,
            stage,
            location_id: locationId,
            arrivalTime: timestamp,
            status: 1,
            fullName,
            sync_status: "pending",
        };

        await workerStore.postData("ADD_STAGE", {
            storeName: UNSAVED_STORE_NAME,
            data: stageData,
        });

        toastSuccess(`Patient queued for ${stage} (offline)`);
        return stageData;
    }

    /**
     * Stores record in IndexDB for backup
     */
    private static async _storeInIndexDB(record: StageRecord): Promise<void> {
        if (!Service.getUseIndexDBStatus()) return;

        const workerStore = useWorkerStore();
        await workerStore.postData("ADD_STAGE", {
            storeName: STORE_NAME,
            data: {
                ...record,
                sync_status: "synced",
            },
        });
    }

    /**
     * Deactivates all active stages for a patient
     */
    static async safeDeactivateStages(patientId: number): Promise<number> {
        const mode = this.getOperationMode();

        try {
            if (mode.includes("API")) {
                await Service.putAPIOfflineJson(`/stages/deactivate`, { patientId }, "stages");
            }

            if (mode.includes("INDEXDB") || mode === "OFFLINE") {
                await this._deactivateStagesOffline(patientId);
            }

            return 1;
        } catch (error) {
            console.error("Deactivation failed:", error);
            throw error;
        }
    }

    private static async _deactivateStagesOffline(patientId: number): Promise<void> {
        const workerStore = useWorkerStore();
        const timestamp = Service.getSessionDate();

        await workerStore.postData("UPDATE_STAGE", {
            storeName: UNSAVED_STORE_NAME,
            whereClause: { patient_id: patientId, status: 1 },
            data: { status: 0, updated_at: timestamp, sync_status: "pending" },
        });

        await workerStore.postData("UPDATE_STAGE", {
            storeName: STORE_NAME,
            whereClause: { patient_id: patientId, status: 1 },
            data: { status: 0, updated_at: timestamp, sync_status: "pending" },
        });
    }

    /**
     * Fetches current stage for a patient
     */
    static async getCurrentStage(patientId: number): Promise<string | null> {
        const mode = this.getOperationMode();

        try {
            if (mode.includes("API")) {
                const response = await Service.getJson(`/stages/current`, { patientId });
                return response.stage;
            }
        } catch (error) {
            console.warn("Online fetch failed, falling back to offline");
        }

        // Offline fallback
        const unsavedStages = (await getOfflineRecords<StageRecord>(UNSAVED_STORE_NAME, {
            whereClause: { patient_id: patientId, status: 1 },
        })) as any;
        return unsavedStages[0]?.stage || null;
    }

    /**
     * Helper: Resolves patient details for offline records
     */
    private static async _resolvePatientDetails(patientId: number, locationId: string): Promise<{ fullName?: string; visit_id?: number }> {
        const patients = (await getOfflineRecords("patientRecords", {
            whereClause: { patientID: patientId },
        })) as any;
        const patient = patients[0];

        const fullName = patient?.personInformation
            ? [patient.personInformation.given_name, patient.personInformation.family_name].filter(Boolean).join(" ")
            : undefined;

        const visits = (await getOfflineRecords("visits", {
            whereClause: { patientId, location_id: locationId },
        })) as any;
        const visit_id = visits[0]?.id;

        return { fullName, visit_id };
    }
}
