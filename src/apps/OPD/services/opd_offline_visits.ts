import { Service } from "@/services/service";
import { toastSuccess, toastDanger } from "@/utils/Alerts";
import { useWorkerStore } from "@/stores/workerStore";
import { checkLiveAPIHealthViaMODS } from "@/services/mods_service";
import { getOfflineRecords } from "@/services/offline_service";

interface VisitRecord {
    id?: number;
    patientId: number;
    startDate: string;
    closedDateTime?: string | null;
    location_id: string;
    programId?: string;
    sync_status?: "pending" | "failed" | "synced";
    updated_at?: string;
}

const STORE_NAME = "visits";
const UNSAVED_STORE_NAME = "unsavedVisits";

export class VisitsService extends Service {
    /**
     * Determines the operational mode based on system status
     */
    private static getOperationMode() {
        const apiStatus = Service.getAPIStatus();
        const modsStatus = Service.getModsStatus();
        const indexDBStatus = Service.getUseIndexDBStatus();

        if (apiStatus && !indexDBStatus) return "API_ONLY";
        if (apiStatus && indexDBStatus) return "API_WITH_INDEXDB";
        if (modsStatus && !indexDBStatus) return "MODS_ONLY";
        if (modsStatus && indexDBStatus) return "MODS_WITH_INDEXDB";
        return "OFFLINE";
    }

    /**
     * Starts a new visit with automatic fallback
     */
    static async startVisit(patientId: number, locationId: string, programId: string = "14"): Promise<VisitRecord> {
        const mode = this.getOperationMode();

        try {
            // First check for existing active visit
            const activeVisit = await this.getActiveVisit(patientId);
            if (activeVisit) {
                throw new Error("Patient already has an active visit");
            }

            switch (mode) {
                case "API_ONLY":
                    return await this._handleApiOnly(patientId, locationId, programId);

                case "API_WITH_INDEXDB":
                    return await this._handleApiWithIndexDB(patientId, locationId, programId);

                case "MODS_ONLY":
                    return await this._handleModsOnly(patientId, locationId, programId);

                case "MODS_WITH_INDEXDB":
                    return await this._handleModsWithIndexDB(patientId, locationId, programId);

                default:
                    return await this._handleOffline(patientId, locationId, programId);
            }
        } catch (error) {
            toastDanger("Failed to start visit");
            throw error;
        }
    }

    /**
     * Closes an existing visit with automatic fallback
     */
    static async closeVisit(patientId: number, closedDateTime: string = new Date().toISOString()): Promise<VisitRecord> {
        const mode = this.getOperationMode();

        try {
            const activeVisit = await this.getActiveVisit(patientId);
            if (!activeVisit) {
                throw new Error("No active visit found for patient");
            }

            switch (mode) {
                case "API_ONLY":
                    return await this._handleApiClose(patientId, closedDateTime);

                case "API_WITH_INDEXDB":
                    return await this._handleApiWithIndexDBClose(patientId, closedDateTime);

                case "MODS_ONLY":
                    return await this._handleModsClose(patientId, closedDateTime);

                case "MODS_WITH_INDEXDB":
                    return await this._handleModsWithIndexDBClose(patientId, closedDateTime);

                default:
                    return await this._handleOfflineClose(patientId, closedDateTime);
            }
        } catch (error) {
            toastDanger("Failed to close visit");
            throw error;
        }
    }

    // API Only handlers
    private static async _handleApiOnly(patientId: number, locationId: string, programId: string): Promise<VisitRecord> {
        const response = await super.postJson(`/visits`, {
            patientId: String(patientId),
            startDate: new Date().toISOString(),
            programId,
            location_id: String(locationId),
        });
        return response.data;
    }

    private static async _handleApiClose(patientId: number, closedDateTime: string): Promise<VisitRecord> {
        const activeVisit = await this.getActiveVisit(patientId);
        if (!activeVisit?.id) throw new Error("No active visit found");

        const response = await super.putAPIOfflineJson(
            `/visits/${activeVisit.id}/close`,
            {
                visit: { closedDateTime },
            },
            "visits"
        );
        toastSuccess("Visit closed (API only)");
        return response.data;
    }

    // API with IndexDB handlers
    private static async _handleApiWithIndexDB(patientId: number, locationId: string, programId: string): Promise<VisitRecord> {
        try {
            const record = await this._handleApiOnly(patientId, locationId, programId);
            await this._storeInIndexDB(record);
            return record;
        } catch (apiError) {
            console.warn("API failed, falling back to IndexDB");
            return this._handleOffline(patientId, locationId, programId);
        }
    }

    private static async _handleApiWithIndexDBClose(patientId: number, closedDateTime: string): Promise<VisitRecord> {
        try {
            const record = await this._handleApiClose(patientId, closedDateTime);
            await this._updateInIndexDB(record);
            return record;
        } catch (apiError) {
            console.warn("API failed, falling back to IndexDB");
            return this._handleOfflineClose(patientId, closedDateTime);
        }
    }

    // MODS Only handlers
    private static async _handleModsOnly(patientId: number, locationId: string, programId: string): Promise<VisitRecord> {
        const isLive = await checkLiveAPIHealthViaMODS();
        if (!isLive) throw new Error("MODS connection unavailable");

        const response = await super.postJson(`/visits`, {
            patientId: String(patientId),
            startDate: new Date().toISOString(),
            programId,
            location_id: String(locationId),
        });
        toastSuccess("Visit started (MODS only)");
        return response.data;
    }

    private static async _handleModsClose(patientId: number, closedDateTime: string): Promise<VisitRecord> {
        const isLive = await checkLiveAPIHealthViaMODS();
        if (!isLive) throw new Error("MODS connection unavailable");

        const activeVisit = await this.getActiveVisit(patientId);
        if (!activeVisit?.id) throw new Error("No active visit found");

        const response = await super.putAPIOfflineJson(
            `/visits/${activeVisit.id}/close`,
            {
                visit: { closedDateTime },
            },
            "visits"
        );
        toastSuccess("Visit closed (MODS only)");
        return response.data;
    }

    // MODS with IndexDB handlers
    private static async _handleModsWithIndexDB(patientId: number, locationId: string, programId: string): Promise<VisitRecord> {
        try {
            const isLive = await checkLiveAPIHealthViaMODS();
            if (isLive) {
                const record = await this._handleApiOnly(patientId, locationId, programId);
                await this._storeInIndexDB(record);
                toastSuccess("Visit started (MODS + IndexDB)");
                return record;
            }
            throw new Error("MODS connection unavailable");
        } catch (error) {
            console.warn("MODS failed, falling back to IndexDB");
            return this._handleOffline(patientId, locationId, programId);
        }
    }

    private static async _handleModsWithIndexDBClose(patientId: number, closedDateTime: string): Promise<VisitRecord> {
        try {
            const isLive = await checkLiveAPIHealthViaMODS();
            if (isLive) {
                const record = await this._handleApiClose(patientId, closedDateTime);
                await this._updateInIndexDB(record);
                toastSuccess("Visit closed (MODS + IndexDB)");
                return record;
            }
            throw new Error("MODS connection unavailable");
        } catch (error) {
            console.warn("MODS failed, falling back to IndexDB");
            return this._handleOfflineClose(patientId, closedDateTime);
        }
    }

    // Offline handlers
    private static async _handleOffline(patientId: number, locationId: string, programId: string): Promise<VisitRecord> {
        const timestamp = new Date().toISOString();
        const workerStore = useWorkerStore();

        const visitData: VisitRecord = {
            patientId,
            startDate: timestamp,
            closedDateTime: null,
            location_id: locationId,
            programId,
            sync_status: "pending",
            updated_at: timestamp,
        };

        await workerStore.postData("ADD_VISIT", {
            storeName: UNSAVED_STORE_NAME,
            data: visitData,
        });

        toastSuccess("Visit started (offline - will sync later)");
        return visitData;
    }

    private static async _handleOfflineClose(patientId: number, closedDateTime: string): Promise<VisitRecord> {
        const activeVisit = await this.getActiveVisit(patientId);
        if (!activeVisit) throw new Error("No active visit found");

        const workerStore = useWorkerStore();
        const storeName = activeVisit.sync_status === "pending" ? UNSAVED_STORE_NAME : STORE_NAME;

        const updateData = {
            closedDateTime,
            updated_at: new Date().toISOString(),
            sync_status: "pending",
        };

        await workerStore.postData("UPDATE_VISIT", {
            storeName,
            whereClause: { id: activeVisit.id },
            data: updateData,
        });

        toastSuccess("Visit closed (offline - will sync later)");

        return { ...activeVisit, ...updateData } as VisitRecord;
    }

    // IndexDB helpers
    private static async _storeInIndexDB(record: VisitRecord): Promise<void> {
        if (!Service.getUseIndexDBStatus()) return;

        const workerStore = useWorkerStore();
        await workerStore.postData("ADD_VISIT", {
            storeName: STORE_NAME,
            data: {
                ...record,
                sync_status: "synced",
            },
        });
    }

    private static async _updateInIndexDB(record: VisitRecord): Promise<void> {
        if (!Service.getUseIndexDBStatus() || !record.id) return;

        const workerStore = useWorkerStore();
        await workerStore.postData("UPDATE_VISIT", {
            storeName: STORE_NAME,
            whereClause: { id: record.id },
            data: {
                ...record,
                sync_status: "synced",
            },
        });
    }

    /**
     * Gets active visit for a patient (checks both online and offline)
     */
    static async getActiveVisit(patientId: number): Promise<VisitRecord | null> {
        const mode = this.getOperationMode();

        if (mode.includes("API")) {
            try {
                const response = await super.getJson(`/check_patient_status/${patientId}`);
                if (response?.activeVisit) return response.activeVisit;
            } catch (error) {
                console.warn("Online active visit check failed, falling back to offline");
            }
        }

        // Offline fallback
        try {
            // Check unsaved visits first
            const normalizeRecords = (data: { records: VisitRecord[]; totalCount: number } | VisitRecord[]): VisitRecord[] => {
                return Array.isArray(data) ? data : data.records || [];
            };

            const unsavedResult = await getOfflineRecords<VisitRecord>(UNSAVED_STORE_NAME, {
                whereClause: { patientId },
            });

            const savedResult = await getOfflineRecords<VisitRecord>(STORE_NAME, {
                whereClause: { patientId },
            });

            const unsavedVisits = normalizeRecords(unsavedResult);
            const savedVisits = normalizeRecords(savedResult);

            const activeVisit = [...unsavedVisits, ...savedVisits].find((v) => !v.closedDateTime) || null;

            return activeVisit;
        } catch (error) {
            console.error("Failed to get active visit from offline storage:", error);
            return null;
        }
    }

    /**
     * Gets all visits for a patient
     */
    static async getPatientVisits(patientId: number): Promise<VisitRecord[]> {
        const mode = this.getOperationMode();

        if (mode.includes("API")) {
            try {
                const response = await super.getJson(`/visits`, { patientId });

                if (Array.isArray(response?.visits)) {
                    return response.visits;
                } else if (response?.records && Array.isArray(response.records)) {
                    // Handle paginated response
                    return response.records;
                }
            } catch (error) {}
        }

        // Offline fallback
        try {
            const [unsavedVisits, savedVisits] = await Promise.all([
                getOfflineRecords<VisitRecord>(UNSAVED_STORE_NAME, {
                    whereClause: { patientId },
                    sortBy: "startDate",
                    sortOrder: "desc",
                }) as any,
                getOfflineRecords<VisitRecord>(STORE_NAME, {
                    whereClause: { patientId },
                    sortBy: "startDate",
                    sortOrder: "desc",
                }) as any,
            ]);

            return [...unsavedVisits, ...savedVisits].sort((a, b) => new Date(b.startDate).getTime() - new Date(a.startDate).getTime());
        } catch (error) {
            console.error("Failed to get visits from offline storage:", error);
            return [];
        }
    }

    /**
     * Syncs pending visits when coming online
     */
    static async syncPendingVisits(): Promise<{ success: boolean; synced: number; failed: number }> {
        const workerStore = useWorkerStore();
        try {
            const response = await workerStore.postData("SYNC_UNSAVED_VISITS", {});
            return {
                success: response?.success || false,
                synced: response?.syncedCount || 0,
                failed: response?.failedCount || 0,
            };
        } catch (error) {
            console.error("Failed to sync pending visits:", error);
            return {
                success: false,
                synced: 0,
                failed: 0,
            };
        }
    }
}
