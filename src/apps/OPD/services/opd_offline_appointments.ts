import { getOfflineRecords, getOfflineSavedUnsavedData } from "@/services/offline_service";
import HisDate from "@/utils/Date";

interface AppointmentRecord {
    person_id: number;
    npid: string;
    given_name: string;
    family_name: string;
    gender: string;
    birthdate: string;
    city_village: string;
    appointment_date: string;
    concept_id: number;
    value_datetime: string;
    obs_datetime: string;
    location_id: string;
}

export const OPDAppointmentService = {
    async getAppointments(startDate: string, endDate: string): Promise<AppointmentRecord[]> {
        try {
            // Get all patient records with their appointments
            const allPatients = await getOfflineRecords("patientRecords") as any[];
            const appointments: AppointmentRecord[] = [];

            for (const patient of allPatients) {
                // Get both saved and unsaved appointments for this patient
                const patientAppointments = getOfflineSavedUnsavedData('appointments');

                // Filter appointments within date range
                const filteredAppointments = patientAppointments.filter((appt: any) => {
                    const apptDate = appt.value_datetime || appt.obs_datetime;
                    return apptDate >= startDate && apptDate <= endDate &&
                        appt.concept_id === 5096;
                });

                // Map to required structure
                filteredAppointments.forEach((appt: any) => {
                    appointments.push({
                        person_id: patient.patientID,
                        npid: patient.npid || 'N/A',
                        given_name: patient.given_name || '',
                        family_name: patient.family_name || '',
                        gender: patient.gender || 'Unknown',
                        birthdate: patient.birthdate || '',
                        city_village: patient.city_village || 'Unknown',
                        appointment_date: appt.value_datetime,
                        concept_id: appt.concept_id,
                        value_datetime: appt.value_datetime,
                        obs_datetime: appt.obs_datetime,
                        location_id: appt.location_id
                    });
                });
            }

            return appointments;
        } catch (error) {
            console.error("Error fetching offline appointments:", error);
            return [];
        }
    },

    formatAppointmentForDisplay(appointments: AppointmentRecord[]) {
        return appointments.map(appt => ({
            person_id: appt.person_id,
            npid: appt.npid,
            name: `${appt.given_name} ${appt.family_name}`.trim() || 'Unknown',
            gender: appt.gender,
            ageDob: HisDate.getBirthdateAge(appt.birthdate),
            village: appt.city_village,
            appointmentDate: HisDate.toStandardHisDisplayFormat(appt.value_datetime)
        }));
    }
};