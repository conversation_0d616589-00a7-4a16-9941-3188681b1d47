import { getOfflineRecords, saveDataOfflineAPI } from "@/services/offline_service";
import { Service } from "@/services/service";
import HisDate from "@/utils/Date";
import { toastDanger, toastSuccess, toastWarning } from "@/utils/Alerts";
import { usePatientList } from "../stores/patientListStore";
import dates from "@/utils/Date";
import { StagesService } from "@/apps/OPD/services/stages_service";
import { VisitRecord } from "./stagesTypes";
import { useWorkerStore } from "@/stores/workerStore";

export const getTodaysVisits = async () => {
    return await getVisits(
        {
            program_id: Service.getProgramID(),
            date: HisDate.toStandardHisFormat(HisDate.sessionDate()),
            status: "active",
        },
        "todayVisits"
    );
};
export const getActiveVisit = async (identifier: any) => {
    return await getVisits(
        {
            program_id: Service.getProgramID(),
            status: "active",
            identifier,
        },
        "visits"
    );
};
export const getOfflineTodayVisit = async (params: any) => {
    const locationId = Service.getUserLocationId();
    if (!locationId) return;
    const visits = ((await getOfflineRecords("visits")) as any) || [];
    const unsaved = ((await getOfflineRecords("unsavedVisits")) as any) || [];
    const pending = unsaved.filter((v: any) => v.sync_status === "pending");
    const allVisits = [...visits, ...pending];
    return allVisits.filter(
        (v: any) =>
            String(v.location_id) === String(locationId) &&
            HisDate.toStandardHisFormat(v.startDate) == HisDate.toStandardHisFormat(HisDate.sessionDate())
    );
};

const getVisits = async (params: any, name: string) => {
    return await Service.getAPIOfflineJson("/visits", params, name);
};
export const getOfflineVisits = async (params: any) => {
    const saveVisits: any = await getOfflineRecords("visits", { whereClause: params });
    const unSaveVisits: any = await getOfflineRecords("unsavedVisits", { whereClause: params });
    return [...saveVisits, ...unSaveVisits];
};

export const getDailyAppointments = async (date: any, end_date = "", srch_text = "") => {
    const programID = Service.getProgramID();
    return Service.getAPIOfflineJson(
        `/programs/${programID}/booked_appointments`,
        { date, end_date, srch_text, paginate: false },
        "booked_appointments"
    );
};

export async function getOfflineDailyAppointments(params: any) {
    const locationId = localStorage.getItem("locationID");
    const allPatients = ((await getOfflineRecords("patientRecords", { whereClause: { location_id: locationId } })) as any) || [];

    const todaysAppointments: any = [];

    allPatients.forEach((patient: any) => {
        if (patient.appointments && patient.appointments.saved) {
            patient.appointments.saved.forEach((appointment: any) => {
                const appointmentDate = appointment.obs_datetime.substring(0, 10);
                if (HisDate.toStandardHisFormat(appointmentDate) === params.date) {
                    todaysAppointments.push({
                        patientId: patient.ID,
                        appointment: appointment,
                    });
                }
            });
        }
    });

    return todaysAppointments;
}

export const openVisit = async (selectedPatient: any) => {
    const patientID = selectedPatient.ID;
    const activeVisit = await getActiveVisit(patientID);

    if (activeVisit.length > 0) {
        toastDanger("Failed, the patient's visit is already active");
        return;
    }

    await createVisit(patientID);
    await StagesService.addPatientToStage(selectedPatient, "VITALS");
    const locationId: any = Service.getUserLocationId();
    await usePatientList().refresh(locationId);
    toastSuccess("Patient's visit is now active, check on the waiting list of vitals");
    return true;
};

export const closeVisit = async (selectedPatient: any) => {
    try {
        const activeVisit = await getVisits(
            {
                program_id: Service.getProgramID(),
                status: "active",
                identifier: `${selectedPatient.ID}`,
            },
            "visits"
        );

        if (activeVisit.length <= 0) {
            toastDanger("Failed, the patient does not have an active visit ");
            return;
        }
        await updateVisit(activeVisit[0].id, Service.getSessionTimeDate(), selectedPatient.ID);
        const locationId: any = Service.getUserLocationId();
        await usePatientList().refresh(locationId);
        toastSuccess("The patient's visit is now closed");
        return true;
    } catch (e) {
        console.error(e);
        return false;
    }
};
const updateVisit = (visitId: number, closedDateTime: any, identifier: any) => {
    return Service.putAPIOfflineJson(
        `/visits/${visitId}/close`,
        {
            visit: { closedDateTime },
            visitId,
            identifier,
        },
        "visits"
    );
};
export const createVisit = (identifier: string, startDate = Service.getSessionTimeDate()) => {
    const locationId: any = Service.getUserLocationId();
    return Service.postAPIOfflineJson(
        `/visits`,
        {
            identifier,
            startDate,
            programId: Service.getProgramID(),
            location_id: locationId,
        },
        "visits"
    );
};
export const createOfflineVisit = async (params: any) => {
    const timestamp = Service.getSessionTimeDate();
    const visitData = {
        identifier: params.identifier,
        startDate: timestamp,
        closedDateTime: null,
        location_id: Service.getUserLocationId(),
        programId: Service.getProgramID(),
        sync_status: "pending",
        updated_at: timestamp,
    };

    const workerStore = useWorkerStore();

    await workerStore.postData("DELETE_RECORD", {
        storeName: "visits",
        whereClause: { identifier: params.identifier },
    });
    await saveDataOfflineAPI("unsavedVisits", visitData, { identifier: params.identifier });

    return visitData;
};
export const updateOfflineVisit = async (params: any) => {
    try {
        const identifier = params.identifier;
        const visitId = params.visitId;
        const now = Service.getSessionTimeDate();

        // First check unsavedVisits
        let unsavedVisits = (await getOfflineRecords("unsavedVisits", {
            whereClause: { identifier },
        })) as any[];

        const savedVisits = (await getOfflineRecords("visits", {
            whereClause: { id: visitId },
        })) as any[];
        let visitToClose = [...unsavedVisits, ...savedVisits];

        if (visitToClose.length <= 0) {
            console.warn(`No visit found to close for patient ${identifier}`);
            return;
        }

        const updatedVisit = {
            ...visitToClose[0],
            closedDateTime: now,
            updated_at: now,
            sync_status: "pending",
        };

        const workerStore = useWorkerStore();

        await workerStore.postData("DELETE_RECORD", {
            storeName: "visits",
            whereClause: { id: visitId },
        });

        await saveDataOfflineAPI("unsavedVisits", updatedVisit, { identifier: params.identifier });
        console.log(`✅ Visit closed for patient ${identifier}`);
    } catch (error) {
        console.error("[VisitService] Failed to close patient visit:", error);
        throw error;
    }
};
export const switchVisitFunctions = async (name: string, params = {} as Record<string, any>) => {
    switch (name) {
        case "todayVisits":
            return await getOfflineTodayVisit(params);
        case "visits":
            return await getOfflineVisits(params);
        case "booked_appointments":
            return await getOfflineDailyAppointments(params);
    }
};
