// types/stageTypes.ts
export interface PersonInformation {
    given_name?: string;
    middle_name?: string;
    family_name?: string;
    birthdate?: string;
    // Add other person properties as needed
}

export interface PatientRecord {
    patientID: number;
    personInformation?: PersonInformation;
    // Add other patient properties as needed
}

export interface VisitRecord {
    id?: number;
    patientId: number;
    location_id: string;
    startDate: string;
    // Add other visit properties as needed
}

export interface StageRecord {
    id?: number;
    patient_id: number;
    visit_id?: number;
    stage: string;
    arrivalTime: string;
    status: number;
    location_id: string;
    fullName?: string;
    created_at?: string;
    updated_at?: string;
    sync_status?: 'pending' | 'failed' | 'synced';
    error?: string;
}