<template>
  <ion-page>
    <ion-header>
      <div class="header position_content">
        <div style="display: flex; align-items: center" @click="dismiss">
          <ion-icon slot="separator" size="large" :icon="iconsContent.arrowLeftWhite"></ion-icon>
        </div>
        <div style="font-size: 1.2em; font-weight: 700">{{ title }}</div>
        <div style="display: flex; align-items: center" @click="openPopover($event)">
          <ion-icon slot="separator" size="large" :icon="iconsContent.fillerWhite"></ion-icon>
        </div>
      </div>
    </ion-header>
    <ion-content :fullscreen="true" class="ion-padding" style="--background: #fff">
      <!-- Segmented Control -->
      <ion-segment v-model="viewMode" @ionChange="onViewModeChange">
        <ion-segment-button value="today">
          <ion-label>Today's Appointments</ion-label>
        </ion-segment-button>
        <ion-segment-button value="range">
          <ion-label>Date Range</ion-label>
        </ion-segment-button>
      </ion-segment>

      <div v-if="viewMode === 'range'" class="date-filter">
        <ion-row>
          <ion-col>
            <DateInputField
                :inputHeader="'Start Date'"
                :sectionHeaderFontWeight="20"
                :unit="''"
                :icon="calendarOutline"
                :placeholder="'press to select date'"
                :iconRight="''"
                :inputWidth="'100%'"
                :inputValue="startDate"
                :eventType="''"
                :minDate="''"
                :maxDate="endDate"
                :disabled="false"
                @update:rawDateValue="getAppointmentsD1"
            />
          </ion-col>
          <ion-col>
            <DateInputField
                :inputHeader="'End Date'"
                :sectionHeaderFontWeight="20"
                :unit="''"
                :icon="calendarOutline"
                :placeholder="'press to select date'"
                :iconRight="''"
                :inputWidth="'100%'"
                :inputValue="endDate"
                :eventType="''"
                :minDate="startDate"
                :maxDate="''"
                :disabled="false"
                @update:rawDateValue="getAppointmentsD2"
            />
          </ion-col>
        </ion-row>
      </div>

      <!-- Appointments Table -->
      <ion-card class="section" style="margin-bottom: 25px; margin-inline: 0px">
        <ion-card-content>
          <div class="dueCardContent">
            <DataTable
                v-if="appointments.length > 0"
                :data="appointments"
                :columns="tableColumns"
                :options="tableOptions"
                class="display nowrap"
                width="100%"
            />
            <div v-else class="no-appointments">
              No appointments found for the selected {{ viewMode === 'today' ? 'day' : 'date range' }}
            </div>
          </div>
        </ion-card-content>
      </ion-card>
    </ion-content>
  </ion-page>
</template>

<script lang="ts">
import {
  IonPage,
  IonContent,
  IonHeader,
  IonItem,
  IonList,
  modalController,
  IonIcon,
  IonCard,
  IonCardContent,
  IonPopover,
  IonSegment,
  IonSegmentButton,
  IonLabel,
  IonRow,
  IonCol
} from "@ionic/vue";
import { defineComponent } from "vue";
import DataTable from "datatables.net-vue3";
import DataTablesCore from "datatables.net";
import "datatables.net-buttons";
import "datatables.net-buttons/js/buttons.html5";
import "datatables.net-responsive";
import "datatables.net-buttons-dt";
import { icons } from "@/utils/svg";
import { useDemographicsStore } from "@/stores/DemographicStore";
import DateInputField from "@/components/DateInputField.vue";
import { calendarOutline, openOutline } from "ionicons/icons";
import HisDate from "@/utils/Date";
import {getOfflineRecords} from "@/services/offline_service";
import {toastDanger} from "@/utils/Alerts";

export default defineComponent({
  name: "OPDAppointmentsModal",
  components: {
    IonPage,
    IonHeader,
    IonContent,
    IonItem,
    IonList,
    IonIcon,
    IonCard,
    IonCardContent,
    IonPopover,
    DataTable,
    IonSegment,
    IonSegmentButton,
    IonLabel,
    IonRow,
    IonCol,
    DateInputField
  },
  props: {
    title: {
      type: String,
      default: "OPD Appointments",
    },
    programType: {
      type: String,
      required: true
    },
    programId: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      isLoading: false,
      popoverOpen: false,
      event: null as any,
      iconsContent: icons,
      appointments: [] as any[],
      viewMode: 'today',
      startDate: HisDate.sessionDate(),
      endDate: HisDate.sessionDate(),
      calendarOutline,
      openOutline,

      tableColumns: [
        { title: "Patient ID", data: "ID" },
        { title: "Name", data: "name" },
        { title: "Age/DOB", data: "ageDob" },
        { title: "Gender", data: "gender" },
        { title: "Village", data: "village" },
        { title: "Appointment Date", data: "appointmentDate" },
        {
          title: "Actions",
          data: null,
          render: (data: any, type: any, row: any) => {
            const container = document.createElement('div');
            const profileButton = document.createElement('ion-button');
            profileButton.innerText = 'Profile';
            profileButton.size = 'small';
            profileButton.style.color = '#fff';
            profileButton.style.padding = '8px 12px';
            profileButton.style.marginRight = '8px';
            profileButton.addEventListener('click', () => this.openClientProfile(row.ID));
            container.appendChild(profileButton);
            return container;
          },
          orderable: false,
        },
      ],
      tableOptions: {
        responsive: true,
        searching: true,
        ordering: true,
        pageLength: 20,
        lengthChange: false,
        dom: 'Bfrtip',
        order: [[5, 'asc']] as Array<[number, 'asc' | 'desc']>,
      },
    };
  },
  async mounted() {
    await this.loadAppointments();
  },
  methods: {
    async loadAppointments() {
      this.isLoading = true;
      try {
        const today = HisDate.sessionDate();
        const startDate = this.viewMode === 'today' ? today : this.startDate;
        const endDate = this.viewMode === 'today' ? today : this.endDate;
        const currentLocationId = localStorage.getItem("locationID");

        // Get all patient records with their appointments
        const allPatients = await getOfflineRecords("patientRecords") as any[];
        const formattedAppointments: any[] = [];

        for (const patient of allPatients) {
          // Get appointments for both saved and unsaved
          const appointments = [
            ...(patient.appointments?.saved || []),
            ...(patient.appointments?.unsaved || [])
          ];

          // Filter appointments
          const filteredAppointments = appointments.filter((appt: any) => {
            if (appt.concept_id !== 5096) return false;
            if (appt.program_id !== 14) return false;
            if (appt.location_id !== currentLocationId) return false;

            const apptDate = appt.value_datetime || appt.obs_datetime;
            if (!apptDate) return false;

            // Convert dates to comparable format
            const apptDateStr = HisDate.toStandardHisFormat(apptDate);
            const startDateStr = HisDate.toStandardHisFormat(startDate);
            const endDateStr = HisDate.toStandardHisFormat(endDate);
            return apptDateStr >= startDateStr && apptDateStr <= endDateStr;
          });

          // Format each appointment for display
          filteredAppointments.forEach((appt: any) => {
            formattedAppointments.push({
              ID: patient.ID,
              name: [patient.personInformation?.given_name,
                patient.personInformation?.family_name]
                  .filter(Boolean).join(' ') || 'Unknown',
              ageDob: patient.personInformation?.birthdate
                  ? HisDate.getBirthdateAge(patient.personInformation.birthdate)
                  : 'N/A',
              gender: patient.personInformation?.gender || 'Unknown',
              village: patient.personInformation?.home_village || 'Unknown',
              appointmentDate: HisDate.toStandardHisDisplayFormat(
                  appt.value_datetime || appt.obs_datetime
              ),
              rawData: appt
            });
          });
        }

        // Sort by appointment date
        this.appointments = formattedAppointments.sort((a, b) => {
          const dateA = a.rawData.value_datetime || a.rawData.obs_datetime;
          const dateB = b.rawData.value_datetime || b.rawData.obs_datetime;
          return new Date(dateA).getTime() - new Date(dateB).getTime();
        });

      } catch (error) {
        console.error("Error loading appointments:", error);
        toastDanger("Failed to load appointments");
      } finally {
        this.isLoading = false;
      }
    },
    async getAppointmentsD1(date: any) {
      this.startDate = HisDate.toStandardHisFormat(date);
      await this.loadAppointments();
    },

    async getAppointmentsD2(date: any) {
      this.endDate = HisDate.toStandardHisFormat(date);
      await this.loadAppointments();
    },

    onViewModeChange() {
      if (this.viewMode === 'today') {
        this.startDate = HisDate.sessionDate();
        this.endDate = HisDate.sessionDate();
      }
      this.loadAppointments();
    },

    async openClientProfile(id: string) {
      try {
        const patientRecords = await getOfflineRecords("patientRecords", {
          whereClause: {ID: id}
        }) as any[];
        await useDemographicsStore().setPatientRecord(patientRecords[0]);
        this.$router.push("patientProfile");
      } catch (error) {
        console.error("Error opening profile:", error);
        toastDanger("Failed to open patient profile");
      }
    },
    openPopover(e: Event) {
      this.event = e;
      this.popoverOpen = true;
    },

    dismiss() {
      modalController.dismiss();
    }
  },
});
</script>

<style scoped>
.header {
  color: #fff;
  display: flex;
  justify-content: space-between;
  padding: 10px 20px 10px 20px;
}

ion-header {
  background: #006401;
}

.position_content {
  max-width: 100vw;
}

.section {
  margin-bottom: 25px;
  margin-inline: 0px;
}

.dueCardContent {
  width: 100%;
}

.date-filter {
  margin: 16px 0;
}

.no-appointments {
  text-align: center;
  padding: 20px;
  color: #666;
}

ion-segment {
  margin: 16px 0;
}

ion-button {
  --background: #006401;
  --background-activated: #004d00;
  --background-focused: #004d00;
  --background-hover: #004d00;
}

@media (max-width: 768px) {
  .date-filter ion-col {
    width: 100%;
  }
}
</style>