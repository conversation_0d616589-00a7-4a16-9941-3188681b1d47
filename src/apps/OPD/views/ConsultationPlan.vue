<template>
    <ion-page>
        <OPDPrintingModal :onYes="printYes" :onNo="printNo" :isOpen="printModalOpen" :title="`Do you want to print the consultation summary?`" />
        <!-- Spinner -->
        <div v-if="isLoading" class="spinner-overlay">
            <ion-spinner name="bubbles"></ion-spinner>
            <div class="loading-text">Please wait...</div>
        </div>
        <ion-loading :is-open="isLoadingPrinter" message="Printing consultation summary..." spinner="circles"></ion-loading>
        <Toolbar />
        <ion-content :fullscreen="true">
            <DemographicBar />
            <div style="width: 88vw; margin: 0 auto; margin-top: 10px">
                <Wizard
                    v-if="showWizard"
                    ref="wizard"
                    vertical-tabs
                    :navigable-tabs="navigable"
                    scrollable-tabs
                    :startIndex="0"
                    :doneButton="{
                        text: 'Finish',
                        icon: 'check',
                        hideText: false,
                        hideIcon: false,
                        disabled: false,
                    }"
                    :nextButton="{
                        disabled: nextButtonDisabled,
                    }"
                    :custom-tabs="tabs"
                    :tabs="tabs"
                    @change="onChangeCurrentTab"
                    @complete:wizard="saveData()"
                >
                    <div>
                        <div class="back_profile">
                            <DynamicButton
                                name="Back to Waiting list"
                                iconSlot="start"
                                fill="clear"
                                :icon="chevronBackOutline"
                                :font-weight="'600'"
                                @click="openBackController()"
                            />
                        </div>
                    </div>

                    <!-- Render components with refs for accessing their methods -->
                    <div v-show="getActiveComponent() === 'ClinicalAssessment'">
                        <ClinicalAssessment ref="clinicalAssessmentRef" />
                    </div>
                    <div v-show="getActiveComponent() === 'Investigations'">
                        <Investigations ref="investigationsRef" />
                    </div>
                    <div v-show="getActiveComponent() === 'OPDDiagnosis'">
                        <OPDDiagnosis ref="diagnosisRef" />
                    </div>
                    <div v-show="getActiveComponent() === 'OPDTreatmentPlan'">
                        <OPDTreatmentPlan ref="treatmentPlanRef" />
                    </div>
                    <div v-show="getActiveComponent() === 'NextAppointment'">
                        <NextAppointment ref="nextAppointmentRef" />
                    </div>
                    <div v-show="getActiveComponent() === 'Outcome'">
                        <OPDOutcome ref="outcomeRef" />
                    </div>
                </Wizard>
            </div>
        </ion-content>
    </ion-page>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
import { IonContent, IonPage, IonLoading, IonSpinner } from "@ionic/vue";
import { chevronBackOutline } from "ionicons/icons";
import { storeToRefs } from "pinia";
import Wizard from "@/components/WizardStep/Wizard.vue";

// Import components
import Toolbar from "@/components/Toolbar.vue";
import DemographicBar from "@/components/DemographicBar.vue";
import DynamicButton from "@/components/DynamicButton.vue";
import ClinicalAssessment from "@/apps/OPD/components/ConsultationPlan/ClinicalAssessment.vue";
import Investigations from "@/components/Investigations.vue";
import OPDDiagnosis from "@/apps/OPD/components/ConsultationPlan/OPDDiagnosis.vue";
import OPDTreatmentPlan from "@/apps/OPD/components/ConsultationPlan/OPDTreatmentPlan.vue";
import NextAppointment from "@/apps/NCD/components/ConsultationPlan/NextAppointment.vue";
import OPDOutcome from "@/apps/OPD/components/ConsultationPlan/OPDOutcome.vue";
import OPDPrintingModal from "@/apps/OPD/components/ConsultationPlan/Modals/OPDPrintingModal.vue";

// Import stores
import { useDemographicsStore } from "@/stores/DemographicStore";
import { useInvestigationStore } from "@/stores/InvestigationStore";
import { useOPDDiagnosisStore } from "@/apps/OPD/stores/DiagnosisStore";
import { useTreatmentPlanStore } from "@/stores/TreatmentPlanStore";
import { useGeneralStore } from "@/stores/GeneralStore";
import { useOutcomeStore } from "@/stores/OutcomeStore";
import { usePatientList } from "@/apps/OPD/stores/patientListStore";
import { useUserStore } from "@/stores/userStore";
import { useNonPharmaTherapyStore, stageNotes } from "@/stores/nonPharmaTherapyStore";
import { useVitalsStore } from "@/stores/VitalsStore";
import { useNextAppointmentStore } from "@/stores/NextAppointmentStore";
import { usePresentingComplaintsStore } from "@/apps/OPD/stores/PresentingComplaintsStore";

// Import services and utilities
import { Service } from "@/services/service";
import { toastWarning, toastSuccess, toastDanger } from "@/utils/Alerts";
import { Treatment, stageAllergies } from "@/apps/NCD/services/treatment";
import { isEmpty } from "lodash";
import HisDate from "@/utils/Date";
import { resetOPDPatientData } from "@/apps/OPD/config/reset_opd_data";
import { createOPDDrugOrder } from "@/apps/NCD/services/medication_service";
import { usePatientProfile } from "@/composables/usePatientProfile";
import { useFormWizard } from "@/composables/useFormWizard";
import { savePatientRecord, getOfflineSavedUnsavedData } from "@/services/offline_service";
import { icons } from "@/utils/svg";
import { lockClosedOutline, checkmarkOutline } from "ionicons/icons";
import { EncounterTypeId } from "@/services/encounter_type";
import { ObservationService } from "@/services/observation_service";

// Composable
const { onTabBeforeChange, currentTabIndex } = useFormWizard("Consultation Plan");
const { printVisitSummary } = usePatientProfile();

// Router
const router = useRouter();
const route = useRoute();

// State
const isLoading = ref(false);
const navigable = ref(true);
const nextButtonDisabled = ref(false);
const isLoadingPrinter = ref(false);
const showWizard = ref(true);
const printModalOpen = ref(false);
const showAlert = ref(false);
const hasPatientsWaitingForLab = ref(false);

// Store references
const demographicsStore = useDemographicsStore();
const investigationStore = useInvestigationStore();
const diagnosisStore = useOPDDiagnosisStore();
const treatmentPlanStore = useTreatmentPlanStore();
const generalStore = useGeneralStore();
const outcomeStore = useOutcomeStore();
const userStore = useUserStore();
const vitalsStore = useVitalsStore();
const nextAppointmentStore = useNextAppointmentStore();
const presentingComplaintsStore = usePresentingComplaintsStore();

// Destructure store refs
const { patient } = storeToRefs(demographicsStore);
const { investigations } = storeToRefs(investigationStore);
const { OPDdiagnosis } = storeToRefs(diagnosisStore);
const { selectedMedicalDrugsList, nonPharmalogicalTherapyAndOtherNotes } = storeToRefs(treatmentPlanStore);
const { OPDActivities } = storeToRefs(generalStore);
const { outcomes } = storeToRefs(outcomeStore);
const { currentSelectedNextAppointmentDate } = storeToRefs(nextAppointmentStore);
const { presentingComplaints } = storeToRefs(presentingComplaintsStore);
const { vitals } = storeToRefs(vitalsStore);

import { useAllegyStore } from "@/apps/OPD/stores/AllergyStore";
import { StagesService } from "../services/stages_service";
const allegyStore = useAllegyStore();
const selectedAllergiesList2 = computed(() => allegyStore.selectedMedicalAllergiesList);

// Component refs
const clinicalAssessmentRef = ref<InstanceType<typeof ClinicalAssessment> | null>(null);
const investigationsRef = ref<InstanceType<typeof Investigations> | null>(null);
const diagnosisRef = ref<InstanceType<typeof OPDDiagnosis> | null>(null);
const treatmentPlanRef = ref<InstanceType<typeof OPDTreatmentPlan> | null>(null);
const nextAppointmentRef = ref<InstanceType<typeof NextAppointment> | null>(null);
const outcomeRef = ref<InstanceType<typeof OPDOutcome> | null>(null);
const wizard = ref<InstanceType<typeof Wizard> | null>(null);

// Get only the active tabs directly from OPDActivities
const getActiveTabs = () => {
    return OPDActivities.value.map((item: any) => {
        return { title: item, icon: "" };
    });
};

// This will only contain active tabs
const tabs = ref();
const data = ref({});

const onChangeCurrentTab = async (index: number, oldIndex: number, runChangeTab?: boolean) => {
    if (oldIndex === 0) await saveClinicalAssessment();
    if (!(await isPresentingComplaintsDone())) {
        toastWarning("Please add presenting complaints before proceeding.");
        return;
    }
    await markWizard();
    if (index % 1 === 0) currentTabIndex.value = index;
    if (wizard.value && runChangeTab) {
        wizard.value.changeTab(index);
    }
};
const getActiveComponent = () => {
    if (!tabs.value || tabs.value.length === 0) {
        return null;
    }

    const index = currentTabIndex.value >= 0 && currentTabIndex.value < tabs.value.length ? currentTabIndex.value : 0;
    const currentTab = tabs.value[index]?.title;

    switch (currentTab) {
        case "Clinical Assessment":
            return "ClinicalAssessment";
        case "Investigations":
            return "Investigations";
        case "Diagnosis":
            return "OPDDiagnosis";
        case "Treatment Plan":
            return "OPDTreatmentPlan";
        case "Next Appointment":
            return "NextAppointment";
        case "Outcome":
            return "Outcome";
        default:
            if (OPDActivities.value.length > 0) {
                const firstActivity = OPDActivities.value[0];
                switch (firstActivity) {
                    case "Clinical Assessment":
                        return "ClinicalAssessment";
                    case "Investigations":
                        return "Investigations";
                    case "Diagnosis":
                        return "OPDDiagnosis";
                    case "Treatment Plan":
                        return "OPDTreatmentPlan";
                    case "Next Appointment":
                        return "NextAppointment";
                    case "Outcome":
                        return "Outcome";
                }
            }
            return null;
    }
};

const openBackController = () => {
    router.push("home");
};

const printYes = async () => {
    isLoadingPrinter.value = true;
    toastSuccess("Printing consultation summary... Please wait.");
    try {
        await printVisitSummary();
        toastSuccess("Consultation summary printed successfully!");
        setTimeout(() => {
            router.push("home");
        }, 3500);
    } catch (error) {
        toastDanger("Failed to print consultation summary.");
    } finally {
        isLoadingPrinter.value = false;
    }
};

const printNo = () => {
    toastSuccess("Patient has finished consultation!");
    router.push("home");
};

const isPresentingComplaintsDone = async () => {
    const latestObs = await ObservationService.getObsByEncounterIdAndDate(EncounterTypeId.PRESENTING_COMPLAINTS);
    if (latestObs && latestObs.length > 0) return true;
    else return false;
};
const markWizard = async () => {
    const sessionDate = HisDate.sessionDate();

    // Process only active tabs
    for (let i = 0; i < tabs.value.length; i++) {
        if (!(await isPresentingComplaintsDone())) {
            navigable.value = false;
            // nextButtonDisabled.value = true;
            if (tabs.value[i].title !== "Clinical Assessment") tabs.value[i].icon = lockClosedOutline;
        } else {
            navigable.value = true;
            nextButtonDisabled.value = false;
            const tab = tabs.value[i];
            data.value = { title: "Untitled" };
            if (tab.title === "Clinical Assessment") {
                const complaintsData = getOfflineSavedUnsavedData("presentingComplaints");
                tabs.value[i].icon = isDateInArray(sessionDate, complaintsData) ? checkmarkOutline : "";
            } else if (tab.title === "Investigations") {
                const labOrders = patient.value?.labOrders?.saved;
                const filteredArray = labOrders?.filter((obj: any) => {
                    return HisDate.toStandardHisFormat(sessionDate) === HisDate.toStandardHisFormat(obj.order_date);
                });
                tabs.value[i].icon = filteredArray?.length > 0 ? checkmarkOutline : "";
            } else if (tab.title === "Diagnosis") {
                const diagnosisData = getOfflineSavedUnsavedData("diagnosis");
                tabs.value[i].icon = isDateInArray(sessionDate, diagnosisData) ? checkmarkOutline : "";
            } else if (tab.title === "Treatment Plan") {
                const treatmentData = getOfflineSavedUnsavedData("treatment");
                tabs.value[i].icon = treatmentData?.length > 0 ? checkmarkOutline : "";
            } else if (tab.title === "Next Appointment") {
                const appointmentData = getOfflineSavedUnsavedData("appointments");
                tabs.value[i].icon = isDateInArray(sessionDate, appointmentData) ? checkmarkOutline : "";
            } else if (tab.title === "Outcome") {
                const outcomeData = getOfflineSavedUnsavedData("outcomes");
                tabs.value[i].icon = outcomeData?.length > 0 ? checkmarkOutline : "";
            }
        }
    }
};

const isDateInArray = (dateToCheck: any, dataArray: any) => {
    if (!dataArray) return false;

    // Convert input date to start of day for comparison
    const checkDate = new Date(dateToCheck);
    checkDate.setHours(0, 0, 0, 0);

    return dataArray.some((item: any) => {
        // Convert each obs_datetime to start of day
        const obsDate = new Date(item.obs_datetime || item.order_date);
        obsDate.setHours(0, 0, 0, 0);

        return obsDate.getTime() === checkDate.getTime();
    });
};

const saveClinicalAssessment = async () => {
    isLoading.value = true;
    try {
        await saveAllergies();
        await Promise.all([clinicalAssessmentRef.value?.onSubmit()]);
        await savePatientRecord(patient.value);
        resetOPDPatientData();
    } catch (error) {
        console.error("Error saving clinical assessment:", error);
        toastDanger("Failed to save clinical assessment");
        throw error;
    } finally {
        isLoading.value = false;
    }
};

const saveAllergies = async () => {
    try {
        if (!isEmpty(selectedAllergiesList2.value)) {
            const allergies = mapToAllergies();
            const patientData = await stageAllergies(allergies);
            demographicsStore.setPatientRecord(patientData);
        }
    } catch (error) {
        console.error("Failed to save allergies:", error);
        toastWarning("Failed to save allergies");
    }
};

const saveTreatmentPlan = async () => {
    try {
        const m_patientData = await createOPDDrugOrder();
        patient.value = Object.assign(patient.value, m_patientData);
        const patientData = await useNonPharmaTherapyStore().saveNonPharmaTherapyPatientData();
        patient.value = Object.assign(patient.value, patientData);
    } catch (error) {
        console.error("Failed to save treatment plan:", error);
        toastWarning("Failed to save treatment plan");
    }
};

const mapToAllergies = (): any[] => {
    return selectedAllergiesList2.value.map((allergy: any) => {
        return {
            concept_id: 985,
            obs_datetime: Service.getSessionDate(),
            // value_coded: allergy.concept_id,
            location_id: userStore.facilityLocation.code,
            value_text: allergy.name,
        };
    });
};

const saveData = async () => {
    try {
        diagnosisRef.value?.onSubmit(), await saveTreatmentPlan();
        await outcomeStore.saveOutcomPatientData();
        await resetOPDPatientData();
        await savePatientRecord(patient.value);

        const locationId = localStorage.getItem("locationID");
        const userRolesString = localStorage.getItem("userRoles");

        if (!locationId) {
            toastDanger("Location ID could not be found. Please check your settings.");
            return;
        }

        const userRoles = userRolesString ? JSON.parse(userRolesString) : [];
        const hasLabRole = userRoles.some((role: { role: string }) => role.role === "Lab");

        if (!hasLabRole) {
            await StagesService.addPatientToStage(patient.value, "DISPENSATION");
            await usePatientList().refresh(locationId);
            await router.push("home");
            toastSuccess("Consultation patient saved successfully.");
        } else {
            await StagesService.addPatientToStage(patient.value, "CONSULTATION");
            await usePatientList().refresh(locationId);
            await router.push("home");
            toastSuccess("Lab results submitted. Patient can return to consultation");
        }
    } catch (error) {
        console.error("Error saving consultation data:", error);
        toastDanger("Failed to save consultation data");
    }
};

// Lifecycle hooks and watchers
onMounted(async () => {
    if (OPDActivities.value.length === 0) {
        await router.push("home");
        return;
    }

    tabs.value = getActiveTabs();
    await markWizard();

    if (currentTabIndex.value === undefined || currentTabIndex.value < 0) {
        currentTabIndex.value = 0;
    }
});

watch(
    vitals,
    async () => {
        await markWizard();
    },
    { deep: true }
);

watch(
    patient,
    async () => {
        await markWizard();
    },
    { deep: true }
);

watch(
    investigations,
    async () => {
        await markWizard();
    },
    { deep: true }
);

watch(
    OPDdiagnosis,
    async () => {
        await markWizard();
    },
    { deep: true }
);

watch(
    selectedMedicalDrugsList,
    async () => {
        await markWizard();
    },
    { deep: true }
);

watch(
    outcomes,
    async () => {
        await markWizard();
    },
    { deep: true }
);

watch(currentSelectedNextAppointmentDate, async () => {
    await markWizard();
});

watch(
    presentingComplaints,
    async () => {
        await markWizard();
    },
    { deep: true }
);

watch(
    route,
    async () => {
        showWizard.value = false;
        tabs.value = getActiveTabs();
        setTimeout(() => {
            currentTabIndex.value = 0;
            showWizard.value = true;
        }, 0);
    },
    { deep: true }
);

watch(hasPatientsWaitingForLab, (newValue) => {
    showAlert.value = newValue;
    if (showAlert.value) {
        setTimeout(() => {
            showAlert.value = false;
        }, 15000);
    }
});
</script>

<style scoped>
.disabled {
    color: red;
}
.spinner-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.5);
    z-index: 9999;
}

ion-spinner {
    width: 80px;
    height: 80px;
}

.loading-text {
    margin-top: 20px;
    font-size: 18px;
    color: #333;
}

.pause-alert {
    background-color: #f8d7da;
    color: #721c24;
    padding: 10px;
    border: 1px solid #f5c6cb;
    border-radius: 5px;
    margin-bottom: 1px;
    text-align: center;
}

.loading {
    pointer-events: none;
}

.back_profile {
    margin-bottom: 10px;
}
</style>
