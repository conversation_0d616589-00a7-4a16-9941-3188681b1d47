<template>
    <ion-page>
        <!-- Spinner -->
        <div v-if="isLoading" class="spinner-overlay">
            <ion-spinner name="bubbles"></ion-spinner>
            <div class="loading-text">Please wait...</div>
        </div>
        <Toolbar />
        <ion-content :fullscreen="true">
            <DemographicBar />
            <div class="container">
                <!-- Header Section -->
                <div class="vitals-header">
                    <div class="back_profile">
                        <DynamicButton
                            name="Back to Waiting list"
                            iconSlot="start"
                            fill="clear"
                            :icon="chevronBackOutline"
                            :font-weight="'600'"
                            @click="openBackController()"
                        />
                        <DynamicButton
                            name="Back to Profile"
                            iconSlot="start"
                            fill="clear"
                            :icon="chevronBackOutline"
                            :font-weight="'600'"
                            @click="openProfileController()"
                            style="margin-left: 15px"
                        />
                    </div>

                    <div class="vitals-title">
                        <h4>Vital Signs</h4>
                        <hr class="title-line" />
                    </div>
                </div>

                <div class="vitals-content">
                    <Vitals ref="vitalsRef" />
                </div>

                <div class="action-buttons">
                    <DynamicButton
                        name="Save Vitals"
                        iconSlot="start"
                        fill="solid"
                        color="primary"
                        :icon="checkmarkOutline"
                        @click="saveVitals()"
                        :disabled="isLoading"
                    />
                </div>
            </div>
        </ion-content>
    </ion-page>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useRouter, useRoute } from "vue-router";
import { IonContent, IonPage, IonSpinner } from "@ionic/vue";
import { chevronBackOutline, checkmarkOutline } from "ionicons/icons";
import { storeToRefs } from "pinia";

// Import components
import Toolbar from "@/components/Toolbar.vue";
import DemographicBar from "@/components/DemographicBar.vue";
import DynamicButton from "@/components/DynamicButton.vue";
import Vitals from "@/apps/NCD/components/ConsultationPlan/Vitals.vue";

// Import stores
import { useDemographicsStore } from "@/stores/DemographicStore";
import { useVitalsStore } from "@/stores/VitalsStore";

// Import services and utilities
import { toastDanger, toastSuccess } from "@/utils/Alerts";
import { savePatientRecord } from "@/services/offline_service";
import { UserService } from "@/services/user_service";
import { usePatientList } from "@/apps/OPD/stores/patientListStore";
import { StagesService } from "../services/stages_service";

// Router
const router = useRouter();
const route = useRoute();

// State
const isLoading = ref(false);

// Store references
const demographicsStore = useDemographicsStore();
const vitalsStore = useVitalsStore();

// Destructure store refs
const { patient } = storeToRefs(demographicsStore);
const { vitals } = storeToRefs(vitalsStore);

// Component refs
const vitalsRef = ref<InstanceType<typeof Vitals> | null>(null);

const openBackController = () => {
    router.push("home");
};

const openProfileController = () => {
    router.push("patientProfile");
};

const saveVitals = async () => {
    isLoading.value = true;
    const locationId = String(localStorage.getItem("locationID"));
    try {
        if (vitalsRef.value) {
            // Call the child's onSubmit and get its boolean result
            const vitalsSavedSuccessfully = await vitalsRef.value.onSubmit();

            if (vitalsSavedSuccessfully) {
                await StagesService.addPatientToStage(patient.value, "CONSULTATION");
                await usePatientList().refresh(locationId);

                if (UserService.isClinician() || UserService.isAdmin()) {
                    await router.push("OPDConsultationPlan");
                } else {
                    await router.push("patientProfile");
                }
            }
        }
    } catch (error) {
        console.error("An unexpected error occurred in saveVitals (parent):", error);
        toastDanger("An unexpected error occurred. Please try again.");
    } finally {
        isLoading.value = false;
    }
};
</script>

<style scoped>
.spinner-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 9999;
}

ion-spinner {
    width: 80px;
    height: 80px;
}

.loading-text {
    margin-top: 20px;
    font-size: 18px;
    color: #333;
}

.vitals-header {
    margin-bottom: 20px;
}

.back_profile {
    margin-bottom: 15px;
    align-items: center;
}

.vitals-title {
    text-align: left;
    margin-bottom: 20px;
}

.vitals-title h4 {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin: 0 0 10px 0;
}

.title-line {
    border: none;
    height: 2px;
    background-color: #e0e0e0;
    margin: 0;
}

.vitals-content {
    margin-bottom: 30px;
}

.action-buttons {
    display: flex;
    justify-content: flex-end;
    padding: 20px 0;
    border-top: 1px solid #e0e0e0;
    margin-top: 20px;
}

.container {
    width: 95vw;
    margin: 0 auto;
    margin-top: 10px;
    padding: 0 10px;
}

@media (min-width: 768px) {
    .container {
        width: 90vw;
        padding: 0 20px;
    }
}

@media (min-width: 1024px) {
    .container {
        max-width: 1200px;
        padding: 0 60px;
    }
}

@media (min-width: 1440px) {
    .container {
        padding: 0 100px;
    }
}
</style>
