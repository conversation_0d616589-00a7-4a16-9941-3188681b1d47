import { FormElement } from "@/components/Forms/interfaces/FormElement";
import { computed } from "vue";

export const adultLevelOfConsciousnessForm = computed(() => {
    return [
        {
            componentType: "radioButtonField",
            header: "Eye opening response",
            name: "Eye opening response",
            obsValueType: "value_coded",
            type: "inline",
            options: [
                {
                    label: "Spontaneously",
                    value: "Spontaneously",
                },
                {
                    label: "To speech",
                    value: "To speech",
                },
                {
                    label: "To pain",
                    value: "To pain",
                },
                {
                    label: "No response",
                    value: "No response",
                },
            ],
        },

        { componentType: "Dashes" },
        {
            componentType: "radioButtonField",
            header: "Best verbal response",
            name: "Best verbal response",
            obsValueType: "value_coded",
            type: "inline",
            options: [
                {
                    label: "Oriented to time, place and person",
                    value: "Oriented to time, place and person",
                },
                {
                    label: "Confused",
                    value: "Confused",
                },
                {
                    label: "Inappropriate words",
                    value: "Inappropriate words",
                },
                {
                    label: "Incomprehensible sounds",
                    value: "Incomprehensible sounds",
                },
                {
                    label: "No response",
                    value: "No response",
                },
            ],
        },
        { componentType: "Dash<PERSON>" },
        {
            componentType: "radioButtonField",
            header: "Best motor response",
            name: "Best motor response",
            obsValueType: "value_coded",
            type: "inline",
            options: [
                {
                    label: "Obeys commands",
                    value: "Obeys commands",
                },
                {
                    label: "Moves to localised pain",
                    value: "Moves to localised pain",
                },
                {
                    label: "Flexion withdrawal from pain",
                    value: "Flexion withdrawal from pain",
                },
                {
                    label: "Abnormal flexion (decorticate)",
                    value: "Abnormal flexion (decorticate)",
                },
                {
                    label: "Abnormal extension (decerebrate)",
                    value: "Abnormal extension (decerebrate)",
                },
                {
                    label: "No response",
                    value: "No response",
                },
            ],
        },
    ] satisfies FormElement[];
});
export const minorLevelOfConsciousnessForm = computed(() => {
    return [
        {
            componentType: "radioButtonField",
            header: "Eye opening response",
            name: "Eye opening response",
            obsValueType: "value_coded",
            type: "inline",
            options: [
                {
                    label: "Directed eye movements",
                    value: "Directed eye movements",
                },
                {
                    label: "Not directed",
                    value: "Not directed",
                },
            ],
        },

        { componentType: "Dashes" },
        {
            componentType: "radioButtonField",
            header: "Best verbal response",
            name: "Best verbal response",
            obsValueType: "value_coded",
            type: "inline",
            options: [
                {
                    label: "Appropriate cry",
                    value: "Appropriate cry",
                },
                {
                    label: "Inappropriate cry or moan",
                    value: "Inappropriate cry or moan",
                },
                {
                    label: "No cry",
                    value: "No cry",
                },
            ],
        },
        { componentType: "Dashes" },
        {
            componentType: "radioButtonField",
            header: "Best motor response",
            name: "Best motor response",
            obsValueType: "value_coded",
            type: "inline",
            options: [
                {
                    label: "Localizes pain",
                    value: "Localizes pain",
                },
                {
                    label: "Withdraws from pain",
                    value: "Withdraws from pain",
                },
                {
                    label: "Non specific or no response",
                    value: "Non specific or no response",
                },
            ],
        },
    ] satisfies FormElement[];
});
export const eyeOpeningWeights: { [key: string]: number } = {
    Spontaneously: 4,
    "To speech": 3,
    "To pain": 2,
    "No response": 1,
};

export const verbalResponseWeights: { [key: string]: number } = {
    "Oriented to time, place and person": 5,
    Confused: 4,
    "Inappropriate words": 3,
    "Incomprehensible sounds": 2,
    "No response": 1,
};

export const motorResponseWeights: { [key: string]: number } = {
    "Obeys commands": 6,
    "Moves to localised pain": 5,
    "Flexion withdrawal from pain": 4,
    "Abnormal flexion (decorticate)": 3,
    "Abnormal extension (decerebrate)": 2,
    "No response": 1,
};

export const eyeOpeningMinorWeights: { [key: string]: number } = {
    "Not directed": 0,
    "Directed eye movements": 1,
};
export const motorResponseMinorWeights: { [key: string]: number } = {
    "Non specific or no response": 0,
    "Withdraws from pain": 1,
    "Localizes pain": 2,
};
export const verbalResponseMinorWeights: { [key: string]: number } = {
    "No cry": 0,
    "Inappropriate cry or moan": 1,
    "Appropriate cry": 2,
};
