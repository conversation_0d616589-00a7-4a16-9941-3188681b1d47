import { defineStore } from "pinia";
import { getOfflineRecords } from "@/services/offline_service";

async function getPrograms() {
    const data = await getOfflineRecords("activeProgramInContext");
    return data;
}

export const useStatusStore = defineStore("statusStore", {
    state: () => {
        return {
            apiStatus: true,
            offlineVillageStatus: {} as any,
            offlineDistrictStatus: {} as any,
            offlineCountriesStatus: {} as any,
            offlineTAsStatus: {} as any,
            offlineRelationshipStatus: {} as any,
            offlinePatientsStatus: {} as any,
            offlineDrugsStatus: {} as any,
            syncingTotal: 0 as any,
            syncingCountPercentage: 0 as any,
            currentPrograms: [] as any,
        };
    },
    actions: {
        setApiStatus(data: any) {
            this.apiStatus = data;
        },
        setOfflineDistrictStatus(data: any) {
            this.offlineDistrictStatus = data;
        },
        setOfflineCountriesStatus(data: any) {
            this.offlineCountriesStatus = data;
        },
        setOfflineVillageStatus(data: any) {
            this.offlineVillageStatus = data;
        },
        setOfflineTAsStatus(data: any) {
            this.offlineTAsStatus = data;
        },
        setOfflineRelationshipStatus(data: any) {
            this.offlineRelationshipStatus = data;
        },
        setOfflinePatientsStatus(data: any) {
            this.offlinePatientsStatus = data;
        },
        setOfflineDrugsStatus(data: any) {
            this.offlineDrugsStatus = data;
        },
        getSyncingCountPercentage() {
            // Base totals that are always included
            let denominator =
                (this.offlineVillageStatus.total || 0) +
                (this.offlineRelationshipStatus.total || 0) +
                (this.offlineCountriesStatus.total || 0) +
                (this.offlineDistrictStatus.total || 0) +
                (this.offlineTAsStatus.total || 0) +
                (this.offlineDrugsStatus.total || 0);

            let numerator =
                (this.offlineVillageStatus.total_village || 0) +
                (this.offlineRelationshipStatus.total_relationships || 0) +
                (this.offlineCountriesStatus.total_countries || 0) +
                (this.offlineDistrictStatus.total_districts || 0) +
                (this.offlineTAsStatus.total_TAs || 0) +
                (this.offlineDrugsStatus.total_OPD_drugs || 0);

            denominator += this.offlinePatientsStatus.serverPatientsCount || 0;
            numerator += this.offlinePatientsStatus.offlinePatientsCount || 0;

            if (denominator === 0) {
                this.syncingCountPercentage = 0;
            } else {
                let percentage = (numerator / denominator) * 100;
                this.syncingCountPercentage = `${percentage ? percentage.toFixed(0) : 0}`;
            }
        },
        async setSyncingTotal() {
            // Fetch all offline records in parallel for better performance
            if (this.apiStatus) {
                // Define which resources to fetch
                const resourcesPromises = [
                    getOfflineRecords("villages"),
                    getOfflineRecords("relationship"),
                    getOfflineRecords("countries"),
                    getOfflineRecords("districts"),
                    getOfflineRecords("TAs"),
                    getOfflineRecords("drugs"),
                ];

                // Add patient records fetch only if needed
                let patientRecordsPromise;
                patientRecordsPromise = getOfflineRecords("patientRecords");
                resourcesPromises.push(patientRecordsPromise);

                // Fetch all resources in parallel
                const results = (await Promise.all(resourcesPromises)) as any;

                // Update statuses for non-patient resources
                this.offlineVillageStatus.total_village = results[0].length;
                this.offlineRelationshipStatus.total_relationships = results[1].length;
                this.offlineCountriesStatus.total_countries = results[2].length;
                this.offlineDistrictStatus.total_districts = results[3].length;
                this.offlineTAsStatus.total_TAs = results[4].length;
                this.offlineDrugsStatus.total_OPD_drugs = results[5].length;

                // Update patient status only if we're processing patients
                const patientRecords = results[6];
                this.offlinePatientsStatus.offlinePatientsCount = patientRecords.length;
            }
        },
    },
    persist: true,
});
