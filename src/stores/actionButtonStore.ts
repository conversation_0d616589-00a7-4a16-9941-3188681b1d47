import { defineStore } from "pinia";

export const useActionButtonStore = defineStore("actionButtonStore", {
    state: () => ({
        show_action_button: true as boolean
    }),
    actions: {
        setShowActionButton(value: boolean) {
            this.show_action_button = value
        },
        getShowActionButton() {
            return this.show_action_button
        },
        resetShowActionButton() {
            this.show_action_button = true
        }
    }
});