import { defineStore } from "pinia";
import { getOfflineRecords } from "@/services/offline_service";
import { Service } from "@/services/service";
import HisDate from "@/utils/Date";
import { toastDanger } from "@/utils/Alerts";
import { navigationService } from "@/services/navigation";
import { getUseMODS } from "@/services/mods_service";

export const useDemographicsStore = defineStore("demographicStore", {
    state: () => ({
        patient: {} as any,
    }),
    actions: {
        async setRecord(data: any) {
            this.patient = data;
        },
        getPatient() {
            return this.patient;
        },
        async setPatientRecord(item: any) {
            if (item?.ID) {
                await this.setRecord(item);
            } else {
                const patientID = item?.patient_id || item?.patientID;
                const patientRecord: any = await this.getPatientData(patientID);
                await this.setRecord(patientRecord);
            }

            if (this.patient?.personInformation?.birthdate) {
                const BD = this.patient.personInformation.birthdate;
                const SessionDate = HisDate.sessionDate();

                console.log("Session Date:", SessionDate);
                console.log("Birth Date:", BD);

                // Convert to Date objects for proper comparison
                const birthDate = new Date(BD);
                const sessionDate = new Date(SessionDate);

                // Check if birth date is NOT (equal to or greater than) session date
                // This is equivalent to: birth date < session date
                if (!(birthDate >= sessionDate)) {
                    // console.log("Birth date is valid (in the past)");
                } else {
                    const patientNameID = `${this.patient?.personInformation?.given_name} ${this.patient?.personInformation?.family_name} | PID: ${this.patient?.patientID}`;
                    const message = `${patientNameID}, birth date (${BD}) is greater than current Session date (${SessionDate}).`;
                    this.setRecord({});
                    navigationService.reloadApp("/home", message);
                }
            }
        },
        async getPatientIdentifier(identifiers: any, id: any) {
            if (identifiers) {
                return identifiers.patient_identifiers
                    .filter((identifier: any) => identifier.identifier_type === id)
                    .map((identifier: any) => identifier.identifier)
                    .join(", ");
            } else {
                return "";
            }
        },
        async getPatientData(patientID: any) {
            if (!patientID) return;

            let patientData = await getOfflineRecords("patientRecords", { whereClause: { patientID } }).then((data: any) => data?.[0]);
            if (!patientData) patientData = await Service.getJson(`/patients/${patientID}/get_patient_record`, { id: patientID });
            return patientData;
        },
    },
    persist: true,
});
