// TODO: add text processing logic to escape characters outside of [A-Za-z0-9], consider :, \", (, ), \,
// TODO: add text processing logic to escape apostrophes
// TODO: maintain current x and current y throughout a label process

export interface LabelTemplate {
    width?: number;
    height?: number;
    orientation?: string;
    fields?: Array<{
        text?: string;
        sample?: string;
        left: number;
        top: number;
        rotation?: number;
        font_size?: number;
        font_horizontal_multiplier?: number;
        font_vertical_multiplier?: number;
        font_reverse?: boolean;
    }>;
    lines?: Array<{
        left: number;
        top: number;
        width: number;
        height: number;
        color?: number;
    }>;
    frames?: Array<{
        left: number;
        top: number;
        width: number;
        height: number;
        frame_width: number;
    }>;
    barcodes?: Array<{
        left: number;
        top: number;
        rotation?: number;
        format: string;
        narrow_bar_width: number;
        wide_bar_width: number;
        height: number;
        human_readable?: boolean;
        data: string;
    }>;
}

export interface MultiTextOptions {
    font_size?: number;
    font_horizontal_multiplier?: number;
    font_vertical_multiplier?: number;
    font_reverse?: boolean;
    hanging_indent?: number;
}

export class Label {
    public output: string = "";
    public template?: LabelTemplate;
    public width: number;
    public height: number;
    public orientation: string;
    public left_margin: number = 35;
    public right_margin: number = 25;
    public top_margin: number = 30;
    public bottom_margin: number = 26;
    public line_spacing: number = 6;
    public content_width: number;
    public content_height: number;
    public column: number = 0;
    public column_count: number = 1;
    public column_width: number;
    public column_height: number;
    public column_spacing: number = 0;
    public x: number;
    public y: number;
    public font_size: number = 1;
    public font_horizontal_multiplier: number = 1;
    public font_vertical_multiplier: number = 1;
    public font_reverse: boolean = false;
    public number_of_labels?: number;

    private gap: string = "026";
    private char_width: number = 0;
    private char_height: number = 0;
    private hanging_indent: number = 0;

    /**
     * Initialize a new label with height weight and orientation. The orientation
     * can be 'T' for top, or 'B' for bottom
     */
    constructor(width: number = 801, height: number = 329, orientation: string = "T", number_of_labels?: number) {
        this.width = width;
        this.height = height;
        this.orientation = orientation;
        this.number_of_labels = number_of_labels;
        this.content_width = this.width - (this.left_margin + this.right_margin);
        this.content_height = this.height - (this.top_margin + this.bottom_margin);
        this.column_width = this.content_width;
        this.column_height = this.content_height;
        this.x = 0;
        this.y = 0;
        this.header();
    }

    /**
     * Create a new label from a template
     */
    static fromTemplate(params: LabelTemplate, values?: any): Label {
        const label = new Label(params.width, params.height, params.orientation);

        (params.fields || []).forEach((field) => {
            label.drawText(
                field.text || field.sample || "",
                field.left,
                field.top,
                field.rotation || 0,
                field.font_size || 1,
                field.font_horizontal_multiplier || 1,
                field.font_vertical_multiplier || 1,
                field.font_reverse || false
            );
        });

        (params.lines || []).forEach((line) => {
            label.drawLine(line.left, line.top, line.width, line.height, line.color || 0);
        });

        (params.frames || []).forEach((frame) => {
            label.drawFrame(frame.left, frame.top, frame.width, frame.height, frame.frame_width);
        });

        (params.barcodes || []).forEach((barcode) => {
            label.drawBarcode(
                barcode.left,
                barcode.top,
                barcode.rotation || 0,
                barcode.format,
                barcode.narrow_bar_width,
                barcode.wide_bar_width,
                barcode.height,
                barcode.human_readable || false,
                barcode.data
            );
        });

        label.template = params;
        return label;
    }

    /**
     * Prints an initial header
     */
    private header(): void {
        this.x = this.left_margin;
        this.y = this.top_margin;
        this.column = 0;
        this.output += "\nN\n";
        this.output += `q${this.width}\n`;
        this.output += `Q${this.height}${this.gap ? "," + this.gap : ""}\n`;
        this.output += `Z${this.orientation}\n`;
    }

    /**
     * Append the final print command to the label and return the output
     */
    public print(label_sets: number = 1, label_copies?: number): string {
        const sets = this.number_of_labels || label_sets;
        this.output += `P${sets}`;
        if (label_copies) {
            this.output += `,${label_copies}`;
        }
        this.output += "\n";
        return this.output;
    }

    /**
     * Issue a reset printer command, which has the same effect as turning the
     * printer off and on
     */
    public resetPrinter(): string {
        this.output += "^@\n";
        return this.output;
    }

    /**
     * Draw a barcode:
     * @param x horizontal position
     * @param y vertical position
     * @param r rotation 1=90, 2=180, 3=270, 0=0
     * @param barcode_kind 1=code 128 A,B,C modes (see p. 51 EPL commands)
     * @param narrow_bar_width use 5
     * @param wide_bar_width use 15
     * @param h height of the barcode
     * @param print_code true/false, whether or not the human readable code should be printed
     * @param data the barcode data
     */
    public drawBarcode(
        x: number,
        y: number,
        r: number,
        barcode_kind: string,
        narrow_bar_width: number,
        wide_bar_width: number,
        h: number,
        print_code: boolean,
        data: string
    ): void {
        this.output += `B${x},${y},${r},${barcode_kind},${narrow_bar_width},${wide_bar_width},${h},${print_code ? "B" : "N"},"${data}"\n`;
    }

    /**
     * Draw a qr code:
     * @param x horizontal position
     * @param y vertical position
     * @param data the data to be encoded
     */
    public drawQrcode(x: number, y: number, data: string): void {
        this.output += `b${x},${y},Q,m2,s5,"${data}"\n`;
    }

    /**
     * Draw line
     * @param color 0=black, 1=white, 2=xor
     */
    public drawLine(x: number, y: number, w: number, h: number, color: number = 0): void {
        let lineType: string;
        switch (color) {
            case 1:
                lineType = "LW";
                break;
            case 2:
                lineType = "LE";
                break;
            default:
                lineType = "LO";
        }
        this.output += `${lineType}${x},${y},${w},${h}\n`;
    }

    /**
     * Draw diagonal line
     */
    public drawLineDiagonal(x: number, y: number, w: number, h: number, y2: number): void {
        this.output += `LS${x},${y},${w},${h},${y2}\n`;
    }

    /**
     * Draw a frame
     */
    public drawFrame(x: number, y: number, x2: number, y2: number, frame_width: number): void {
        this.output += `X${x},${y},${frame_width},${x2},${y2}\n`;
    }

    /**
     * Draw text
     * @param data The actual text, escape characters with "\"
     * @param x horizontal position
     * @param y vertical position
     * @param r rotation 1=90, 2=180, 3=270, 0=0
     * @param font_selection Font selection (see comments in original for details)
     * @param horizontal_multiplier expand the text horizontally (valid values 1-9)
     * @param vertical_multiplier expand the text vertically (valid values 1-9)
     * @param reverse true/false, whether the text should be reversed
     */
    public drawText(
        data: string,
        x: number,
        y: number,
        r: number = 0,
        font_selection: number = 1,
        horizontal_multiplier: number = 1,
        vertical_multiplier: number = 1,
        reverse: boolean = false
    ): void {
        try {
            data = data.replace(/'/g, "\\\\'");
        } catch (error) {
            // Keep original data if replacement fails
        }
        this.output += `A${x},${y},${r},${font_selection},${horizontal_multiplier},${vertical_multiplier},${reverse ? "R" : "N"},"${data}"\n`;
    }

    /**
     * Word wrapping, column wrapping, label wrapping text code
     */
    public drawMultiText(data: string, options: MultiTextOptions = {}): void {
        data = data.replace(/'/g, "\\\\'");

        if (options.font_size !== undefined) this.font_size = options.font_size;
        if (options.font_horizontal_multiplier !== undefined) this.font_horizontal_multiplier = options.font_horizontal_multiplier;
        if (options.font_vertical_multiplier !== undefined) this.font_vertical_multiplier = options.font_vertical_multiplier;
        if (options.font_reverse !== undefined) this.font_reverse = options.font_reverse;

        const [char_width, char_height] = this.getCharSizes(this.font_size, this.font_horizontal_multiplier, this.font_vertical_multiplier);
        this.char_width = char_width;
        this.char_height = char_height;
        this.hanging_indent = options.hanging_indent || 0;

        // Print each line separately
        data.split("\n").forEach((line) => {
            if (!line.trim()) return;

            const words = line.split(/\s+/);
            let size = 0;
            let word_start_index = 0;
            let word_count = 0;
            let new_line = true;
            let need_hanging_indent = false;

            while (word_start_index + word_count < words.length) {
                const next_word = word_start_index + word_count;
                const next_word_size = this.getWordSize(this.char_width, words[next_word], !new_line);

                // Check if we need to wrap
                if (size + next_word_size >= this.column_width) {
                    // Break the line, write what we have and continue
                    const text =
                        (need_hanging_indent ? " ".repeat(this.hanging_indent) : "") +
                        words.slice(word_start_index, word_start_index + word_count).join(" ");
                    this.checkBounds();
                    this.drawText(
                        text,
                        this.x,
                        this.y,
                        0,
                        this.font_size,
                        this.font_horizontal_multiplier,
                        this.font_vertical_multiplier,
                        this.font_reverse
                    );

                    // Allow for indents
                    need_hanging_indent = true;
                    // Start from this word, count it and its size
                    word_start_index = next_word;
                    // Account for writing a single, really long word
                    if (word_count === 0) word_start_index += 1;
                    word_count = 1;
                    size = next_word_size;
                    this.y += this.line_spacing + this.char_height;
                    new_line = true;
                } else {
                    size += next_word_size;
                    word_count += 1;
                    new_line = false;
                }
            }

            // Write out the end of the current set of words
            if (word_start_index <= words.length - 1) {
                const text =
                    (need_hanging_indent ? " ".repeat(this.hanging_indent) : "") +
                    words.slice(word_start_index, word_start_index + word_count).join(" ");
                this.checkBounds();
                this.drawText(
                    text,
                    this.x,
                    this.y,
                    0,
                    this.font_size,
                    this.font_horizontal_multiplier,
                    this.font_vertical_multiplier,
                    this.font_reverse
                );
            }
            this.y += this.line_spacing + this.char_height;
        });
    }

    private checkBounds(): void {
        // If we have run out of room, move to the next column
        if (this.y + this.char_height <= this.height - this.bottom_margin) return;

        this.column += 1;
        this.y = this.top_margin;

        // If we have run out of columns, move to the next label
        if (this.column > this.column_count - 1) {
            this.column = 0;
            this.print(1);
            this.header();
        }
        this.x = this.left_margin + this.column * (this.column_width + this.column_spacing);
    }

    private getCharSizes(font_selection: number, horizontal_multiplier: number, vertical_multiplier: number): [number, number] {
        let char_width: number;
        let char_height: number;

        switch (font_selection) {
            case 2:
                char_width = 12;
                char_height = 20;
                break;
            case 3:
                char_width = 14;
                char_height = 24;
                break;
            case 4:
                char_width = 16;
                char_height = 32;
                break;
            case 5:
                char_width = 36.25;
                char_height = 48;
                break;
            default:
                char_width = 10;
                char_height = 14;
        }

        return [char_width * horizontal_multiplier, char_height * vertical_multiplier];
    }

    private getWordSize(char_width: number, word: string, need_space: boolean): number {
        return Math.floor(char_width * (word.length + (need_space ? 1 : 0)));
    }
}

export class StandardLabel extends Label {
    constructor() {
        // In TypeScript, we'd typically get this from configuration or environment
        // For now, using default dimensions like the Ruby fallback
        const dimensions = "801,329".split(",").map((d) => parseInt(d, 10));
        super(dimensions[0], dimensions[1], "T");
    }
}

// Alias for backward compatibility
export class ZebraPrinter extends Label {}

// Default export for the module
export default {
    Label,
    StandardLabel,
    ZebraPrinter,
};
