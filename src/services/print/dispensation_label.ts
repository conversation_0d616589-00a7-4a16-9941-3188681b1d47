import { DrugOrderService } from "../drug_order_service";

export async function print_dispensation(label: any, patient: any, visitsDate: any) {
    const lastVaccine = await DrugOrderService.getLastDrugsReceived(patient.patientID);
    const drugNames = lastVaccine.map((item: any) => item.drug.name + " " + item.frequency + " " + item.quantity).join(", ");
    if (drugNames) {
        return `DISPENSATION: ${drugNames} `;
    }
    return "";
}
