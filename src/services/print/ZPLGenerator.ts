/**
 * A TypeScript class to generate Zebra Programming Language (ZPL) commands
 * for printing labels with barcodes, text, and images.
 */
export class ZPLGenerator {
    private labelWidth: number;
    private labelHeight: number;
    private printDensity: number; // Dots per millimeter
    private elements: string[] = []; // To store ZPL commands for individual elements

    /**
     * Initializes the ZPLGenerator with label dimensions and print density.
     * @param labelWidthDots The width of the label in dots. Default is 600 dots (e.g., 2 inches at 300 DPI).
     * @param labelHeightDots The height of the label in dots. Default is 400 dots (e.g., 1.33 inches at 300 DPI).
     * @param printDensityDpmm Dots per millimeter. Common values are 8 (203 DPI) or 12 (300 DPI).
     * This affects how positions and sizes are interpreted by the printer.
     */
    constructor(labelWidthDots: number = 600, labelHeightDots: number = 400, printDensityDpmm: number = 8) {
        this.labelWidth = labelWidthDots;
        this.labelHeight = labelHeightDots;
        this.printDensity = printDensityDpmm;
    }

    /**
     * Generates the ZPL command to start a new label format.
     * @returns The ZPL command string.
     */
    private _startLabel(): string {
        // ^XA: Start Format
        // ^PW: Print Width (in dots)
        // ^LL: Label Length (in dots)
        return `^XA^PW${this.labelWidth}^LL${this.labelHeight}`;
    }

    /**
     * Generates the ZPL command to end a label format and print it.
     * @returns The ZPL command string.
     */
    private _endLabel(): string {
        // ^XZ: End Format
        return "^FS^XZ"; // ^FS is Field Separator, often used before ^XZ
    }

    /**
     * Adds a barcode to the label.
     * @param data The data to encode in the barcode.
     * @param xPos X-coordinate (horizontal position) in dots.
     * @param yPos Y-coordinate (vertical position) in dots.
     * @param heightDots Height of the barcode in dots. Default is 100.
     * @param narrowBarWidth Width of the narrow bar in dots. Default is 2.
     * @param wideBarWidth Width of the wide bar in dots. Default is 4.
     * @param type Type of barcode. Currently supports "C128" (Code 128).
     */
    public addBarcode(
        data: string,
        xPos: number,
        yPos: number,
        heightDots: number = 100,
        narrowBarWidth: number = 2,
        wideBarWidth: number = 4,
        type: string = "C128"
    ): void {
        let zplBarcode: string = "";
        if (type === "C128") {
            // ^FOx,y: Field Origin (position)
            // ^BYw,h,i: Barcode Field Defaults (w=narrow bar width, h=height, i=wide to narrow ratio)
            // ^BC: Barcode 128
            // ,h,f,g,o: h=height, f=print interpretation line (Y/N), g=print interpretation line above (Y/N), o=orientation (N=normal)
            zplBarcode =
                `^FO${xPos},${yPos}` +
                `^BY${narrowBarWidth},${heightDots},${wideBarWidth}` +
                `^BCN,${heightDots},Y,N,N` + // N=Normal orientation, Y=print human readable, N=no human readable above
                `^FD${data}^FS`;
        } else {
            console.warn(`Warning: Barcode type '${type}' not supported yet. Skipping.`);
        }
        this.elements.push(zplBarcode);
    }

    /**
     * Adds text to the label.
     * @param text The text string to print.
     * @param xPos X-coordinate (horizontal position) in dots.
     * @param yPos Y-coordinate (vertical position) in dots.
     * @param fontSize Font size in dots (approximate height). Default is 30.
     * For font type '0', this is a multiplier.
     * @param fontType ZPL font type. '0' is the default scalable font.
     * Other options are A-Z, 0-9 (fixed fonts).
     * @param fieldWidth Field block width in dots for text wrapping. 0 for no wrapping.
     * @param maxLines Maximum number of lines for the text block.
     */
    public addText(
        text: string,
        xPos: number,
        yPos: number,
        fontSize: number = 30,
        fontType: string = "0",
        fieldWidth: number = 0,
        maxLines: number = 1
    ): void {
        // ^FOx,y: Field Origin (position)
        // ^Aft,h,w: Font (f=font type, t=orientation, h=height, w=width)
        // ^FBw,l,i,j,k: Field Block (w=width, l=lines, i=indent, j=alignment, k=text justification)
        // ^FD: Field Data
        let zplText: string = `^FO${xPos},${yPos}` + `^A${fontType}N,${fontSize},${fontSize}`; // N=Normal orientation, height, width
        if (fieldWidth > 0) {
            zplText += `^FB${fieldWidth},${maxLines},0,L,0`; // Left align, no indent
        }
        zplText += `^FD${text}^FS`;
        this.elements.push(zplText);
    }

    /**
     * Adds an image to the label. The image must be pre-converted to ZPL's
     * graphic format (e.g., using a ZPL converter tool).
     * @param graphicName The name to assign to the graphic (e.g., "LOGO.GRF").
     * @param xPos X-coordinate (horizontal position) in dots.
     * @param yPos Y-coordinate (vertical position) in dots.
     * @param binaryDataZ64 Base64 encoded binary data of the graphic in Z64 format.
     * This is the actual ZPL graphic data string.
     * @param widthBytes Number of bytes per row of the graphic.
     * @param totalRows Total number of rows (height) of the graphic.
     */
    public addImage(graphicName: string, xPos: number, yPos: number, binaryDataZ64: string, widthBytes: number, totalRows: number): void {
        // ^FOx,y: Field Origin (position)
        // ^GFA,total_bytes,width_bytes,total_rows,data: Graphic Field ASCII
        // total_bytes = width_bytes * total_rows
        const totalBytes: number = widthBytes * totalRows;
        const zplImageStore: string = `^GFA,${totalBytes},${widthBytes},${totalRows},${binaryDataZ64}^FS`;
        // For simplicity, we'll embed the graphic data directly.
        // For larger images, it's better to download them to the printer first using ^DGF.
        this.elements.push(zplImageStore);

        // Note: In a real scenario, you'd typically download the graphic once
        // using ^DGF and then recall it multiple times using ^XG.
        // For this class, we're assuming the graphic data is provided directly
        // and will be embedded with ^GFA. If the user wants to store it,
        // they'd use ^DGF separately or provide the graphic_name and data
        // to a dedicated store_graphic method.
        // For now, we'll just add the ^GFA command directly.
        // If the user wants to recall a *pre-stored* graphic, they'd use addImageRecall.
    }

    /**
     * Recalls and prints a graphic that has already been stored on the printer.
     * @param graphicName The filename of the stored graphic (e.g., "R:LOGO.GRF").
     * @param xPos X-coordinate (horizontal position) in dots.
     * @param yPos Y-coordinate (vertical position) in dots.
     * @param xMagnification X-axis magnification factor (1-10).
     * @param yMagnification Y-axis magnification factor (1-10).
     */
    public addImageRecall(graphicName: string, xPos: number, yPos: number, xMagnification: number = 1, yMagnification: number = 1): void {
        // ^FOx,y: Field Origin (position)
        // ^XGf,x,y: Recall Graphic (f=filename, x=x_magnification, y=y_magnification)
        const zplImageRecall: string = `^FO${xPos},${yPos}` + `^XG${graphicName},${xMagnification},${yMagnification}^FS`;
        this.elements.push(zplImageRecall);
    }

    /**
     * Generates the complete ZPL string from all added elements.
     * @returns The complete ZPL command string.
     */
    public getZPLString(): string {
        let fullZPL: string = this._startLabel();
        for (const elementZPL of this.elements) {
            fullZPL += elementZPL;
        }
        fullZPL += this._endLabel();
        return fullZPL;
    }
}
