import { useDemographicsStore } from "@/stores/DemographicStore";
import { ZPLGenerator } from "@/services/print/ZPLGenerator";
import { createModal, toastDanger, toastSuccess } from "@/utils/Alerts";
import SelectPrinterModal from "@/components/Modal/SelectPrinterModal.vue";
import { Service } from "../service";
export const sendZPLCommand = async (printerData: any) => {
    try {
        const response = await fetch(`https://${printerData.ip_address}/print`, {
            method: "POST",
            mode: "cors",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                zpl: printerData.zpl,
            }),
        });
        if (response.ok) {
            // Check if the response status is 2xx
            toastSuccess(`Test print sent successfully to ${printerData.printer_name}`);
        } else {
            const errorData = await response.json(); // Attempt to parse error message from response
            toastDanger(`Failed to send test print to ${printerData.printer_name}. Error: ${errorData.message || response.statusText}`);
        }
    } catch (error) {
        console.error("Error sending test print:", error);
        toastDanger(`Error sending test print to ${printerData.printer_name}. Please check your network or server connection.`);
    }
};

export async function SelectPrinter() {
    const data = Service.getDefaultPrinter();
    if (data?.ip_address) return data.ip_address;
    const selectedPrinted = await createModal(SelectPrinterModal, { class: "large-modal" }, true);
    return selectedPrinted?.ip_address;
}

export function generateBarcodeWithName(
    barcode: string,
    textLineOne: string,
    textLineTwo: string,
    xStart: number = 10, // Starting X position for both name and barcode
    yName: number = 15, // Y position for the name
    yBarcode: number = 100 // Y position for the barcode (below the name)
): string {
    const zplGenerator = new ZPLGenerator(750, 450, 8); // Assuming 800 is the full sticker width in dots/units
    zplGenerator.addText(textLineOne, xStart, yName, 30, "0", 800); // Font size 30, field width 800
    zplGenerator.addText(textLineTwo, xStart, yName + 40, 30, "0", 800); // Added offset for second line, field width 800

    // IMPORTANT: Adjust the 5th parameter (narrow bar width)
    // Also, consider reducing xStart if the barcode is pushed too far right.
    const desiredNarrowBarWidth = 4; // Experiment with values like 5, 6, 7, 8, etc.
    const barcodeXStart = 20; // Adjust this if you need to move the barcode left to fit

    zplGenerator.addBarcode(barcode, barcodeXStart, yBarcode, 150, desiredNarrowBarWidth, 8, "C128");

    const zplString = zplGenerator.getZPLString();
    return zplString;
}

export function canvasToZPL(canvas: HTMLCanvasElement): string {
    const ctx = canvas.getContext("2d");
    if (!ctx) throw new Error("Could not get canvas context");

    const width = canvas.width;
    const height = canvas.height;

    const imageData = ctx.getImageData(0, 0, width, height);
    const pixels = imageData.data;

    const bytesPerRow = Math.ceil(width / 8);
    const totalBytes = bytesPerRow * height;
    let zplData = "";

    const hex = (n: number) => n.toString(16).padStart(2, "0").toUpperCase();

    for (let y = 0; y < height; y++) {
        let row = "";

        for (let xByte = 0; xByte < bytesPerRow; xByte++) {
            let byte = 0;

            for (let bit = 0; bit < 8; bit++) {
                const x = xByte * 8 + bit;
                if (x >= width) continue;

                const idx = (y * width + x) * 4;
                const r = pixels[idx];
                const g = pixels[idx + 1];
                const b = pixels[idx + 2];
                const a = pixels[idx + 3];

                // Convert to grayscale
                const grayscale = (r + g + b) / 3;
                const isDark = a > 128 && grayscale < 128;

                byte = (byte << 1) | (isDark ? 1 : 0);
            }

            row += hex(byte);
        }

        zplData += row + "\n";
    }

    const totalBytesHex = totalBytes.toString();
    const bytesPerRowHex = bytesPerRow.toString();
    const heightHex = height.toString();

    return `^GFA,${totalBytesHex},${totalBytesHex},${bytesPerRowHex},${zplData}`;
}
