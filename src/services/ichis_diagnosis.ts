import { ObservationService } from "./observation_service";
import { Service } from "./service";

export const buildIchisDiagnosis = async (patientRecord: any) => {
    const ichisDiagnosisObj = Service.getIchisDiagnosis();
    if (ichisDiagnosisObj.length <= 0) return patientRecord;
    const data = await Promise.all(
        ichisDiagnosisObj.map(async (item: any) => {
            const onlyNumbers = item?.value?.match(/[\d.]+/)?.[0] || "";
            const date = item?.date;
            if (item.display == "NCD_CV_D_Likelihood of Diabetes") {
                const eventId = item?.id?.split("-");
                return await ObservationService.buildValueNumber("Unspecified Diabetes", onlyNumbers, null, null, date, eventId[1]);
            }
            if (item.display == "NCD_SHD_CV_Client waist circumference")
                return await ObservationService.buildValueNumber("Waist circumference", onlyNumbers, null, null, date);
            if (item.display == "NCD_SHD_CV_Client systolic blood pressure")
                return await ObservationService.buildValueNumber("Systolic", onlyNumbers, null, null, date);
            if (item.display == "NCD_SHD_CV_Client diastolic blood pressure")
                return await ObservationService.buildValueNumber("Diastolic", onlyNumbers, null, null, date);
        })
    );
    patientRecord.observations.push({
        encounter_type: "VITALS",
        status: "unsaved",
        obs: data,
    });
    return patientRecord;
};
