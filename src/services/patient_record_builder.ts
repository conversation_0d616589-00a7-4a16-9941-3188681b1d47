import HisDate from "@/utils/Date";
import { Service } from "./service";
import { getOfflineVaccineSchedule } from "@/apps/Immunization/services/vaccines_service";
import { ObservationService } from "./observation_service";
import { RegistrationService } from "./registration_service";
import { EncounterTypeId } from "@/services/encounter_type";

export class PatientRecordBuilder {
    private getRegion(regionId: number): string | undefined {
        const regions: Record<number, string> = {
            1: "Central Region",
            2: "Northern Region",
            3: "Southern Region",
            4: "Foreign",
        };
        return regions[regionId];
    }

    private hasValidData(obj: any, requiredFields: string[] = []): boolean {
        if (!obj || typeof obj !== "object") return false;

        // If no required fields specified, check if object has any meaningful data
        if (requiredFields.length === 0) {
            return Object.values(obj).some((value) => value !== null && value !== undefined && value !== "");
        }

        // Check if all required fields exist and have valid values
        return requiredFields.every((field) => obj[field] !== null && obj[field] !== undefined && obj[field] !== "");
    }

    private async buildObservation(label: string, value: any): Promise<any> {
        if (!value) return null;

        const actualValue = typeof value === "object" && value.name ? value.name : value;
        return await ObservationService.buildValueText(label, actualValue);
    }

    private buildGuardianInfo(guardianInfo: any) {
        const guardian: any = {};

        guardian.given_name = guardianInfo?.guardianFirstname || "";
        guardian.middle_name = guardianInfo?.guardianMiddleName || "";
        guardian.family_name = guardianInfo?.guardianLastname || "";
        guardian.cell_phone_number = guardianInfo?.guardianPhoneNumber.phoneNumber || "";
        guardian.national_id = guardianInfo?.guardianNationalID || "";
        guardian.gender = "";
        guardian.birthdate = "";
        guardian.birthdate_estimated = "false";
        guardian.home_region = "";
        guardian.home_district = "";
        guardian.home_traditional_authority = "";
        guardian.home_village = "";
        guardian.current_region = "";
        guardian.current_district = "";
        guardian.current_traditional_authority = "";
        guardian.current_village = "";
        guardian.landmark = "";
        return { saved: [], unsaved: [guardian] };
    }

    private async buildVitals(birthRegistration: any) {
        if (!birthRegistration?.Weight) return null;

        return {
            saved: [],
            unsaved: [await this.buildObservation("Weight", birthRegistration.Weight)],
        };
    }
    private async buildARTPatientType(clinic: string, type: string) {
        const obs = [];
        let results = [];
        if (clinic) obs.push(await ObservationService.buildValueText("Art clinic location", clinic));
        if (type) obs.push(await ObservationService.buildValueCoded("Type of patient", type));
        if (obs.length === 0) return [];
        results = [
            {
                encounter_type: EncounterTypeId.REGISTRATION,
                status: "unsaved",
                obs,
            },
        ];
        return results;
    }
    private async buildBirthRegistrationObservations(birthRegistration: any) {
        if (!this.hasValidData(birthRegistration)) return null;

        const observations = await Promise.all([
            this.buildObservation(
                "How many doses of Tdv did the mother receive?",
                birthRegistration["How many doses of Tdv did the mother receive?"]
            ),
            this.buildObservation("Protected at birth", birthRegistration["Protected at birth"]),
            this.buildObservation("HIV status", birthRegistration["HIV status"]),
        ]);

        const validObservations = observations.filter((obs) => obs !== null);
        return validObservations.length > 0 ? validObservations : null;
    }

    async buildPatientRecord(data: any, ddeId: any, patientID = "") {
        const { personalInformation, currentLocation, homeLocation, birthRegistration, guardianInformation, socialHistory, patientType } = data;
        return {
            patientID: patientID || ddeId || "",
            ID: ddeId || "",
            NcdID: "",
            personInformation: {
                given_name: personalInformation?.firstname || "",
                middle_name: personalInformation?.middleName || "",
                family_name: personalInformation?.lastname || "",
                gender: personalInformation?.gender || "",
                birthdate: personalInformation?.Estimate
                    ? new RegistrationService().calculateDoB(personalInformation?.estimation, personalInformation?.estimation_unit || "Years")
                    : personalInformation?.birthdate || "",
                birthdate_estimated: personalInformation?.Estimate ? "true" : "false",
                home_region: this.getRegion(homeLocation?.home_district?.region_id) || "",
                home_district: homeLocation?.home_district?.name || "",
                home_traditional_authority: homeLocation?.home_traditional_authority?.name || "",
                home_village: homeLocation?.home_village?.name || "",
                current_region: this.getRegion(currentLocation?.current_district?.region_id) || "",
                current_district: currentLocation?.current_district?.name || "",
                current_traditional_authority: currentLocation?.current_traditional_authority?.name || "",
                current_village: currentLocation?.current_village?.name || "",
                country: data?.country.country.name || "",
                landmark: currentLocation?.closestLandmark?.name || "",
                cell_phone_number: personalInformation["Phone Number"]?.phoneNumber || "",
                occupation: socialHistory?.occupation || "",
                marital_status: socialHistory?.maritalStatus || "",
                religion: socialHistory?.religion?.name || "",
                education_level: socialHistory?.highestLevelOfEducation || "",
            },
            guardianInformation: this.buildGuardianInfo(guardianInformation),
            birthRegistration: await this.buildBirthRegistrationObservations(birthRegistration),
            otherPersonInformation: {
                nationalID: personalInformation?.nationalID || "",
                ichisID: Service.getIchisId() || "",
                TEI: Service.getTEI() || "",
                birthID: birthRegistration["Serial Number"] || "",
                relationshipID: guardianInformation?.relationship?.id || "",
            },
            vitals: await this.buildVitals(birthRegistration),
            vaccineSchedule: await getOfflineVaccineSchedule(personalInformation?.gender, personalInformation?.birthdate),
            vaccineAdministration: {
                orders: [],
                obs: [],
                voided: [],
            },
            observations: await this.buildARTPatientType(patientType?.facility?.name, patientType?.patient_type),
            appointments: {
                saved: [],
                unsaved: [],
            },
            saveStatusPersonInformation: "pending",
            saveStatusGuardianInformation: "pending",
            saveStatusBirthRegistration: "pending",
            saveStatusVitals: "pending",
            date_created: "",
            creator: "",
            sync_status: "unsynced",
        };
    }
}
