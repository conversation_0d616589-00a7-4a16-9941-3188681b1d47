import PersonMatchView from "@/components/PersonMatchView.vue";
import { getOfflineRecords } from "./offline_service";
import { PatientDemographicsExchangeService } from "./patient_demographics_exchange_service";
import { Service } from "./service";
import { createModal } from "@/utils/Alerts";
import { PatientService } from "./patient_service";
import { useDemographicsStore } from "@/stores/DemographicStore";
import { useWorkerStore } from "@/stores/workerStore";
import { getddeNPIDObectViaMODS } from "./mods_service";
const workerStore = useWorkerStore();

export class DDEService {
    async getDDEIds() {
        const useIndexDB = Service.getUseIndexDBStatus();
        const useMods = Service.getModsStatus();
        const useApi = Service.getAPIStatus();

        if (useMods) {
            const ddeNPID: any = await getddeNPIDObectViaMODS();
            if (ddeNPID) {
                const npid_array = [{ npid: ddeNPID?.npid }];
                return npid_array;
            } else {
                return [];
            }  
        }
        
        if (useIndexDB) {
            return await getOfflineRecords("dde");
        }

        if (useApi) {
            try {
                const ddeNPID = await Service.getJson("/dde/patients/sync_npids", {
                    count: 1,
                    program_id: Service.getProgramID(),
                });
                return ddeNPID?.npids || [];
            } catch (error) {
                return [];
            }
        }

        return [];
    }
    async updateDDEIds(ddeIds: any) {
        ddeIds = ddeIds.slice(1);
        workerStore.postData("OVERRIDE_OBJECT_STORE", { storeName: "dde", data: ddeIds });
    }
    async possibleDuplicates(personInformation: any) {
        try {
            const ddeInstance = new PatientDemographicsExchangeService();
            const deduplicationData = await ddeInstance.checkPotentialDuplicates(personInformation);

            if (deduplicationData.length > 0) {
                const response: any = await createModal(PersonMatchView, { class: "fullScreenModal" }, true, {
                    to_be_registered: personInformation,
                    deduplicationData: deduplicationData,
                });

                if (response != "dismiss" && response != "back") {
                    const result = await ddeInstance.importPatient(response?.person?.id);
                    const patientData = await PatientService.findByID(result.patient_id);
                    await useDemographicsStore().setPatientRecord(patientData);
                    return true;
                } else if (response == "back") {
                    return true;
                }
                return false;
            } else {
                return false;
            }
        } catch (error) {
            return false;
        }
    }
}
