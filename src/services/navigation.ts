// services/navigation.ts - Using localStorage
import type { Router } from "vue-router";
import { toastDanger } from "@/utils/Alerts";

class NavigationService {
    private router?: Router;
    private static readonly RELOAD_FLAG_KEY = 'app_reloaded_flag';
    private static readonly RELOAD_MESSAGE_KEY = 'app_reload_message';
    
    setRouter(router: Router) {
        this.router = router;
    }
    
    push(path: string) {
        this.router?.push(path);
    }
    
    reloadApp(routeName?: string, message?: string) {
        if (!this.router) {
            console.warn('Router not initialized');
            return;
        }
        
        // Store the message if provided
        if (message) {
            localStorage.setItem(NavigationService.RELOAD_MESSAGE_KEY, message);
        }
        
        // Set flag that app is being reloaded
        localStorage.setItem(NavigationService.RELOAD_FLAG_KEY, 'true');
        
        const targetRoute = routeName || '/';
        this.router.push(targetRoute).then(() => {
            this.router!.go(0);
        });
    }
    
    // Call this in your main app after reload to check and show message
    checkAndShowReloadMessage(): void {
        const wasReloaded = localStorage.getItem(NavigationService.RELOAD_FLAG_KEY) === 'true';
        const message = localStorage.getItem(NavigationService.RELOAD_MESSAGE_KEY);
        
        if (wasReloaded) {
            console.log('App was reloaded');
            
            if (message) {
                console.log(`Showing reload message: ${message}`);
                toastDanger(message, 20000000);
            }
            
            // Set flag to false and clean up message
            localStorage.setItem(NavigationService.RELOAD_FLAG_KEY, 'false');
            this.clearReloadMessage();
        }
    }
    
    // Execute post-reload action with message
    executePostReloadAction(): void {
        const wasReloaded = localStorage.getItem(NavigationService.RELOAD_FLAG_KEY) === 'true';
        
        if (wasReloaded) {
            const message = localStorage.getItem(NavigationService.RELOAD_MESSAGE_KEY);
            
            if (message) {
                console.log(`Showing post-reload message: ${message}`);
                toastDanger(message, 20000000);
            }
            
            // Set flag to false and clean up message
            localStorage.setItem(NavigationService.RELOAD_FLAG_KEY, 'false');
            this.clearReloadMessage();
        }
    }
    
    // Set a message for next reload without triggering reload
    setReloadMessage(message: string): void {
        localStorage.setItem(NavigationService.RELOAD_MESSAGE_KEY, message);
        localStorage.setItem(NavigationService.RELOAD_FLAG_KEY, 'true');
    }
    
    // Check if app was reloaded
    wasAppReloaded(): boolean {
        return localStorage.getItem(NavigationService.RELOAD_FLAG_KEY) === 'true';
    }
    
    // Get stored reload message
    getReloadMessage(): string | null {
        return localStorage.getItem(NavigationService.RELOAD_MESSAGE_KEY);
    }
    
    // Clear reload flags and message
    clearReloadData(): void {
        localStorage.setItem(NavigationService.RELOAD_FLAG_KEY, 'false');
        this.clearReloadMessage();
    }
    
    private clearReloadMessage(): void {
        localStorage.removeItem(NavigationService.RELOAD_MESSAGE_KEY);
    }
}

export const navigationService = new NavigationService();