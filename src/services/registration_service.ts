import HisDate from "@/utils/Date";
import dayjs from "dayjs";
import { Service } from "./service";
import { savePatientRecord } from "@/services/offline_service";
import { createModal, toastDanger, toastSuccess, toastWarning } from "@/utils/Alerts";
import ConfirmPrinting from "@/components/ConfirmPrinting.vue";
import { PrintoutService } from "./printout_service";
import { DDEService } from "./dde_service";
import { PatientRecordBuilder } from "./patient_record_builder";
import { buildIchisDiagnosis } from "@/services/ichis_diagnosis";
import { tr } from "date-fns/locale";
import { checkLiveAPIHealthViaMODS, getUseMODS, isPatientPossibleDuplicate } from "./mods_service";

export class RegistrationService extends Service {
    private patientRecordBuilder: PatientRecordBuilder;

    constructor() {
        super();
        this.patientRecordBuilder = new PatientRecordBuilder();
    }

    async saveRegistrationData(data: any) {
        const is_mods_enabled = getUseMODS();
        const ddeIds = await new DDEService().getDDEIds();
        const _perform_save_ = async () => {
            const check_dde_ids = ddeIds.length <= 0 || ddeIds[0]?.length <= 0;
            if (check_dde_ids) toastDanger("DDE IDs are not available.");
            if ((check_dde_ids && !Service.getAPIStatus()) || (check_dde_ids && Service.getUseIndexDBStatus())) {
                if (is_mods_enabled && check_dde_ids) {
                    const live_api_health = await checkLiveAPIHealthViaMODS();
                    if (live_api_health) {
                        //continue with registration
                    } else {
                        return false;
                    }
                } else {
                    return false;
                }
            }

            let patientRecord = await this.patientRecordBuilder.buildPatientRecord(data, ddeIds[0]?.npid);

            if (Service.getProgramID() == 32) patientRecord = await buildIchisDiagnosis(patientRecord);

            if (Service.getAPIStatus() && !Service.getModsStatus() &&(await new DDEService().possibleDuplicates(patientRecord.personInformation)))
                return true;

            if (await savePatientRecord(patientRecord)) {
                toastSuccess("Registration successful");
                sessionStorage.setItem("ichis_diagnosis", JSON.stringify([]));
                if (Service.getUseIndexDBStatus()) await new DDEService().updateDDEIds(ddeIds);

                await this.printBarcode();
                return true;
            }
            return false;
        }

        if (is_mods_enabled) {
            // building patient record for duplication comparison
            const temp_patientRecord = await this.patientRecordBuilder.buildPatientRecord(data, "");
            // checking for possible duplicates via MODS
            const result = await isPatientPossibleDuplicate(temp_patientRecord.personInformation as any);
            if (result.success) {
                if (result.action == "cancel") {
                    toastWarning("Patient registration cancelled by user.");
                    return false; // User cancelled the duplicate check
                }

                if (result.action == "proceed") {
                    // Proceed with saving the patient record
                    toastWarning("Proceeding with registration despite possible duplicates.");
                    return await _perform_save_()
                }
            }

            if (result.success == false || result.action == "request-failed") {
                toastDanger("Cannot proceed with registration due to request failure.");
                return false; // if MODS is used, we do not proceed with the local save
            }
        }

        if (is_mods_enabled == false) {
            return await _perform_save_()
        }
    }
    async updateDemographics(data: any) {
        let patientRecord = await this.patientRecordBuilder.buildPatientRecord(data, Service.getNationalID(), Service.getPatientID());
        patientRecord.saveStatusPersonInformation = "edit";
        patientRecord.saveStatusGuardianInformation = "edit";
        patientRecord.saveStatusBirthRegistration = "edit";
        patientRecord.saveStatusVitals = "edit";
        if (await savePatientRecord(patientRecord)) {
            toastSuccess("Updated successful");
            return true;
        }
        return false;
    }

    checkIfChild(personalData: any) {
        if (!personalData?.estimation && !personalData?.birthdate) {
            return {
                moreThanThirteenYears: true,
                underNineMonths: false,
                underFiveYears: false,
            };
        }

        let birthdate = "";
        if (personalData.estimation) {
            birthdate = this.calculateDoB(personalData.estimation, personalData.estimation_unit || "Years") || "";
        } else {
            birthdate = personalData.birthdate;
        }

        return {
            moreThanThirteenYears: HisDate.getAgeInYears(birthdate) > 13,
            underNineMonths: HisDate.ageInMonths(birthdate) < 9,
            underFiveYears: HisDate.getAgeInYears(birthdate) < 5,
        };
    }

    async printBarcode() {
        const response: any = await createModal(ConfirmPrinting, { class: "small-confirm-modal " });
        if (response === "dismiss") return;
        new PrintoutService().printData("barcode");
    }

    calculateDoB(value: number, unit: string): string | null {
        try {
            let sessionDate = dayjs(Service.getSessionDate());

            // Handle age calculation (negative years)
            if (unit === "Years" && value < 0) {
                sessionDate = sessionDate.add(Math.abs(value), "years");
            } else {
                // Subtract units for date calculation
                switch (unit) {
                    case "Days":
                        sessionDate = sessionDate.subtract(value, "days");
                        break;
                    case "Months":
                        sessionDate = sessionDate.subtract(value, "months");
                        break;
                    case "Years":
                        sessionDate = sessionDate.subtract(value, "years");
                        break;
                    default:
                        return null;
                }
            }

            return HisDate.toStandardHisDisplayFormat(sessionDate.format("YYYY-MM-DD")) || "";
        } catch (error) {
            console.error("Error calculating date:", error);
            return null;
        }
    }
}
