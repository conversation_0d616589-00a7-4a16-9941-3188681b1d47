import { StagesService } from "@/apps/OPD/services/stages_service";
import { createOfflineVisit } from "@/apps/OPD/services/visits_service";

export const postOfflineJson = async (name: string, params = {} as Record<string, any>) => {
    switch (name) {
        case "visits":
            return createOfflineVisit(params);
        case "stages":
            return StagesService.addPatientToStageOffline(params);
    }
};
