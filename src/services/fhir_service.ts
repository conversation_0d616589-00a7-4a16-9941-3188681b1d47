import { Service } from "./service";

export class FhirService extends Service {
    constructor() {
        super();
    }
    static async getFhirPatients() {
        return await super.getJson(`fhir/patients/`);
    }
    static async getFhirPatient(PatientIdentifier: string) {
        return await super.getJson(`fhir/patient/${PatientIdentifier}`, { id: PatientIdentifier });
    }
    static async getFhirObservations(PatientIdentifier: string) {
        return await super.getJson(`fhir/patient/${PatientIdentifier}/observations/`, { id: PatientIdentifier });
    }
}
