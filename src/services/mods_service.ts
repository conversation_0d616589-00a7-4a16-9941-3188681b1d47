// src/services/patientService.ts
import { useModal } from "@/composables/useModal"; // <-- NEW: Import useModal
import { useWorkerStore } from "@/stores/workerStore";
import { useDemographicsStore } from "@/stores/DemographicStore";
import { toastWarning, toastSuccess, toastDanger } from "@/utils/Alerts";
import { getOfflinePatientData, getOfflineRecords, openDatabase } from "./offline_service";

// --- Interface Definitions (Consistent with previous iteration) ---
interface PatientDemographics {
    given_name: string;
    middle_name: string;
    family_name: string;
    gender: string;
    birthdate: string;
    birthdate_estimated: boolean;
    home_region: string;
    home_district: string;
    home_traditional_authority: string;
    home_village: string;
    current_region: string;
    current_district: string;
    current_traditional_authority: string;
    current_village: string;
    country: string;
    landmark: string;
    cell_phone_number: string;
    occupation: string;
    marital_status: string;
    religion: string;
    education_level: string;
}

interface Patient {
    id: string; // BigInt converted to string
    patientID: string;
    message: string;
    timestamp: string; // ISO string
    createdAt: string; // ISO string
    updatedAt: string; // ISO string
    data: any; // PatientDemographics and other fields
}

interface DuplicateMatchResultResponse {
    isPossibleDuplicate: boolean;
    bestMatchScore: number;
    bestMatchPatient: Patient | null;
    potentialMatches: Array<{ patient: Patient; score: number }>;
}

// --- MODIFIED: Define the service function's ultimate return type ---
// Now it indicates the final action based on user's decision in modal
interface IsPatientPossibleDuplicateServiceResponse {
    success: boolean;
    action: "proceed" | "cancel" | "no-duplicate" | "connection-error" | "request-failed"; // More granular actions
    reason?: string;
    error?: string;
    originalInput: PatientDemographics; // Always useful to pass back
    // The duplicateResult is now consumed by the modal itself, not returned directly by this function
}

// Instantiate the modal composable ONCE at the module level
// This makes the modal control functions globally accessible for this service
const { openModal } = useModal();

/**
 * Checks for possible patient duplicates and, if found, prompts the user via a modal.
 * Returns the user's final decision ('proceed' or 'cancel').
 *
 * @param personInformation The patient's demographic data to check.
 * @returns A promise that resolves to IsPatientPossibleDuplicateServiceResponse.
 */
export const isPatientPossibleDuplicate = async (personInformation: PatientDemographics): Promise<IsPatientPossibleDuplicateServiceResponse> => {
    // 1. Connection Test
    const conn_test = await handleTestConnection();
    if (conn_test == false) {
        console.warn("No connection to backend, cannot check for duplicates.");
        return {
            success: false,
            action: "connection-error",
            reason: "No connection to backend",
            originalInput: personInformation,
        };
    }

    try {
        // It's good practice to define BASE_URL outside the function
        const BASE_URL = await getConnectonString();
        // 2. Prepare Payload
        const payload = {
            data: personInformation,
        };

        // 3. Make API Call
        const response = await fetch(`${BASE_URL}/is-patient-possible-duplicate`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(payload),
        });

        // 4. Handle HTTP Errors
        if (!response.ok) {
            const errorBody = await response.text();
            console.error(`HTTP error! status: ${response.status}, body: ${errorBody}`);
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        // 5. Parse JSON Response
        const backendResult: DuplicateMatchResultResponse = await response.json();

        console.log("Duplicate check backend response:", backendResult);

        // 6. Conditional Modal Interaction based on backendResult
        const isPossibleDuplicate = backendResult.isPossibleDuplicate;
        if (true) {
            // --- NEW: Await user decision from the modal ---
            const userDecision = await openModal({
                originalPatientInfo: personInformation,
                duplicateResult: backendResult,
            });

            console.log("User decision from modal:", userDecision);

            // 7. Return based on user's decision
            return {
                success: true,
                action: userDecision, // 'proceed' or 'cancel'
                originalInput: personInformation,
            };
        } else {
            // No duplicate detected, proceed directly
            console.log("No duplicate found, proceeding with default action.");
            return {
                success: true,
                action: "no-duplicate", // Indicate no modal interaction needed
                originalInput: personInformation,
            };
        }
    } catch (error: any) {
        console.error("Duplicate check request failed:", error);
        return {
            success: false,
            action: "request-failed",
            reason: "API request failed",
            error: error.message,
            originalInput: personInformation,
        };
    }
};


// Generic API fetch function
export const fetchApiData = async (endpoint: any, options = {} as any) => {
    const {
        method = "GET",
        headers = {},
        body = null,
        skipConnectionTest = false,
        returnFullResponse = false
    } = options;

    // Optional connection test
    if (!skipConnectionTest) {
        const conn_test = await handleTestConnection();
        if (conn_test == false) {
            return null;
        }
    }

    const BASE_URL = await getConnectonString();
    const url = `${BASE_URL}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;

    try {
        const fetchOptions = {
            method,
            headers: {
                "Content-Type": "application/json",
                ...headers,
            },
        } as any;

        if (body && (method === "POST" || method === "PUT" || method === "PATCH")) {
            fetchOptions.body = typeof body === 'string' ? body : JSON.stringify(body);
        }

        const response = await fetch(url, fetchOptions);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const jsonData = await response.json();

        // Return full response if requested
        if (returnFullResponse) {
            return jsonData;
        }

        // Handle empty arrays
        if (Array.isArray(jsonData) && jsonData.length === 0) {
            return null;
        }

        // Return data property if it exists, otherwise return the full response
        return jsonData.data || jsonData;
    } catch (error) {
        console.error(`API request to ${endpoint} failed:`, error);
        return null;
    }
};

export const getPatientRecordByID = async (patientID: any) => {
    return await fetchApiData(`/patient/${patientID}/payload`);
};

export async function updateConnectionString(data: any): Promise<void> {
    console.log("Updating connection string with data:");
    const plainData = JSON.parse(JSON.stringify(data));

    const storeName = "offlineConnectionString";
    const db = (await openDatabase(storeName, "connectin_string_id")) as any;

    return new Promise((resolve, reject) => {
        const transaction = db.transaction([storeName], "readwrite");
        const objectStore = transaction.objectStore(storeName);

        // First, clear all existing records
        const clearRequest = objectStore.clear();

        clearRequest.onsuccess = () => {
            // Then add the new record
            const addRequest = objectStore.add(plainData);

            addRequest.onsuccess = () => resolve();
            addRequest.onerror = () => reject(`Error inserting record: ${addRequest.error}`);
        };

        clearRequest.onerror = () => reject(`Error clearing store: ${clearRequest.error}`);
    });
}

export const getConnectonString = async () => {
    const connection_strings = (await getOfflineRecords("offlineConnectionString")) as any;
    return connection_strings[0].connection_string;
};

export const searchPatientFromMODS = async (searchCriteria: { given_name?: string; family_name?: string; gender?: string }) => {
    const conn_test = await handleTestConnection();
    if (conn_test == false) {
        return;
    }
    const BASE_URL = await getConnectonString();
    const params = Object.entries(searchCriteria)
        .filter(([_, value]) => value !== undefined && value !== "")
        .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
        .join("&");

    const url = `${BASE_URL}/search${params ? `?${params}` : ""}`;

    try {
        const response = await fetch(url, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const jsonData = await response.json();
        return jsonData.data;
    } catch (error) {
        console.error("Search request failed:", error);
        // throw error;
    }
};

export function getUseMODS(): boolean {
    // Get the value from localStorage
    let useMODS = localStorage.getItem("useMODS");

    // Check if it's empty or null
    if (useMODS === null || useMODS === undefined || useMODS === "") {
        // Set default value (false = Live Server mode)
        useMODS = "false";
        localStorage.setItem("useMODS", useMODS);
        return false;
    }

    // Convert string to boolean and return
    return useMODS === "true";
}

export function getStoreCachedRecords(): boolean {
    // Get the value from localStorage
    let storeCachedRecords = localStorage.getItem("storeCachedRecords");

    // Check if it's empty or null
    if (storeCachedRecords === null || storeCachedRecords === undefined || storeCachedRecords === "") {
        // Set default value (false = Live Server mode)
        storeCachedRecords = "false";
        localStorage.setItem("storeCachedRecords", storeCachedRecords);
        return false;
    }

    return storeCachedRecords === "true";
}

export const updateDemopgraphicStoreONPatientRecordChange = async () => {
    if (getUseMODS() == true) {
        const demographicsStore = useDemographicsStore();
        if (demographicsStore?.patient?.ID) {
            const patientRecord = await getOfflinePatientData(demographicsStore?.patient?.patientID);

            if (patientRecord) {
                // Convert both objects to strings
                const patientRecordString = JSON.stringify(patientRecord);
                const demographicsPatientString = JSON.stringify(demographicsStore?.patient);
                // Compare string sizes
                // console.log("Patient ID: ", demographicsStore?.patient?.patientID);
                const patientRecordSize = patientRecordString.length;
                const demographicsPatientSize = demographicsPatientString.length;
                // console.log(`Patient Record String Size: ${patientRecordSize}`);
                // console.log(`Demographics Patient String Size: ${demographicsPatientSize}`);
                if (patientRecordSize === demographicsPatientSize) {
                    console.log("String sizes match!");
                } else {
                    // console.log("String sizes do not match");

                    if (patientRecordSize > demographicsPatientSize) {
                        console.log(`Patient Record is larger by ${patientRecordSize - demographicsPatientSize} characters`);
                        demographicsStore.setPatientRecord(patientRecord);
                    } else {
                        // console.log(`Demographics Patient is larger by ${demographicsPatientSize - patientRecordSize} characters`);
                    }
                }
                // console.log("Patient Record String:", patientRecordString);
                // console.log("Demographics Patient String:", demographicsPatientString);
            }
        }
    }
};

export const getddeNPIDObectViaMODS = async () => {
    const conn_test = await handleTestConnection();
    if (conn_test == false) {
        return;
    }
    const BASE_URL = await getConnectonString();
    const url = `${BASE_URL}/unassigned-npid`;

    try {
        const response = await fetch(url, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const jsonData = await response.json();

        if (jsonData.length == 0) {
            return null;
        }

        return jsonData.data;
    } catch (error) {
        console.error("Search request failed:", error);
        return null;
    }
};

export const checkLiveAPIHealthViaMODS = async () => {
    // checks MODS is reacheble and the MODS itslef has connection to the live API (EMR)
    const conn_test = await handleTestConnection();
    if (conn_test == false) {
        return false;
    }

    const BASE_URL = await getConnectonString();
    const url = `${BASE_URL}/live-api-health`;

    try {
        const response = await fetch(url, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const jsonData = await response.json();
        return jsonData.data;
    } catch (error) {
        //for debuging purposes include the error in console
        console.error("Live API health check failed:", "error");
        return false;
    }
};

export const handleTestConnection = async () => {
    const CONNECTION_TIMEOUT = 5000; // 5 seconds timeout
    const LAST_TEST_KEY = "network_last_test_result";
    const BASE_URL = await getConnectonString();
    const url = `${BASE_URL}/test-connection`;

    function extractHostPort(url: any) {
        const parsedUrl = new URL(url);
        return parsedUrl.host;
    }

    try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), CONNECTION_TIMEOUT);

        const response = await fetch(url, {
            method: "GET",
            headers: {
                Accept: "application/json",
            },
            signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        const connectionStatus = data.connection_status;
        //   connectionTimestamp.value = new Date(data.timestamp).toLocaleString()

        if (data.connection_status === "available") {
            // Save successful test results
            localStorage.setItem(
                LAST_TEST_KEY,
                JSON.stringify({
                    status: data.connection_status,
                    timestamp: data.timestamp,
                    connectionString: extractHostPort(url),
                })
            );

            // toastSuccess('Connection successful!')
            return true;
        } else {
            toastWarning("Connection test completed but MODS server is not available");
            return false;
        }
    } catch (error: any) {
        console.error("connection test string_: ", url);
        console.error("Connection test failed:", error);

        if (error.name === "AbortError") {
            toastDanger("Connection timed out. Please check your network settings for MODS.");
        } else {
            toastDanger("Connection test failed. Please check your network settings for MODS.");
        }

        return false;
    }
};

export const submitPayloadToExternalServiceViaMODS = async (payload: any) => {
    try {
        const BASE_URL = await getConnectonString();
        const conn_test = await handleTestConnection();
        if (conn_test == false) {
            return;
        }

        const response = await fetch(`${BASE_URL}/receive-payload`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(payload),
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        const demographicsStore = useDemographicsStore();
        demographicsStore.setRecord(result[0].record);

        pre_process(result);

        // const workerStore = useWorkerStore();
        // await workerStore.postData("ADD_OBJECT_STORE", { storeName: "patientRecords", data: result[0].record });
    } catch (error) {
        console.error("Error submitting payload:", error);
        // throw error;
    }
};

const pre_process = (payload: any) => {
    // Process the batch response
    for (const record of payload) {
        if (record.hasChanges === true) {
            const parsedRecord = record.record;
            if (record?.id_to_remove) {
                const workerStore = useWorkerStore();

                workerStore.postData("DELETE_RECORD", {
                    storeName: "patientRecords",
                    whereClause: { patientID: record?.id_to_remove },
                });

                workerStore.postData("DELETE_RECORD", {
                    storeName: "patientRecords",
                    whereClause: { patientID: record?.id_to_remove2 },
                });
                self.postMessage({ message: "update_stale_record", payload: parsedRecord, IDTR: record?.id_to_remove });
            }

            if (record.id_to_update == null) {
                self.postMessage({
                    message: "update_stale_record",
                    payload: record?.record,
                    IDTR: record?.record?.patientID,
                    update: true,
                });
            }
        }
    }
};

export const getSessionDateViaMods = async () => {
    try {
        const BASE_URL = await getConnectonString();
        const conn_test = await handleTestConnection();
        if (conn_test == false) {
            return;
        }

        const response = await fetch(`${BASE_URL}/session-date-time`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        return result.data.storedData.date;
    } catch (error) {
        console.error("failed to fetch session date via MODS: ", error);
        return null;
    }
};
