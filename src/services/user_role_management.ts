import { Service } from "./service";

export function isNCDClerk() {
    const roleData: any = JSON.parse(localStorage.getItem("userRoles") as string);
    const roles: any = roleData ? roleData : [];
    if (
        roles.some(
            (role: any) =>
                role.role === "General Registration Clerk" ||
                roles.some((role: any) => role.role === "Vitals Clerk" || roles.some((role: any) => role.role === "Registration Clerk"))
        ) &&
        Service.getProgramID() == 32
    ) {
        return true;
    } else {
        return false;
    }
}
