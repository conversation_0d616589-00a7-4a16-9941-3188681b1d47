{"name": "MaHIS", "private": true, "version": "1.0.22", "type": "module", "main": "electron/main.cjs", "author": {"name": "Luke International", "email": "<EMAIL>"}, "scripts": {"dev": "vite --host", "build": "vue-tsc && vite build", "android:test": "vite build --mode test && rm -rf android/app/src/main/assets/public/* && cp -r dist/* android/app/src/main/assets/public", "android": "ionic cap build android", "build:test": "vite build --mode test", "build:development": "vite build --mode development", "build:prod": "vite build --mode production", "preview": "vite preview", "test:e2e": "cypress run", "test:unit": "vitest", "lint": "eslint", "electron:dev": "NODE_ENV=development electron electron/main.cjs", "electron:build": "vite build && NODE_ENV=production electron-builder"}, "build": {"appId": "gov.mw.mahis", "productName": "MaHIS", "files": ["dist/**/*", "electron/**/*"], "directories": {"output": "dist_electron"}, "extraResources": [{"from": "electron", "to": "app"}], "mac": {"target": ["dmg", "zip"], "category": "public.app-category.utilities"}, "win": {"target": "nsis"}, "linux": {"target": ["AppImage", "deb"], "category": "Utility"}}, "dependencies": {"@awesome-cordova-plugins/bluetooth-serial": "^6.4.0", "@awesome-cordova-plugins/core": "^6.16.0", "@capacitor-mlkit/barcode-scanning": "^6.1.0", "@capacitor/android": "^6.1.2", "@capacitor/app": "^6.0.1", "@capacitor/core": "^6.1.2", "@capacitor/filesystem": "^6.0.1", "@capacitor/haptics": "6.0.1", "@capacitor/keyboard": "6.0.2", "@capacitor/status-bar": "6.0.1", "@ionic-native/core": "^5.36.0", "@ionic-native/pdf-generator": "^5.36.0", "@ionic/vue": "^7.6.1", "@ionic/vue-router": "^7.7.2", "@popperjs/core": "^2.11.8", "@preact/signals": "^2.0.4", "@schedule-x/calendar": "^2.29.0", "@schedule-x/theme-default": "^2.4.0", "@schedule-x/vue": "^2.3.0", "@types/crypto-js": "^4.2.2", "@uniquedj95/vtable": "^1.9.1", "@vue/compiler-sfc": "^3.4.31", "@vueform/toggle": "^2.1.4", "@vuepic/vue-datepicker": "^8.1.0", "apexcharts": "^4.0.0", "bootstrap": "^5.3.3", "cap-label-printer-plugin": "^2.2.0", "capacitor-blob-writer": "^1.1.14", "chart.js": "^4.4.2", "datatables.net": "^2.1.8", "datatables.net-bs4": "^2.1.8", "datatables.net-bs5": "^2.0.8", "datatables.net-buttons": "^3.1.2", "datatables.net-buttons-bs4": "^3.1.2", "datatables.net-buttons-dt": "^3.0.2", "datatables.net-dt": "^2.0.8", "datatables.net-responsive-dt": "^3.0.2", "datatables.net-select": "^2.0.3", "datatables.net-select-bs5": "^2.0.3", "datatables.net-select-dt": "^2.0.3", "datatables.net-vue3": "^3.0.2", "date-fns": "^2.30.0", "dom-to-image-more": "^3.6.0", "emr-api-client": "^1.1.0", "express": "^4.21.2", "fast-sort": "^3.4.0", "floating-vue": "^5.2.2", "html2canvas": "^1.4.1", "ionicons": "^7.4.0", "jsbarcode": "^3.12.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jszip": "^3.10.1", "k-progress-v3": "^1.0.0", "nprogress": "^0.2.0", "pdfmake": "^0.2.14", "pickerjs": "^1.2.1", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.3", "preact": "^10.26.5", "qrcode": "^1.5.4", "rxjs": "^7.8.2", "socket.io-client": "^4.8.1", "swrv": "^1.0.4", "tiny-emitter": "^2.1.0", "vue": "^3.5.13", "vue-awesome-paginate": "^1.2.0", "vue-ellipse-progress": "^2.2.0", "vue-multiselect": "^3.0.0-beta.3", "vue-qrcode-reader": "^5.5.6", "vue-router": "^4.1.6", "vue-select": "^4.0.0-beta.6", "vue-tel-input": "^9.1.4", "vue3-apexcharts": "^1.8.0", "vue3-barcode-qrcode-reader": "^1.0.16", "vue3-carousel": "^0.3.3", "vue3-toastify": "^0.2.2", "yup": "^1.4.0"}, "devDependencies": {"@capacitor/cli": "^5.7.5", "@eslint/js": "^9.21.0", "@types/lodash": "^4.14.202", "@types/nprogress": "^0.2.3", "@types/vue-select": "^2.5.0", "@types/vue-tel-input": "^2.1.1", "@vitejs/plugin-basic-ssl": "^1.1.0", "@vitejs/plugin-legacy": "^6.1.1", "@vitejs/plugin-vue": "^5.2.4", "@vue/compiler-sfc": "^3.5.13", "@vue/eslint-config-typescript": "^14.4.0", "@vue/test-utils": "^2.3.0", "cypress": "^13.14.1", "electron": "^34.0.2", "electron-builder": "^25.1.8", "eslint": "^9.21.0", "eslint-plugin-vue": "^9.32.0", "globals": "^16.0.0", "jsdom": "^22.1.0", "rollup-plugin-visualizer": "^5.14.0", "sass-embedded": "^1.89.2", "terser": "^5.39.2", "typescript": "^5.7.3", "typescript-eslint": "^8.25.0", "vite": "^6.3.5", "vitest": "^3.1.4", "vue-tsc": "^2.0.22"}, "description": "An Ionic project"}