{"[dart]": {"editor.formatOnSave": true, "editor.formatOnType": true, "editor.selectionHighlight": false, "editor.suggest.snippetsPreventQuickSuggestions": false, "editor.suggestSelection": "first", "editor.tabCompletion": "onlySnippets", "editor.wordBasedSuggestions": "off"}, "[ruby]": {"editor.defaultFormatter": "misogi.ruby-rubocop"}, "editor.suggestSelection": "first", "vsintellicode.modify.editor.suggestSelection": "automaticallyOverrodeDefaultValue", "files.exclude": {"**/.classpath": true, "**/.project": true, "**/.settings": true, "**/.factorypath": true}, "bracket-pair-colorizer-2.depreciation-notice": false, "bracketPairColorizer.depreciation-notice": false, "terminal.integrated.enableMultiLinePasteWarning": false, "[python]": {"editor.formatOnType": true}, "tabnine.experimentalAutoImports": true, "editor.formatOnPaste": true, "editor.formatOnType": true, "[json]": {"editor.defaultFormatter": "vscode.json-language-features"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.formatOnSave": true, "prettier.printWidth": 150, "prettier.tabWidth": 4, "cloudcode.autoDependencies": "off", "remote.autoForwardPortsSource": "hybrid", "gitlens.gitCommands.skipConfirmations": ["fetch:command", "stash-push:command", "switch:command"], "terminal.external.windowsExec": "C:\\Program Files\\Git\\git-bash.exe", "terminal.integrated.defaultProfile.windows": "<PERSON><PERSON>"}